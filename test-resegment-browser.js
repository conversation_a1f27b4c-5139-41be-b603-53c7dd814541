const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  const context = await browser.newContext();
  const page = await context.newPage();

  // Listen for console messages
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log('Browser console error:', msg.text());
    }
  });

  // Listen for page errors
  page.on('pageerror', error => {
    console.log('Page error:', error.message);
  });

  try {
    // Navigate to the app
    console.log('Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('Page loaded');

    // Check for any JavaScript errors
    const title = await page.title();
    console.log('Page title:', title);

    // Login
    console.log('Logging in...');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for navigation
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('Logged in successfully');

    // Navigate to the test project
    await page.waitForTimeout(2000);
    const projectCards = await page.$$('[data-testid="project-card"]');
    console.log('Found', projectCards.length, 'projects');

    if (projectCards.length > 0) {
      // Click on the first project
      await projectCards[0].click();
      await page.waitForURL('**/projects/**');
      console.log('Opened project details');

      // Check for resegment buttons
      await page.waitForTimeout(2000);
      const resegmentButtons = await page.$$('[aria-label*="Resegment"], [title*="Resegment"], button:has(svg.lucide-refresh-ccw)');
      console.log('Found', resegmentButtons.length, 'resegment buttons');
    }

  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
    console.log('Browser closed');
  }
})();