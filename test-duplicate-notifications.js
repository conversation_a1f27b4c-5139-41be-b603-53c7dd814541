#!/usr/bin/env node
/**
 * Test script to verify duplicate notification fixes
 * This script simulates segmentation completion events to check if notifications are deduplicated
 */

const io = require('socket.io-client');

// Configuration
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5001';
const PROJECT_ID = process.env.PROJECT_ID || 'test-project';
const IMAGE_ID = process.env.IMAGE_ID || 'test-image-' + Date.now();

console.log('Testing duplicate notification fixes...');
console.log('Backend URL:', BACKEND_URL);
console.log('Project ID:', PROJECT_ID);
console.log('Image ID:', IMAGE_ID);

// Connect to Socket.IO
const socket = io(BACKEND_URL, {
  transports: ['websocket'],
  reconnection: true,
});

socket.on('connect', () => {
  console.log('Connected to backend WebSocket');
  
  // Join the project room
  socket.emit('join_project', { projectId: PROJECT_ID });
});

socket.on('joined_project', (data) => {
  console.log('Joined project room:', data);
  
  // Simulate multiple segmentation_update events
  console.log('\nSimulating multiple segmentation completion events...');
  
  // Emit 3 completion events for the same image within a short time window
  const eventData = {
    imageId: IMAGE_ID,
    status: 'completed',
    resultPath: '/path/to/result.json',
    timestamp: new Date().toISOString()
  };
  
  console.log('Emitting event 1...');
  socket.emit('test_segmentation_update', eventData);
  
  setTimeout(() => {
    console.log('Emitting event 2 (should be deduplicated)...');
    socket.emit('test_segmentation_update', eventData);
  }, 500);
  
  setTimeout(() => {
    console.log('Emitting event 3 (should be deduplicated)...');
    socket.emit('test_segmentation_update', eventData);
  }, 1000);
  
  // After 3 seconds, emit a new event (should show notification)
  setTimeout(() => {
    console.log('\nEmitting new event after dedupe window...');
    const newEventData = {
      ...eventData,
      imageId: IMAGE_ID + '-new',
      timestamp: new Date().toISOString()
    };
    socket.emit('test_segmentation_update', newEventData);
  }, 3000);
  
  // Disconnect after 5 seconds
  setTimeout(() => {
    console.log('\nTest completed. Check the frontend for notifications.');
    console.log('Expected: 2 notifications (one for first event, one for new event after window)');
    socket.disconnect();
    process.exit(0);
  }, 5000);
});

socket.on('segmentation_update', (data) => {
  console.log('Received segmentation_update:', data);
});

socket.on('error', (error) => {
  console.error('Socket error:', error);
});

socket.on('disconnect', (reason) => {
  console.log('Disconnected:', reason);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nShutting down...');
  socket.disconnect();
  process.exit(0);
});