#!/usr/bin/env python3
"""
Test script for ML service horizontal scaling
Tests load distribution, failover, and performance with multiple ML instances
"""

import requests
import json
import time
import threading
import statistics
from collections import defaultdict
from datetime import datetime
import sys
import os

# Configuration
BACKEND_URL = os.environ.get('BACKEND_URL', 'http://localhost:5001')
ML_LB_URL = os.environ.get('ML_LB_URL', 'http://localhost:5003')
HAPROXY_STATS_URL = os.environ.get('HAPROXY_STATS_URL', '****************************************/stats')
NUM_TEST_TASKS = int(os.environ.get('NUM_TEST_TASKS', '20'))
CONCURRENT_REQUESTS = int(os.environ.get('CONCURRENT_REQUESTS', '5'))

# Test results storage
results = {
    'health_checks': [],
    'processing_times': [],
    'errors': [],
    'instance_distribution': defaultdict(int),
    'success_count': 0,
    'failure_count': 0
}
lock = threading.Lock()

def check_ml_health():
    """Check health of ML service through load balancer"""
    try:
        response = requests.get(f"{ML_LB_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return {
                'status': 'healthy',
                'instance': data.get('instance_id', 'unknown'),
                'active_tasks': data.get('processing', {}).get('active_tasks', 0),
                'timestamp': datetime.now().isoformat()
            }
        else:
            return {
                'status': 'unhealthy',
                'error': f'Status code: {response.status_code}',
                'timestamp': datetime.now().isoformat()
            }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

def get_haproxy_stats():
    """Get HAProxy statistics"""
    try:
        response = requests.get(HAPROXY_STATS_URL + '?stats;csv', timeout=5)
        if response.status_code == 200:
            lines = response.text.strip().split('\n')
            headers = lines[0].split(',')
            stats = []
            for line in lines[1:]:
                if 'ml_backend,ml' in line:
                    values = line.split(',')
                    server_stats = dict(zip(headers, values))
                    stats.append({
                        'server': server_stats.get('svname', 'unknown'),
                        'status': server_stats.get('status', 'unknown'),
                        'active_sessions': server_stats.get('scur', '0'),
                        'total_sessions': server_stats.get('stot', '0'),
                        'bytes_in': server_stats.get('bin', '0'),
                        'bytes_out': server_stats.get('bout', '0'),
                        'errors': server_stats.get('eresp', '0')
                    })
            return stats
        return None
    except Exception as e:
        print(f"Error getting HAProxy stats: {e}")
        return None

def simulate_task_processing(task_id):
    """Simulate a segmentation task"""
    start_time = time.time()
    try:
        # Create a mock task
        task = {
            'taskId': f'test-{task_id}',
            'imageId': f'image-{task_id}',
            'imagePath': f'/test/image-{task_id}.jpg',
            'parameters': {
                'test': True,
                'task_id': task_id
            },
            'callbackUrl': f'{BACKEND_URL}/api/test/callback/{task_id}'
        }
        
        # In a real test, you would submit this to RabbitMQ
        # For now, we'll test the health endpoint multiple times
        # to simulate load and see distribution
        
        # Make multiple health checks to see load balancing
        for i in range(3):
            health = check_ml_health()
            if health['status'] == 'healthy':
                instance = health.get('instance', 'unknown')
                with lock:
                    results['instance_distribution'][instance] += 1
            time.sleep(0.1)
        
        # Simulate processing time
        processing_time = time.time() - start_time
        
        with lock:
            results['processing_times'].append(processing_time)
            results['success_count'] += 1
            
        return True
        
    except Exception as e:
        with lock:
            results['errors'].append({
                'task_id': task_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            results['failure_count'] += 1
        return False

def run_load_test():
    """Run concurrent load test"""
    print(f"\n🚀 Starting ML Service Scaling Test")
    print(f"   - Testing with {NUM_TEST_TASKS} tasks")
    print(f"   - Concurrent requests: {CONCURRENT_REQUESTS}")
    print(f"   - ML Load Balancer: {ML_LB_URL}")
    print("-" * 50)
    
    # Initial health check
    print("\n📊 Initial Health Check:")
    for i in range(3):
        health = check_ml_health()
        print(f"   Attempt {i+1}: {health}")
        results['health_checks'].append(health)
        time.sleep(1)
    
    # Check HAProxy stats
    print("\n📈 HAProxy Statistics:")
    stats = get_haproxy_stats()
    if stats:
        for server in stats:
            print(f"   - {server['server']}: Status={server['status']}, "
                  f"Sessions={server['active_sessions']}/{server['total_sessions']}")
    else:
        print("   Unable to retrieve HAProxy stats")
    
    # Run load test
    print(f"\n🔄 Running load test with {NUM_TEST_TASKS} tasks...")
    start_time = time.time()
    
    # Create threads for concurrent processing
    threads = []
    for i in range(NUM_TEST_TASKS):
        if len(threads) >= CONCURRENT_REQUESTS:
            # Wait for a thread to complete
            threads[0].join()
            threads.pop(0)
        
        thread = threading.Thread(target=simulate_task_processing, args=(i,))
        thread.start()
        threads.append(thread)
        
        # Small delay between task submissions
        time.sleep(0.1)
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    total_time = time.time() - start_time
    
    # Final statistics
    print("\n✅ Load Test Complete!")
    print("-" * 50)
    print(f"📊 Test Results:")
    print(f"   - Total tasks: {NUM_TEST_TASKS}")
    print(f"   - Successful: {results['success_count']}")
    print(f"   - Failed: {results['failure_count']}")
    print(f"   - Total time: {total_time:.2f}s")
    print(f"   - Tasks per second: {NUM_TEST_TASKS / total_time:.2f}")
    
    if results['processing_times']:
        print(f"\n⏱️  Processing Times:")
        print(f"   - Average: {statistics.mean(results['processing_times']):.3f}s")
        print(f"   - Median: {statistics.median(results['processing_times']):.3f}s")
        print(f"   - Min: {min(results['processing_times']):.3f}s")
        print(f"   - Max: {max(results['processing_times']):.3f}s")
    
    print(f"\n🎯 Load Distribution:")
    total_requests = sum(results['instance_distribution'].values())
    for instance, count in sorted(results['instance_distribution'].items()):
        percentage = (count / total_requests * 100) if total_requests > 0 else 0
        print(f"   - {instance}: {count} requests ({percentage:.1f}%)")
    
    if results['errors']:
        print(f"\n❌ Errors ({len(results['errors'])}):")
        for error in results['errors'][:5]:  # Show first 5 errors
            print(f"   - Task {error['task_id']}: {error['error']}")
    
    # Final HAProxy stats
    print("\n📈 Final HAProxy Statistics:")
    stats = get_haproxy_stats()
    if stats:
        for server in stats:
            print(f"   - {server['server']}: Status={server['status']}, "
                  f"Total Sessions={server['total_sessions']}, "
                  f"Errors={server['errors']}")
    
    # Test verdict
    print("\n🏁 Test Verdict:")
    if results['failure_count'] == 0 and len(results['instance_distribution']) > 1:
        print("   ✅ PASSED - Load balancing is working correctly!")
    elif results['failure_count'] > 0:
        print("   ❌ FAILED - Some tasks failed during processing")
    elif len(results['instance_distribution']) <= 1:
        print("   ⚠️  WARNING - Load not distributed across multiple instances")
    else:
        print("   ✅ PASSED with warnings")
    
    return results['failure_count'] == 0

if __name__ == "__main__":
    success = run_load_test()
    sys.exit(0 if success else 1)