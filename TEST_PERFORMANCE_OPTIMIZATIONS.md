# Performance Optimizations Test Plan

## Overview
This document outlines the testing strategy to verify all implemented performance optimizations are working correctly.

## Test Categories

### 1. Database Query Optimization Tests

#### Test: User Stats Service Performance
```bash
# Create test script to measure query performance
cd packages/backend
npm run test -- src/services/__tests__/userStatsServiceOptimized.test.ts
```

Expected Results:
- Query count reduced from 15+ to 2-3
- Response time < 100ms for user stats
- Proper data aggregation without N+1 issues

#### Test: Database Indexes
```sql
-- Verify indexes exist
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname IN (
  'idx_images_user_project',
  'idx_images_status',
  'idx_cells_image',
  'idx_segmentation_image'
);
```

### 2. React Component Optimization Tests

#### Test: React.memo Implementation
```typescript
// Verify memo wrappers are applied
grep -r "memo(" packages/frontend/src/components/
```

#### Test: Re-render Prevention
1. Open React DevTools Profiler
2. Navigate to project page with many images
3. Interact with single image
4. Verify only that image component re-renders

### 3. Redis Caching Tests

#### Test: Cache Hit Rates
```bash
# Monitor Redis cache hits
docker-compose exec redis redis-cli
> INFO stats
> MONITOR  # Watch cache operations in real-time
```

#### Test: API Response Times
```bash
# First request (cache miss)
time curl -H "Authorization: Bearer $TOKEN" http://localhost:5001/api/user/stats

# Second request (cache hit) - should be much faster
time curl -H "Authorization: Bearer $TOKEN" http://localhost:5001/api/user/stats
```

### 4. Async File Operations Tests

#### Test: Concurrent Upload Handling
```bash
# Test concurrent file uploads
for i in {1..10}; do
  curl -X POST -F "file=@test-image.jpg" \
    -H "Authorization: Bearer $TOKEN" \
    http://localhost:5001/api/images/upload &
done
wait
```

Expected: Server remains responsive during uploads

### 5. Virtual Scrolling Tests

#### Test: Large Dataset Rendering
1. Create project with 1000+ images
2. Open project view
3. Monitor memory usage in Chrome DevTools
4. Verify smooth scrolling performance

Expected:
- DOM nodes < 100 (only visible items rendered)
- Memory usage stable while scrolling
- No lag or jank during scroll

### 6. HTTP Caching Tests

#### Test: Static Asset Caching
```bash
# Check cache headers
curl -I http://localhost:3000/assets/logo.png
# Should see: Cache-Control: public, max-age=604800

# Test conditional requests
curl -I -H "If-None-Match: \"abc123\"" http://localhost:3000/assets/logo.png
# Should return 304 if ETag matches
```

#### Test: API Response Caching
```bash
# Check API cache headers
curl -I -H "Authorization: Bearer $TOKEN" \
  http://localhost:5001/api/projects
# Should see appropriate Cache-Control headers
```

### 7. Request Deduplication Tests

#### Test: Duplicate Request Prevention
```javascript
// In browser console
// Trigger multiple identical requests rapidly
for(let i = 0; i < 5; i++) {
  fetch('/api/projects').then(r => console.log('Response', i));
}
// Check Network tab - should see only 1 actual request
```

### 8. Performance Monitoring Tests

#### Test: Metrics Collection
```bash
# View performance metrics
curl http://localhost:5001/api/performance/metrics
```

Expected output includes:
- API response times by endpoint
- Slow query logs
- Memory usage trends
- Database connection stats

### 9. Load Testing

#### Setup
```bash
# Install artillery for load testing
npm install -g artillery
```

#### Test: API Load Test
```yaml
# load-test.yml
config:
  target: "http://localhost:5001"
  phases:
    - duration: 60
      arrivalRate: 10
      rampTo: 50
  defaults:
    headers:
      Authorization: "Bearer YOUR_TOKEN"

scenarios:
  - name: "User Stats"
    flow:
      - get:
          url: "/api/user/stats"
      - think: 5
      - get:
          url: "/api/projects"
      - think: 5
      - get:
          url: "/api/images?projectId=1"
```

Run: `artillery run load-test.yml`

Expected:
- p95 response time < 500ms
- 0% error rate
- Stable memory usage

## Automated Test Suite

Create automated test script:

```bash
#!/bin/bash
# test-performance.sh

echo "🧪 Testing Performance Optimizations..."

# 1. Backend Tests
echo "📊 Running backend performance tests..."
cd packages/backend
npm run test -- --testPathPattern="performance|optimized" --verbose

# 2. Database Tests  
echo "🗄️ Checking database optimizations..."
docker-compose exec -T db psql -U postgres -d spheroseg << EOF
EXPLAIN ANALYZE
SELECT COUNT(*) FROM images WHERE user_id = 1;
EOF

# 3. Redis Tests
echo "📦 Testing Redis cache..."
docker-compose exec -T redis redis-cli ping

# 4. Frontend Build
echo "🎨 Building optimized frontend..."
cd ../frontend
npm run build
du -sh dist/  # Check bundle size

# 5. Performance Metrics
echo "📈 Fetching performance metrics..."
curl -s http://localhost:5001/api/performance/metrics | jq .

echo "✅ Performance tests completed!"
```

## Manual Verification Checklist

- [ ] User stats load in < 100ms
- [ ] Project list with 100+ items scrolls smoothly  
- [ ] Image grid with 1000+ items uses < 200MB memory
- [ ] Static assets return 304 on refresh
- [ ] API requests are deduplicated in Network tab
- [ ] Redis cache is being utilized (check logs)
- [ ] No console errors or warnings
- [ ] Performance monitoring dashboard shows metrics
- [ ] Database queries execute in < 100ms
- [ ] File uploads don't block the server

## Performance Benchmarks

### Before Optimizations
- User Stats Query: ~500ms (15+ queries)
- Project List Load: ~1.2s
- Image Grid Render (1000): ~3s, 500MB memory
- API Response Avg: ~250ms

### After Optimizations (Target)
- User Stats Query: < 100ms (2-3 queries)
- Project List Load: < 400ms
- Image Grid Render (1000): < 200ms, 120MB memory
- API Response Avg: < 100ms

## Monitoring Commands

```bash
# Watch backend logs
docker-compose logs -f backend

# Monitor database queries
docker-compose exec db psql -U postgres -d spheroseg -c "SELECT query, mean_exec_time FROM pg_stat_statements ORDER BY mean_exec_time DESC LIMIT 10;"

# Check Redis stats
docker-compose exec redis redis-cli --stat

# Monitor Node.js memory
docker-compose exec backend-dev node -e "setInterval(() => console.log(process.memoryUsage()), 5000)"
```

## Troubleshooting

### Issue: Slow API responses
1. Check performance metrics endpoint
2. Look for slow queries in logs
3. Verify Redis is running
4. Check for N+1 queries

### Issue: High memory usage
1. Verify virtual scrolling is active
2. Check for memory leaks in performance monitor
3. Ensure React.memo is preventing re-renders

### Issue: Cache not working
1. Check cache headers in Network tab
2. Verify Redis connection
3. Check for Cache-Control overrides

## Success Criteria

✅ All automated tests pass
✅ Performance benchmarks met or exceeded
✅ No regression in functionality
✅ Monitoring shows stable performance
✅ User experience is noticeably improved