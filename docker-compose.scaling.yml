# Docker Compose Scaling Configuration for ML Service
# Use with: docker-compose -f docker-compose.yml -f docker-compose.scaling.yml up
version: '3.8'

services:
  # ML Service with scaling capabilities
  ml:
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2048M
          cpus: '2.0'
        reservations:
          memory: 1024M
          cpus: '1.0'
    environment:
      - RABBITMQ_PREFETCH_COUNT=4
      - WORKER_NAME=ml-worker-${HOSTNAME:-default}
      - MAX_CONCURRENT_TASKS=4
      - HEALTH_CHECK_INTERVAL=30
    labels:
      - "com.spheroseg.service=ml"
      - "com.spheroseg.lb.backend=ml"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # HAProxy Load Balancer for ML Services
  ml-load-balancer:
    image: haproxy:2.8-alpine
    container_name: spheroseg_ml_lb
    depends_on:
      - ml
    volumes:
      - ./haproxy:/usr/local/etc/haproxy:ro
    networks:
      - spheroseg-network
    ports:
      - "5003:5003"  # ML Load Balancer port
      - "8404:8404"  # HAProxy stats page
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    healthcheck:
      test: ["CMD", "haproxy", "-c", "-f", "/usr/local/etc/haproxy/haproxy.cfg"]
      interval: 30s
      timeout: 5s
      retries: 3

  # Update backend to use ML load balancer
  backend:
    environment:
      - ML_SERVICE_URL=http://ml-load-balancer:5003
      - ENABLE_ML_LOAD_BALANCING=true
      - ML_SERVICE_HEALTH_CHECK_INTERVAL=30000

  # RabbitMQ configuration for multiple consumers
  rabbitmq:
    environment:
      - RABBITMQ_DEFAULT_PREFETCH_COUNT=4
      - RABBITMQ_CONSUMER_TIMEOUT=3600000  # 1 hour timeout
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Prometheus for monitoring (preparation for later task)
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: spheroseg_prometheus
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - spheroseg-network
    ports:
      - "9090:9090"
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 512M

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: spheroseg_node_exporter
    networks:
      - spheroseg-network
    ports:
      - "9100:9100"
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 128M

volumes:
  prometheus_data:
    driver: local

networks:
  spheroseg-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450  # Optimize for cloud environments