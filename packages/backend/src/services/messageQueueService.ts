import amqp from 'amqplib';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import config from '../config';

interface SegmentationTask {
  imageId: string;
  imagePath: string;
  userId: string;
  priority?: number;
}

interface TaskResult {
  imageId: string;
  taskId: string;
  status: 'completed' | 'failed';
  segmentationData?: any;
  error?: string;
  processingTime?: number;
}

interface QueueMetrics {
  connected: boolean;
  queues: Record<string, { messages: number; consumers: number }>;
  publishedCount: number;
  consumedCount: number;
  errorCount: number;
}

interface DeadLetterMetadata {
  retryCount: number;
  originalQueue: string;
  reason: string;
  timestamp: Date;
}

export class MessageQueueService {
  private connection: amqp.Connection | null = null;
  private channel: amqp.Channel | null = null;
  private readonly url: string;
  private publishedCount = 0;
  private consumedCount = 0;
  private errorCount = 0;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;
  private readonly reconnectDelay = 5000;

  constructor(url?: string) {
    this.url = url || config.rabbitmq?.url || 'amqp://localhost';
  }

  async connect(): Promise<void> {
    try {
      this.connection = await this.connectWithRetry();
      this.channel = await this.connection.createChannel();

      // Setup error handlers
      this.connection.on('error', (err) => {
        logger.error('RabbitMQ connection error:', err);
        this.handleConnectionError();
      });

      this.connection.on('close', () => {
        logger.warn('RabbitMQ connection closed');
        this.handleConnectionError();
      });

      logger.info('Connected to RabbitMQ');
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ:', error);
      throw error;
    }
  }

  private async connectWithRetry(): Promise<amqp.Connection> {
    while (this.reconnectAttempts < this.maxReconnectAttempts) {
      try {
        const connection = await amqp.connect(this.url);
        this.reconnectAttempts = 0;
        return connection;
      } catch (error) {
        this.reconnectAttempts++;
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          throw error;
        }
        logger.warn(
          `RabbitMQ connection attempt ${this.reconnectAttempts} failed, retrying in ${this.reconnectDelay}ms...`
        );
        await new Promise((resolve) => setTimeout(resolve, this.reconnectDelay));
      }
    }
    throw new Error('Max reconnection attempts reached');
  }

  private async handleConnectionError(): Promise<void> {
    this.connection = null;
    this.channel = null;

    // Attempt to reconnect
    setTimeout(async () => {
      try {
        await this.connect();
        await this.setupQueues();
        logger.info('Successfully reconnected to RabbitMQ');
      } catch (error) {
        logger.error('Failed to reconnect to RabbitMQ:', error);
      }
    }, this.reconnectDelay);
  }

  async setupQueues(): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    // Create exchanges
    await this.channel.assertExchange('segmentation', 'topic', {
      durable: true,
    });

    await this.channel.assertExchange('segmentation.dlx', 'topic', {
      durable: true,
    });

    // Create queues with dead letter exchange
    await this.channel.assertQueue('segmentation.tasks', {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': 'segmentation.dlx',
        'x-message-ttl': 3600000, // 1 hour TTL
        'x-max-retries': 3,
      },
    });

    await this.channel.assertQueue('segmentation.results', {
      durable: true,
    });

    await this.channel.assertQueue('segmentation.dlq', {
      durable: true,
      arguments: {
        'x-message-ttl': 86400000, // 24 hour TTL for dead letters
      },
    });

    // Bind queues to exchanges
    await this.channel.bindQueue(
      'segmentation.tasks',
      'segmentation',
      'task.new'
    );

    await this.channel.bindQueue(
      'segmentation.results',
      'segmentation',
      'task.completed'
    );

    await this.channel.bindQueue(
      'segmentation.results',
      'segmentation',
      'task.failed'
    );

    await this.channel.bindQueue(
      'segmentation.dlq',
      'segmentation.dlx',
      '#'
    );

    logger.info('RabbitMQ queues and exchanges configured');
  }

  async publishSegmentationTask(task: SegmentationTask): Promise<boolean> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    const messageId = uuidv4();
    const message = {
      ...task,
      timestamp: Date.now(),
      messageId,
    };

    const success = this.channel.publish(
      'segmentation',
      'task.new',
      Buffer.from(JSON.stringify(message)),
      {
        persistent: true,
        contentType: 'application/json',
        timestamp: Date.now(),
        messageId,
        priority: task.priority || 1,
        headers: {
          userId: task.userId,
        },
      }
    );

    if (success) {
      this.publishedCount++;
      logger.info(`Published segmentation task for image ${task.imageId}`);
      return true;
    } else {
      this.errorCount++;
      throw new Error('Failed to publish message - channel buffer full');
    }
  }

  async publishTaskResult(result: TaskResult): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    const routingKey = result.status === 'completed' 
      ? 'task.completed' 
      : 'task.failed';

    const success = this.channel.publish(
      'segmentation',
      routingKey,
      Buffer.from(JSON.stringify(result)),
      {
        persistent: true,
        contentType: 'application/json',
        timestamp: Date.now(),
        messageId: uuidv4(),
      }
    );

    if (success) {
      this.publishedCount++;
      logger.info(`Published task result for image ${result.imageId}`);
    } else {
      this.errorCount++;
      throw new Error('Failed to publish result');
    }
  }

  async consumeTasks(
    handler: (task: SegmentationTask) => Promise<boolean>
  ): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    // Set prefetch to process one message at a time
    await this.channel.prefetch(1);

    await this.channel.consume(
      'segmentation.tasks',
      async (message) => {
        if (!message) return;

        try {
          const task = JSON.parse(message.content.toString());
          logger.info(`Processing task for image ${task.imageId}`);

          const success = await handler(task);

          if (success) {
            this.channel!.ack(message);
            this.consumedCount++;
            logger.info(`Successfully processed task for image ${task.imageId}`);
          } else {
            throw new Error('Handler returned false');
          }
        } catch (error) {
          logger.error('Error processing message:', error);
          this.errorCount++;
          
          // Send to dead letter queue (don't requeue)
          this.channel!.nack(message, false, false);
        }
      },
      { noAck: false }
    );

    logger.info('Started consuming segmentation tasks');
  }

  async consumeResults(
    handler: (result: TaskResult) => Promise<void>
  ): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    await this.channel.consume(
      'segmentation.results',
      async (message) => {
        if (!message) return;

        try {
          const result = JSON.parse(message.content.toString());
          await handler(result);
          this.channel!.ack(message);
          this.consumedCount++;
        } catch (error) {
          logger.error('Error processing result:', error);
          this.errorCount++;
          this.channel!.nack(message, false, false);
        }
      },
      { noAck: false }
    );

    logger.info('Started consuming segmentation results');
  }

  async consumeDeadLetters(
    handler: (task: any, metadata: DeadLetterMetadata) => Promise<boolean>
  ): Promise<void> {
    if (!this.channel) {
      throw new Error('Channel not initialized');
    }

    await this.channel.consume(
      'segmentation.dlq',
      async (message) => {
        if (!message) return;

        try {
          const task = JSON.parse(message.content.toString());
          const deathHeaders = message.properties.headers?.['x-death'];
          
          const metadata: DeadLetterMetadata = {
            retryCount: deathHeaders?.[0]?.count || 0,
            originalQueue: deathHeaders?.[0]?.queue || 'unknown',
            reason: deathHeaders?.[0]?.reason || 'unknown',
            timestamp: deathHeaders?.[0]?.time || new Date(),
          };

          logger.warn(
            `Processing dead letter for image ${task.imageId}, retry count: ${metadata.retryCount}`
          );

          const success = await handler(task, metadata);

          if (success) {
            this.channel!.ack(message);
            logger.info(`Successfully processed dead letter for image ${task.imageId}`);
          } else {
            // Permanently reject if handler returns false
            this.channel!.nack(message, false, false);
          }
        } catch (error) {
          logger.error('Error processing dead letter:', error);
          this.channel!.nack(message, false, false);
        }
      },
      { noAck: false }
    );

    logger.info('Started consuming dead letters');
  }

  async getQueueMetrics(): Promise<QueueMetrics> {
    const metrics: QueueMetrics = {
      connected: this.connection !== null && this.channel !== null,
      queues: {},
      publishedCount: this.publishedCount,
      consumedCount: this.consumedCount,
      errorCount: this.errorCount,
    };

    if (this.channel) {
      try {
        const tasksQueue = await this.channel.checkQueue('segmentation.tasks');
        const resultsQueue = await this.channel.checkQueue('segmentation.results');
        const dlQueue = await this.channel.checkQueue('segmentation.dlq');

        metrics.queues = {
          'segmentation.tasks': {
            messages: tasksQueue.messageCount,
            consumers: tasksQueue.consumerCount,
          },
          'segmentation.results': {
            messages: resultsQueue.messageCount,
            consumers: resultsQueue.consumerCount,
          },
          'segmentation.dlq': {
            messages: dlQueue.messageCount,
            consumers: dlQueue.consumerCount,
          },
        };
      } catch (error) {
        logger.error('Error fetching queue metrics:', error);
      }
    }

    return metrics;
  }

  async disconnect(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }
      if (this.connection) {
        await this.connection.close();
        this.connection = null;
      }
      logger.info('Disconnected from RabbitMQ');
    } catch (error) {
      logger.error('Error disconnecting from RabbitMQ:', error);
    }
  }
}

// Export singleton instance
export default new MessageQueueService();