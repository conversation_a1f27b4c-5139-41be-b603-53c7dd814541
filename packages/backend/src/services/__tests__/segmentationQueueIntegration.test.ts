import { SegmentationQueueService } from '../segmentationQueueService';
import messageQueueService from '../messageQueueService';
import pool from '../../db/pool';

// Mock dependencies
jest.mock('../messageQueueService');
jest.mock('../../db/pool');
jest.mock('../../utils/logger');

describe('SegmentationQueue Integration with MessageQueue', () => {
  let segmentationQueueService: SegmentationQueueService;
  let mockClient: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock database client
    mockClient = {
      query: jest.fn(),
      release: jest.fn(),
    };

    (pool.connect as jest.Mock).mockResolvedValue(mockClient);

    // Mock message queue methods
    (messageQueueService.publishSegmentationTask as jest.Mock).mockResolvedValue(true);
    (messageQueueService.consumeTasks as jest.Mock).mockImplementation(
      async (handler) => {
        // Store the handler for testing
        (messageQueueService as any).taskHandler = handler;
      }
    );

    segmentationQueueService = new SegmentationQueueService();
  });

  describe('Queue Task with Message Queue', () => {
    it('should publish task to message queue instead of database', async () => {
      const imageId = '123';
      const imagePath = '/uploads/test.jpg';
      const userId = 'user-123';

      // Mock database queries
      mockClient.query.mockResolvedValueOnce({ rows: [] }); // Check existing task
      mockClient.query.mockResolvedValueOnce({ 
        rows: [{ id: 'task-123' }] 
      }); // Insert task record

      await segmentationQueueService.queueTask(imageId, imagePath, userId);

      // Verify message queue was called
      expect(messageQueueService.publishSegmentationTask).toHaveBeenCalledWith({
        imageId,
        imagePath,
        userId,
        priority: 1,
      });

      // Verify database was updated
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO segmentation_tasks'),
        expect.arrayContaining([imageId, userId, 'queued'])
      );
    });

    it('should handle message queue failures gracefully', async () => {
      const imageId = '123';
      const imagePath = '/uploads/test.jpg';
      const userId = 'user-123';

      // Mock message queue failure
      (messageQueueService.publishSegmentationTask as jest.Mock)
        .mockRejectedValue(new Error('Queue unavailable'));

      // Mock database queries
      mockClient.query.mockResolvedValueOnce({ rows: [] });
      mockClient.query.mockResolvedValueOnce({ 
        rows: [{ id: 'task-123' }] 
      });

      const result = await segmentationQueueService.queueTask(
        imageId, 
        imagePath, 
        userId
      );

      // Should fallback to database queue
      expect(result).toBe('task-123');
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO segmentation_queue'),
        expect.arrayContaining([imageId, imagePath, 'queued'])
      );
    });
  });

  describe('Process Tasks from Message Queue', () => {
    it('should consume tasks from message queue', async () => {
      await segmentationQueueService.startProcessing();

      expect(messageQueueService.consumeTasks).toHaveBeenCalled();
    });

    it('should process task from message queue', async () => {
      const task = {
        imageId: '123',
        imagePath: '/uploads/test.jpg',
        userId: 'user-123',
        priority: 1,
      };

      // Start processing
      await segmentationQueueService.startProcessing();

      // Get the handler that was passed to consumeTasks
      const handler = (messageQueueService as any).taskHandler;
      expect(handler).toBeDefined();

      // Mock ML service response
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ success: true }),
      });
      global.fetch = mockFetch as any;

      // Mock database updates
      mockClient.query.mockResolvedValue({ rows: [] });

      // Call the handler with a task
      const result = await handler(task);

      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/segment'),
        expect.objectContaining({
          method: 'POST',
          body: expect.any(String),
        })
      );
    });

    it('should handle processing errors and return false', async () => {
      const task = {
        imageId: '123',
        imagePath: '/uploads/test.jpg',
        userId: 'user-123',
        priority: 1,
      };

      await segmentationQueueService.startProcessing();
      const handler = (messageQueueService as any).taskHandler;

      // Mock ML service error
      const mockFetch = jest.fn().mockRejectedValue(new Error('ML service down'));
      global.fetch = mockFetch as any;

      // Mock database updates
      mockClient.query.mockResolvedValue({ rows: [] });

      const result = await handler(task);

      expect(result).toBe(false);
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE segmentation_tasks'),
        expect.arrayContaining(['failed', task.imageId])
      );
    });
  });

  describe('Priority Queue Support', () => {
    it('should set priority based on user tier', async () => {
      const imageId = '123';
      const imagePath = '/uploads/test.jpg';
      const premiumUserId = 'premium-user-123';

      // Mock user tier check
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing task
        .mockResolvedValueOnce({ 
          rows: [{ tier: 'premium' }] 
        }) // Get user tier
        .mockResolvedValueOnce({ 
          rows: [{ id: 'task-123' }] 
        }); // Insert task

      await segmentationQueueService.queueTask(
        imageId, 
        imagePath, 
        premiumUserId
      );

      expect(messageQueueService.publishSegmentationTask).toHaveBeenCalledWith({
        imageId,
        imagePath,
        userId: premiumUserId,
        priority: 10, // Premium priority
      });
    });
  });

  describe('Dead Letter Queue Handling', () => {
    beforeEach(() => {
      (messageQueueService.consumeDeadLetters as jest.Mock).mockImplementation(
        async (handler) => {
          (messageQueueService as any).dlqHandler = handler;
        }
      );
    });

    it('should process dead letter messages', async () => {
      await segmentationQueueService.startDeadLetterProcessing();

      expect(messageQueueService.consumeDeadLetters).toHaveBeenCalled();

      const dlqHandler = (messageQueueService as any).dlqHandler;
      const task = {
        imageId: '123',
        imagePath: '/uploads/test.jpg',
        userId: 'user-123',
      };
      const metadata = {
        retryCount: 2,
        originalQueue: 'segmentation.tasks',
        reason: 'processing-error',
        timestamp: new Date(),
      };

      // Mock database queries
      mockClient.query.mockResolvedValue({ rows: [] });

      const result = await dlqHandler(task, metadata);

      // Should check retry count and decide whether to retry
      expect(result).toBe(true);
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO segmentation_failures'),
        expect.arrayContaining([
          task.imageId,
          metadata.reason,
          metadata.retryCount,
        ])
      );
    });

    it('should reject dead letters after max retries', async () => {
      await segmentationQueueService.startDeadLetterProcessing();

      const dlqHandler = (messageQueueService as any).dlqHandler;
      const task = {
        imageId: '123',
        imagePath: '/uploads/test.jpg',
        userId: 'user-123',
      };
      const metadata = {
        retryCount: 5, // Exceeds max retries
        originalQueue: 'segmentation.tasks',
        reason: 'processing-error',
        timestamp: new Date(),
      };

      mockClient.query.mockResolvedValue({ rows: [] });

      const result = await dlqHandler(task, metadata);

      expect(result).toBe(false); // Don't retry
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE images'),
        expect.arrayContaining(['failed', task.imageId])
      );
    });
  });

  describe('Queue Metrics', () => {
    it('should expose queue metrics for monitoring', async () => {
      const mockMetrics = {
        connected: true,
        queues: {
          'segmentation.tasks': { messages: 10, consumers: 2 },
          'segmentation.results': { messages: 5, consumers: 1 },
          'segmentation.dlq': { messages: 1, consumers: 1 },
        },
        publishedCount: 100,
        consumedCount: 95,
        errorCount: 5,
      };

      (messageQueueService.getQueueMetrics as jest.Mock).mockResolvedValue(
        mockMetrics
      );

      const metrics = await segmentationQueueService.getQueueMetrics();

      expect(metrics).toMatchObject({
        messageQueue: mockMetrics,
        database: expect.objectContaining({
          pendingTasks: expect.any(Number),
          processingTasks: expect.any(Number),
          failedTasks: expect.any(Number),
        }),
      });
    });
  });
});