import pool from '../db/pool';
import logger from '../utils/logger';
import config from '../config';
import messageQueueService from './messageQueueService';
import { promises as fs } from 'fs';
import path from 'path';

interface QueueMetrics {
  messageQueue?: any;
  database: {
    pendingTasks: number;
    processingTasks: number;
    failedTasks: number;
    completedTasks: number;
  };
}

export class SegmentationQueueService {
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;
  private readonly useMessageQueue: boolean;
  private readonly mlServiceUrl: string;
  private readonly internalBackendUrl: string;
  private readonly maxRetries = 3;

  constructor() {
    this.useMessageQueue = config.rabbitmq?.enabled || process.env.ENABLE_MESSAGE_QUEUE === 'true';
    this.mlServiceUrl = config.ml?.serviceUrl || process.env.ML_SERVICE_URL || 'http://ml:5002';
    this.internalBackendUrl = config.backend?.internalUrl || process.env.INTERNAL_BACKEND_URL || 'http://backend:5001';
  }

  async initialize(): Promise<void> {
    if (this.useMessageQueue) {
      try {
        await messageQueueService.connect();
        await messageQueueService.setupQueues();
        logger.info('Message queue initialized for segmentation tasks');
      } catch (error) {
        logger.error('Failed to initialize message queue:', error);
        logger.warn('Falling back to database queue');
      }
    }
  }

  async queueTask(imageId: string, imagePath: string, userId: string): Promise<string> {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      // Check if task already exists
      const existingTask = await client.query(
        'SELECT id FROM segmentation_tasks WHERE image_id = $1 AND task_status != $2',
        [imageId, 'failed']
      );

      if (existingTask.rows.length > 0) {
        await client.query('COMMIT');
        return existingTask.rows[0].id;
      }

      // Create task record
      const taskResult = await client.query(
        `INSERT INTO segmentation_tasks (image_id, user_id, task_status, created_at)
         VALUES ($1, $2, $3, NOW())
         RETURNING id`,
        [imageId, userId, 'queued']
      );

      const taskId = taskResult.rows[0].id;

      // Get user priority (if premium user)
      let priority = 1;
      try {
        const userResult = await client.query(
          'SELECT tier FROM users WHERE id = $1',
          [userId]
        );
        if (userResult.rows[0]?.tier === 'premium') {
          priority = 10;
        }
      } catch (error) {
        logger.warn('Could not fetch user tier:', error);
      }

      // Try to publish to message queue
      if (this.useMessageQueue) {
        try {
          await messageQueueService.publishSegmentationTask({
            imageId,
            imagePath,
            userId,
            priority,
          });

          await client.query('COMMIT');
          logger.info(`Task ${taskId} queued via message queue for image ${imageId}`);
          return taskId;
        } catch (error) {
          logger.error('Failed to publish to message queue:', error);
          // Continue to fallback
        }
      }

      // Fallback to database queue
      await client.query(
        `INSERT INTO segmentation_queue (image_id, image_path, status, priority, created_at)
         VALUES ($1, $2, $3, $4, NOW())`,
        [imageId, imagePath, 'queued', priority]
      );

      await client.query('COMMIT');
      logger.info(`Task ${taskId} queued via database for image ${imageId}`);
      return taskId;

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error queueing segmentation task:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async startProcessing(): Promise<void> {
    if (this.isProcessing) {
      logger.warn('Segmentation processing already started');
      return;
    }

    this.isProcessing = true;

    if (this.useMessageQueue) {
      // Start consuming from message queue
      await messageQueueService.consumeTasks(async (task) => {
        return await this.processTask(task);
      });

      // Also consume results
      await messageQueueService.consumeResults(async (result) => {
        await this.handleTaskResult(result);
      });

      logger.info('Started consuming segmentation tasks from message queue');
    } else {
      // Fallback to polling database
      this.startDatabasePolling();
    }
  }

  async startDeadLetterProcessing(): Promise<void> {
    if (!this.useMessageQueue) {
      return;
    }

    await messageQueueService.consumeDeadLetters(async (task, metadata) => {
      // Check if we should retry
      if (metadata.retryCount < this.maxRetries) {
        logger.info(`Retrying task for image ${task.imageId}, attempt ${metadata.retryCount + 1}`);
        
        // Log the failure
        await this.logFailure(task.imageId, metadata.reason, metadata.retryCount);
        
        // Retry by republishing
        try {
          await messageQueueService.publishSegmentationTask(task);
          return true;
        } catch (error) {
          logger.error('Failed to retry task:', error);
          return false;
        }
      } else {
        // Max retries exceeded, mark as permanently failed
        logger.error(`Task for image ${task.imageId} permanently failed after ${metadata.retryCount} attempts`);
        await this.markImageAsFailed(task.imageId);
        return false;
      }
    });

    logger.info('Started consuming dead letter queue');
  }

  private async processTask(task: any): Promise<boolean> {
    const client = await pool.connect();
    
    try {
      // Update task status to processing
      await client.query(
        'UPDATE segmentation_tasks SET task_status = $1, started_at = NOW() WHERE image_id = $2',
        ['processing', task.imageId]
      );

      await client.query(
        'UPDATE images SET segmentation_status = $1 WHERE id = $2',
        ['processing', task.imageId]
      );

      // Normalize path for ML service
      const mlImagePath = task.imagePath.replace(/^\/uploads\//, '/ML/uploads/');

      // Call ML service
      const response = await fetch(`${this.mlServiceUrl}/api/segment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId: task.messageId || task.imageId,
          imagePath: mlImagePath,
          callbackUrl: `${this.internalBackendUrl}/api/images/${task.imageId}/segmentation`,
        }),
      });

      if (!response.ok) {
        throw new Error(`ML service returned ${response.status}`);
      }

      logger.info(`Successfully sent task to ML service for image ${task.imageId}`);
      return true;

    } catch (error) {
      logger.error(`Error processing task for image ${task.imageId}:`, error);
      
      await client.query(
        'UPDATE segmentation_tasks SET task_status = $1, error_message = $2 WHERE image_id = $3',
        ['failed', error.message, task.imageId]
      );

      await client.query(
        'UPDATE images SET segmentation_status = $1 WHERE id = $2',
        ['failed', task.imageId]
      );

      return false;
    } finally {
      client.release();
    }
  }

  private async handleTaskResult(result: any): Promise<void> {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      if (result.status === 'completed') {
        // Update task status
        await client.query(
          `UPDATE segmentation_tasks 
           SET task_status = $1, completed_at = NOW(), processing_time_ms = $2
           WHERE image_id = $3`,
          ['completed', result.processingTime, result.imageId]
        );

        // Update image status
        await client.query(
          'UPDATE images SET segmentation_status = $1 WHERE id = $2',
          ['completed', result.imageId]
        );

        logger.info(`Task completed for image ${result.imageId}`);
      } else {
        // Handle failure
        await client.query(
          `UPDATE segmentation_tasks 
           SET task_status = $1, error_message = $2, completed_at = NOW()
           WHERE image_id = $3`,
          ['failed', result.error, result.imageId]
        );

        await client.query(
          'UPDATE images SET segmentation_status = $1 WHERE id = $2',
          ['failed', result.imageId]
        );

        logger.error(`Task failed for image ${result.imageId}: ${result.error}`);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling task result:', error);
    } finally {
      client.release();
    }
  }

  private async logFailure(imageId: string, reason: string, retryCount: number): Promise<void> {
    const client = await pool.connect();
    
    try {
      await client.query(
        `INSERT INTO segmentation_failures (image_id, reason, retry_count, created_at)
         VALUES ($1, $2, $3, NOW())`,
        [imageId, reason, retryCount]
      );
    } catch (error) {
      logger.error('Error logging failure:', error);
    } finally {
      client.release();
    }
  }

  private async markImageAsFailed(imageId: string): Promise<void> {
    const client = await pool.connect();
    
    try {
      await client.query(
        'UPDATE images SET segmentation_status = $1 WHERE id = $2',
        ['failed', imageId]
      );
      
      await client.query(
        `UPDATE segmentation_tasks 
         SET task_status = $1, error_message = $2
         WHERE image_id = $3`,
        ['failed', 'Max retries exceeded', imageId]
      );
    } catch (error) {
      logger.error('Error marking image as failed:', error);
    } finally {
      client.release();
    }
  }

  private startDatabasePolling(): void {
    this.processingInterval = setInterval(async () => {
      await this.processNextFromDatabase();
    }, 5000); // Poll every 5 seconds

    logger.info('Started database polling for segmentation tasks');
  }

  private async processNextFromDatabase(): Promise<void> {
    // Implementation for database-based queue processing
    // This is the fallback when message queue is not available
    const client = await pool.connect();
    
    try {
      // Get next task
      const result = await client.query(
        `UPDATE segmentation_queue
         SET status = 'processing', started_at = NOW()
         WHERE id = (
           SELECT id FROM segmentation_queue
           WHERE status = 'queued'
           ORDER BY priority DESC, created_at ASC
           LIMIT 1
           FOR UPDATE SKIP LOCKED
         )
         RETURNING *`,
      );

      if (result.rows.length > 0) {
        const task = result.rows[0];
        await this.processTask({
          imageId: task.image_id,
          imagePath: task.image_path,
          userId: task.user_id || 'unknown',
          priority: task.priority,
        });
      }
    } catch (error) {
      logger.error('Error processing database queue:', error);
    } finally {
      client.release();
    }
  }

  async stopProcessing(): Promise<void> {
    this.isProcessing = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    if (this.useMessageQueue) {
      await messageQueueService.disconnect();
    }

    logger.info('Stopped segmentation processing');
  }

  async getQueueMetrics(): Promise<QueueMetrics> {
    const client = await pool.connect();
    
    try {
      // Get database metrics
      const pendingResult = await client.query(
        "SELECT COUNT(*) FROM segmentation_tasks WHERE task_status = 'queued'"
      );
      const processingResult = await client.query(
        "SELECT COUNT(*) FROM segmentation_tasks WHERE task_status = 'processing'"
      );
      const failedResult = await client.query(
        "SELECT COUNT(*) FROM segmentation_tasks WHERE task_status = 'failed'"
      );
      const completedResult = await client.query(
        "SELECT COUNT(*) FROM segmentation_tasks WHERE task_status = 'completed'"
      );

      const metrics: QueueMetrics = {
        database: {
          pendingTasks: parseInt(pendingResult.rows[0].count),
          processingTasks: parseInt(processingResult.rows[0].count),
          failedTasks: parseInt(failedResult.rows[0].count),
          completedTasks: parseInt(completedResult.rows[0].count),
        },
      };

      // Add message queue metrics if available
      if (this.useMessageQueue) {
        try {
          metrics.messageQueue = await messageQueueService.getQueueMetrics();
        } catch (error) {
          logger.error('Error fetching message queue metrics:', error);
        }
      }

      return metrics;
    } finally {
      client.release();
    }
  }
}

// Export singleton instance
export default new SegmentationQueueService();