{"common": {"appName": "Segmentación de esferoides", "appNameShort": "SpheroSeg", "loading": "Cargando...", "loadingAccount": "Cargando su cuenta...", "loadingApplication": "Cargando aplicación...", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "search": "Buscar", "error": "Error", "success": "Éxito", "reset": "Restablecer", "clear": "Limpiar", "close": "<PERSON><PERSON><PERSON>", "back": "Atrás", "signIn": "In<PERSON><PERSON>", "signUp": "Registrarse", "signOut": "<PERSON><PERSON><PERSON>", "signingIn": "Iniciando se<PERSON>...", "settings": "Configuración", "profile": "Perfil", "dashboard": "Panel de control", "project": "Proyecto", "projects": "Proyectos", "newProject": "Nuevo proyecto", "upload": "<PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "removeAll": "Eliminar todo", "uploadImages": "<PERSON><PERSON>", "recentAnalyses": "Análisis recientes", "noProjects": "No se encontraron proyectos", "noImages": "No se encontraron imágenes", "createYourFirst": "Cree su primer proyecto para comenzar", "tryAgain": "Intentar de nuevo", "email": "Correo electrónico", "password": "Contraseña", "confirmPassword": "Confirmar con<PERSON>", "firstName": "Nombre", "lastName": "Apellido", "username": "Nombre de usuario", "name": "Nombre", "description": "Descripción", "date": "<PERSON><PERSON>", "status": "Estado", "image": "Imagen", "projectName": "Nombre del proyecto", "projectDescription": "Descripción del proyecto", "language": "Idioma", "theme": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "system": "Sistema", "welcome": "Bienvenido a la plataforma de segmentación de esferoides", "account": "C<PERSON><PERSON>", "passwordConfirm": "Confirmar con<PERSON>", "manageAccount": "Administra<PERSON> cuenta", "changePassword": "Cambiar contraseña", "deleteAccount": "Eliminar cuenta", "requestAccess": "Solicitar acceso", "accessRequest": "Solicitud de acceso", "createAccount": "<PERSON><PERSON><PERSON> cuenta", "signInToAccount": "Iniciar sesión en la cuenta", "termsOfService": "Términos de servicio", "privacyPolicy": "Política de privacidad", "termsOfServiceLink": "Términos de servicio", "privacyPolicyLink": "Política de privacidad", "optional": "Opcional", "saveChanges": "Guardar cambios", "saving": "Guardando", "notSpecified": "No especificado", "enable": "Habilitar", "disable": "Deshabilitar", "backToHome": "Volver al inicio", "and": "y", "lastChange": "Último cambio", "sort": "Ordenar", "emailPlaceholder": "Ingrese su correo electrónico", "passwordPlaceholder": "Ingrese su contraseña", "export": "Exportar", "selectImages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noImagesDescription": "Cargue imágenes para comenzar con su proyecto", "yes": "Sí", "no": "No", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "files": "Archivos", "validationFailed": "Validación fallida", "cropAvatar": "Recortar foto de perfil", "profileTitle": "Perfil", "profileDescription": "Actualice su información de perfil visible para otros usuarios", "profileUsername": "Nombre de usuario", "profileUsernamePlaceholder": "Ingrese su nombre de usuario", "profileFullName": "Nombre completo", "profileFullNamePlaceholder": "Ingrese su nombre completo", "profileTitlePlaceholder": "ej. <PERSON><PERSON><PERSON><PERSON><PERSON>, Profesor", "profileOrganization": "Organización", "profileOrganizationPlaceholder": "Ingrese su organización o institución", "profileBio": "Biografía", "profileBioPlaceholder": "Escriba una breve biografía sobre usted", "profileBioDescription": "Breve descripción de sus intereses de investigación y experiencia", "profileLocation": "Ubicación", "profileLocationPlaceholder": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, República Checa", "profileSaveButton": "Guardar perfil", "actions": "Acciones", "view": "<PERSON>er", "share": "Compartir", "projectNamePlaceholder": "Ingrese el nombre del proyecto", "projectDescPlaceholder": "Ingrese la descripción del proyecto", "creatingProject": "Creando proyecto...", "createSuccess": "Proyecto creado exitosamente", "unauthorized": "No está autorizado para realizar esta acción", "forbidden": "Acceso prohibido", "maxFileSize": "Tamaño máximo del archivo: {{size}}MB", "accepted": "<PERSON><PERSON><PERSON>", "processing": "Procesando...", "uploading": "Cargando...", "uploadComplete": "Carga completada", "uploadFailed": "Carga fallida", "deletePolygon": "Eliminar polígono", "editor": {"error": "Error", "success": "Éxito", "edit": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>"}}, "settings": {"title": "Configuración", "pageTitle": "Configuración", "profile": "Perfil", "account": "C<PERSON><PERSON>", "appearance": "Apariencia", "profileSettings": "Configuración del perfil", "accountSettings": "Configuración de la cuenta", "securitySettings": "Configuración de seguridad", "preferenceSettings": "Configuración de preferencias", "selectLanguage": "Seleccionar idioma", "selectTheme": "<PERSON><PERSON><PERSON><PERSON><PERSON> tema", "updateProfile": "Actualizar perfil", "changePassword": "Cambiar contraseña", "deleteAccount": "Eliminar cuenta", "savedChanges": "Cambios guardados exitosamente", "saveChanges": "Guardar cambios", "profileUpdated": "Perfil actualizado exitosamente", "languageSettings": "Configuración de idioma", "themeSettings": "Configuración de tema", "privacySettings": "Configuración de privacidad", "exportData": "Exportar datos", "importData": "Importar datos", "uploadAvatar": "Cargar foto de perfil", "removeAvatar": "Eliminar foto de perfil", "twoFactorAuth": "Autenticación de dos factores", "emailNotifications": "Notificaciones por correo", "pushNotifications": "Notificaciones push", "weeklyDigest": "Resumen semanal", "monthlyReport": "Informe mensual", "displaySettings": "Configuración de visualización", "accessibilitySettings": "Configuración de accesibilidad", "advancedSettings": "Configuración avanzada", "useBrowserLanguage": "Usar idioma del navegador", "language": "Idioma", "theme": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "system": "Sistema", "languageUpdated": "Idioma actualizado exitosamente", "themeUpdated": "Tema actualizado exitosamente", "toggleTheme": "Alternar tema", "languageDescription": "Elija su idioma preferido", "themeDescription": "Elija su tema preferido", "profileLoadError": "Error al cargar el perfil", "appearanceDescription": "Personalice la apariencia de la aplicación", "personal": "Información personal", "fullName": "Nombre completo", "organization": "Organización", "department": "Departamento", "publicProfile": "<PERSON><PERSON><PERSON>", "makeProfileVisible": "Hacer mi perfil visible para otros investigadores", "passwordSettings": "Configuración de contraseña", "currentPassword": "Contraseña actual", "newPassword": "Nueva contraseña", "confirmNewPassword": "Confirmar nueva contraseña", "dangerZone": "Zona de peligro", "deleteAccountWarning": "Una vez que elimine su cuenta, no hay vuelta atrás. Todos sus datos serán eliminados permanentemente.", "savingChanges": "Guardando cambios...", "savePreferences": "Guardar preferencias", "usernameTaken": "Este nombre de usuario ya está en uso", "deleteAccountDescription": "Esta acción es irreversible. Todos sus datos serán eliminados permanentemente.", "confirmUsername": "Confirme su correo electrónico", "password": "Contraseña", "enterPassword": "Ingrese su contraseña", "passwordChangeError": "Error al cambiar la contraseña", "passwordChangeSuccess": "Contraseña cambiada exitosamente", "passwordsDoNotMatch": "Las contraseñas no coinciden", "accountDeleteSuccess": "Cuenta eliminada exitosamente", "accountDeleteError": "Error al eliminar la cuenta", "passwordChanged": "Contraseña cambiada", "confirmPasswordLabel": "Confirmar con<PERSON>", "changePasswordDescription": "Cambie su contraseña para mantener su cuenta segura", "dangerZoneDescription": "Estas acciones son irreversibles y eliminarán permanentemente sus datos", "deletingAccount": "Eliminando cuenta...", "deleteAccountError": "Error al eliminar la cuenta"}, "segmentation": {"contextMenu": {"editPolygon": "<PERSON><PERSON>", "splitPolygon": "<PERSON><PERSON><PERSON>", "deletePolygon": "Eliminar polígono", "confirmDeleteTitle": "¿Está seguro de que desea eliminar el polígono?", "confirmDeleteMessage": "Esta acción es irreversible. El polígono será eliminado permanentemente de la segmentación.", "duplicateVertex": "Duplicar vértice", "deleteVertex": "Eliminar vértice"}, "title": "Editor de segmentación", "resolution": "{width}x{height}", "queue": {"title": "Cola de segmentación", "summary": "{{total}} tareas en total ({{running}} procesando, {{queued}} en cola)", "noRunningTasks": "Sin tareas en ejecución", "noQueuedTasks": "Sin tareas en cola", "task": "Tarea", "statusRunning": "Segmentación: {{count}} ejecutándose{{queued}}", "statusQueued": ", {{count}} en cola", "statusOnlyQueued": "Segmentación: {{count}} en cola", "statusOnlyQueued_one": "Segmentación: 1 en cola", "statusOnlyQueued_other": "Segmentación: {{count}} en cola", "processing": "Procesando", "queued": "En cola", "statusProcessing": "Segmentación: {{count}} procesando", "statusReady": "Listo", "tasksTotal": "{{total}} tareas en total ({{running}} procesando, {{queued}} en cola)"}, "selectPolygonForEdit": "Seleccione un polígono para editar", "selectPolygonForSlice": "Seleccione un polígono para cortar", "selectPolygonForAddPoints": "Seleccione un polígono para agregar puntos", "clickToAddPoint": "Haga clic para agregar un punto", "clickToCompletePolygon": "Haga clic en el primer punto para completar el polígono", "clickToAddFirstSlicePoint": "Haga clic para agregar el primer punto de corte", "clickToAddSecondSlicePoint": "Haga clic para agregar el segundo punto de corte", "polygonCreationMode": "Modo de creación de polígono", "polygonEditMode": "Modo de edición de polígono", "polygonSliceMode": "Modo de corte de polígono", "polygonAddPointsMode": "Modo de agregar puntos", "viewMode": "Modo de visualización", "totalPolygons": "Total de polígonos", "totalVertices": "Total de vértices", "vertices": "Vértices", "zoom": "Zoom", "mode": "Modo", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "polygons": "Polígonos", "imageNotFound": "Imagen no encontrada", "returnToProject": "Volver al proyecto", "backToProject": "Volver al proyecto", "previousImage": "Imagen anterior", "nextImage": "<PERSON><PERSON> sigu<PERSON>e", "toggleShortcuts": "<PERSON>ernar <PERSON>", "modes": {"view": "Modo de visualización", "edit": "Modo de edición", "create": "Modo de creación", "slice": "Modo de corte", "addPoints": "Modo de agregar puntos", "deletePolygon": "Modo de eliminar pol<PERSON>", "createPolygon": "<PERSON><PERSON> de crear p<PERSON>", "editVertices": "Modo de editar vértices", "editMode": "Modo de edición", "slicingMode": "Modo de corte", "pointAddingMode": "Modo de agregar puntos"}, "status": {"processing": "Procesando", "queued": "En cola", "completed": "Completado", "failed": "Fallido", "pending": "Pendiente"}, "autoSave": {"enabled": "Autoguardado: Habilitado", "disabled": "Autoguardado: <PERSON><PERSON><PERSON><PERSON><PERSON>", "idle": "Autoguardado: Inactivo", "pending": "Pendiente...", "saving": "Guardando...", "success": "Guardado", "error": "Error"}, "loading": "Cargando segmentación...", "polygon": "Polígono", "unsavedChanges": "Cam<PERSON>s sin guardar", "noData": "No hay datos de segmentación disponibles", "noPolygons": "No se encontraron polígonos", "regions": "Segmentación", "position": "Posición", "polygonDeleted": "Polígono eliminado exitosamente", "saveSuccess": "Segmentación guardada exitosamente", "resegmentSuccess": "Resegmentación iniciada exitosamente", "resegmentComplete": "Resegmentación completada exitosamente", "resegmentError": "Error al resegmentar la imagen", "resegmentButton": "Resegmentar", "completedSegmentation": "Completado", "resegmentButtonTooltip": "Resegmentar con red neuronal", "helpTips": {"title": "Consejos:", "edit": {"createPoint": "Haga clic para crear un nuevo punto", "shiftPoints": "Mantenga presionado Shift para crear automáticamente una secuencia de puntos", "closePolygon": "Cierre el polígono haciendo clic en el primer punto"}, "slice": {"start": "Haga clic para iniciar el corte", "finish": "Haga clic nuevamente para finalizar el corte", "cancel": "Esc para cancelar el corte"}, "addPoint": {"hover": "Pase el cursor sobre la línea del polígono", "click": "Haga clic para agregar un punto al polígono seleccionado", "exit": "Esc para salir del modo de agregar"}}}, "errors": {"somethingWentWrong": "Algo salió mal", "componentError": "Ocurrió un error en este componente", "errorDetails": "Detalles del error", "tryAgain": "Intentar de nuevo", "reloadPage": "<PERSON><PERSON><PERSON>", "goBack": "Volver", "notFound": "Página no encontrada", "pageNotFoundMessage": "La página solicitada no pudo ser encontrada", "returnToHome": "Volver al inicio", "unauthorized": "Acceso no autorizado", "forbidden": "Acceso prohibido", "serverError": "Error del servidor", "networkError": "Error de red", "timeoutError": "Tiempo de espera agotado", "validationError": "Error de validación", "unknownError": "Error descon<PERSON>", "goHome": "Ir a la página de inicio", "fetchSegmentationFailed": "Error al obtener la segmentación", "fetchImageFailed": "Error al obtener la imagen", "saveSegmentationFailed": "Error al guardar la segmentación", "missingPermissions": "Permisos insuficientes", "invalidInput": "Entrada inválida", "resourceNotFound": "Recurso no encontrado"}, "project": {"detail": {"noImagesSelected": "No hay imágenes seleccionadas", "triggeringResegmentation": "Iniciando resegmentación para {{count}} imágenes...", "deleteConfirmation": "¿Está seguro de que desea eliminar {{count}} imágenes? Esta acción no se puede deshacer.", "deletingImages": "Eliminando {{count}} imágenes...", "deleteSuccess": "Se eliminaron exitosamente {{count}} imágenes", "deleteFailed": "Error al eliminar {{count}} imágenes", "preparingExport": "Preparando exportación de {{count}} imágenes..."}, "segmentation": {"processingInBatches": "Iniciando segmentación para {{count}} imágenes en {{batches}} lotes...", "batchQueued": "Lote {{current}}/{{total}} en cola exitosamente", "batchQueuedFallback": "Lote {{current}}/{{total}} en cola exitosamente (punto de acceso alternativo)", "batchError": "Error procesando lote {{current}}/{{total}}", "partialSuccess": "Segmentación: {{success}} imágenes en cola exitosamente, {{failed}} fallaron", "allSuccess": "Segmentación: <PERSON><PERSON> las {{count}} imágenes en cola exitosamente", "allFailed": "Segmentación: <PERSON><PERSON> las {{count}} imágenes fallaron", "startedImages": "Segmentación iniciada para {{count}} imágenes", "queuedLocallyWarning": "Segmentación en cola localmente para {{count}} imágenes. Falló la conexión con el servidor."}, "loading": "Cargando proyecto...", "notFound": "Proyecto no encontrado", "error": "Error al cargar el proyecto", "empty": "Este proyecto está vacío", "noImages": {"title": "Sin imágenes aún", "description": "Este proyecto aún no tiene imágenes. Cargue imágenes para comenzar.", "uploadButton": "<PERSON><PERSON>"}, "addImages": "Agregue imágenes para comenzar", "deleteProject": "Eliminar proyecto", "deleteConfirmation": "¿Está seguro de que desea eliminar el proyecto \"{{projectName}}\"? Esta acción no se puede deshacer.", "duplicateProject": "Duplicar proyecto", "duplicateDescription": "Crear una copia de este proyecto. El nuevo proyecto se creará con el nombre que proporcione a continuación.", "newProjectName": "Nuevo nombre del proyecto", "enterProjectName": "Ingrese el nuevo nombre del proyecto", "duplicate": "Duplicar"}, "projectsPage": {"title": "Proyectos", "description": "Gestionar proyectos de investigación", "createNew": "Crear nuevo proyecto", "createProject": "<PERSON><PERSON>r proyecto", "createProjectDesc": "Iniciar un nuevo proyecto de investigación", "projectName": "Nombre del proyecto", "projectDescription": "Descripción del proyecto", "projectNamePlaceholder": "Ingrese el nombre del proyecto", "projectDescriptionPlaceholder": "Ingrese la descripción del proyecto", "projectCreated": "Proyecto creado exitosamente", "projectCreationFailed": "Error al crear el proyecto", "projectDeleted": "Proyecto eliminado exitosamente", "projectDeletionFailed": "Error al eliminar el proyecto", "confirmDelete": "¿Está seguro de que desea eliminar este proyecto?", "confirmDeleteDescription": "Esta acción no se puede deshacer. Todos los datos asociados con este proyecto serán eliminados permanentemente.", "deleteProject": "Eliminar proyecto", "editProject": "Editar proyecto", "viewProject": "Ver proyecto", "projectUpdated": "Proyecto actualizado exitosamente", "projectUpdateFailed": "Error al actualizar el proyecto", "noProjects": "No se encontraron proyectos", "createFirstProject": "Cree su primer proyecto para comenzar", "searchProjects": "Buscar proyectos...", "filterProjects": "Filtrar proyectos", "sortProjects": "Ordenar proyectos", "projectNameRequired": "El nombre del proyecto es obligatorio", "loginRequired": "Debe iniciar sesión para crear un proyecto", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Última actualización", "imageCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Estado", "actions": "Acciones", "loading": "Cargando proyectos...", "error": "Error al cargar proyectos", "retry": "Intentar de nuevo", "duplicating": "Duplicando proyecto...", "duplicate": "Duplicar", "duplicateSuccess": "Proyecto duplicado exitosamente", "duplicateFailed": "Error al duplicar el proyecto", "duplicateTitle": "Duplicar proyecto", "duplicateProject": "Duplicar proyecto", "duplicateProjectDescription": "Crear una copia de este proyecto incluyendo todas las imágenes. Puede personalizar las opciones a continuación.", "duplicateCancelled": "Duplicación de proyecto cancelada", "duplicatingProject": "Duplicando proyecto", "duplicatingProjectDescription": "Su proyecto se está duplicando. Esto puede tomar unos momentos.", "duplicateProgress": "Progreso de duplicación", "duplicationComplete": "Duplicación de proyecto completada", "duplicationTaskFetchError": "Error al obtener datos de la tarea", "duplicationCancelError": "Error al cancelar la duplicación", "duplicateProgressDescription": "Su proyecto se está duplicando. Este proceso puede tomar tiempo para proyectos grandes.", "duplicationPending": "Pendiente", "duplicationProcessing": "Procesando", "duplicationCompleted": "Completado", "duplicationFailed": "Fallido", "duplicationCancelled": "Cancelado", "duplicationCancellationFailed": "Error al cancelar la duplicación", "duplicationSuccessMessage": "¡Proyecto duplicado exitosamente! Ahora puede acceder al nuevo proyecto.", "copySegmentations": "Copiar resultados de segmentación", "resetImageStatus": "Restablecer estado de procesamiento de imágenes", "newProjectTitle": "Nuevo título del proyecto", "itemsProcessed": "elementos procesados", "items": "elementos", "unknownProject": "Proyecto desconocido", "activeTasks": "Activas", "allTasks": "<PERSON><PERSON>", "noActiveDuplications": "Sin duplicaciones activas", "noDuplications": "No se encontraron tareas de duplicación", "deleteProjectDescription": "Esta acción eliminará permanentemente el proyecto y todos los datos asociados.", "deleteWarning": "Esta acción no se puede deshacer. Todos los datos asociados con este proyecto serán eliminados permanentemente.", "untitledProject": "Proyecto sin título", "typeToConfirm": "Escriba \"eliminar\" para confirmar", "deleteConfirm": "¿Está seguro de que desea eliminar este proyecto?", "exportProject": "Exportar proyecto", "archived": "Archivado", "completed": "Completado", "draft": "<PERSON><PERSON><PERSON>", "active": "Activo", "createDate": "<PERSON><PERSON><PERSON>", "lastModified": "Última modificación", "projectDescPlaceholder": "Ingrese la descripción del proyecto", "creatingProject": "Creando proyecto...", "noImages": {"title": "Sin imágenes aún", "description": "Este proyecto aún no tiene imágenes. Cargue imágenes para iniciar la segmentación.", "uploadButton": "<PERSON><PERSON>"}}, "auth": {"signIn": "In<PERSON><PERSON>", "signUp": "Registrarse", "signOut": "<PERSON><PERSON><PERSON>", "signingIn": "Iniciando se<PERSON>...", "forgotPassword": "¿Olvidó su contraseña?", "resetPassword": "Restablecer contraseña", "dontHaveAccount": "¿No tiene una cuenta?", "alreadyHaveAccount": "¿Ya tiene una cuenta?", "createAccount": "<PERSON><PERSON><PERSON> cuenta", "signInWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "signInWithGithub": "Iniciar se<PERSON><PERSON> con GitHub", "or": "o", "signInTitle": "In<PERSON><PERSON>", "signInDescription": "Inicie sesión en su cuenta", "noAccount": "¿No tiene una cuenta?", "emailAddressLabel": "Dirección de correo electrónico", "passwordLabel": "Contraseña", "currentPasswordLabel": "Contraseña actual", "newPasswordLabel": "Nueva contraseña", "confirmPasswordLabel": "Confirmar con<PERSON>", "rememberMe": "Recordarme", "emailRequired": "El correo electrónico es obligatorio", "passwordRequired": "La contraseña es obligatoria", "alreadyLoggedInTitle": "Ya ha iniciado sesión", "alreadyLoggedInMessage": "Ya ha iniciado sesión en su cuenta", "goToDashboardLink": "Ir al panel de control", "invalidEmail": "Dirección de correo electrónico inválida", "passwordTooShort": "La contraseña debe tener al menos 6 caracteres", "passwordsDontMatch": "Las contraseñas no coinciden", "invalidCredentials": "Correo electrónico o contraseña inválidos", "accountCreated": "Cuenta creada exitosamente", "resetLinkSent": "Enlace de restablecimiento de contraseña enviado a su correo electrónico", "resetSuccess": "Contraseña restablecida exitosamente", "signInSuccess": "Sesión iniciada exitosamente", "signOutSuccess": "Sesión cerrada exitosamente", "sessionExpired": "Su sesión ha expirado. Por favor, inicie sesión nuevamente.", "unauthorized": "No está autorizado para acceder a este recurso", "verifyEmail": "Por favor, verifique su dirección de correo electrónico", "verificationLinkSent": "Enlace de verificación enviado a su correo electrónico", "verificationSuccess": "Correo electrónico verificado exitosamente", "resendVerification": "Reenviar correo de verificación", "requestAccess": "Solicitar acceso", "termsAndPrivacy": "Al registrarse, acepta nuestros Términos de servicio y Política de privacidad.", "forgotPasswordLink": "¿Olvidó su contraseña?", "passwordChanged": "Contraseña cambiada exitosamente", "currentPasswordIncorrect": "La contraseña actual es incorrecta", "registerTitle": "<PERSON><PERSON><PERSON> cuenta", "registerDescription": "Registrarse para una nueva cuenta", "registerSuccess": "¡Registro exitoso! Ahora puede iniciar sesión.", "emailPlaceholder": "Ingrese su correo electrónico", "passwordPlaceholder": "Ingrese su contraseña", "firstNamePlaceholder": "ej. <PERSON>", "lastNamePlaceholder": "ej. <PERSON>", "passwordConfirmPlaceholder": "Confirme su contraseña", "signUpTitle": "<PERSON><PERSON><PERSON> cuenta", "signUpDescription": "Registrarse para una nueva cuenta", "enterInfoCreateAccount": "Ingrese su información para crear una cuenta", "creatingAccount": "<PERSON><PERSON>ndo cuenta...", "emailAlreadyExists": "Este correo electrónico ya está registrado. Por favor, use un correo diferente o inicie sesión.", "emailHasPendingRequest": "Este correo electrónico ya tiene una solicitud de acceso pendiente. Por favor, espere la aprobación.", "signUpSuccessEmail": "¡Registro exitoso! Por favor, revise su correo electrónico o espere la aprobación del administrador.", "signUpFailed": "Error en el registro. Por favor, intente de nuevo.", "alreadyHaveAccess": "¿Ya tiene acceso?", "forgotPasswordTitle": "Restablecer su contraseña", "checkYourEmail": "Revise su correo electrónico para una nueva contraseña", "enterEmailForReset": "Ingrese su dirección de correo electrónico y le enviaremos una nueva contraseña", "passwordResetLinkSent": "Si existe una cuenta para este correo electrónico, se ha enviado una nueva contraseña", "passwordResetFailed": "Error al enviar nueva contraseña. Por favor, intente de nuevo.", "enterEmail": "Por favor, ingrese su dirección de correo electrónico", "sendingResetLink": "Enviando nueva contraseña...", "sendResetLink": "Enviar nueva contraseña", "backToSignIn": "Volver a iniciar sesión"}, "requestAccess": {"and": "y", "title": "Solicitar acceso a la plataforma de segmentación de esferoides", "description": "Complete el siguiente formulario para solicitar acceso a nuestra plataforma. Revisaremos su solicitud y nos pondremos en contacto pronto.", "emailLabel": "Su dirección de correo electrónico", "nameLabel": "Su nombre", "institutionLabel": "Institución/Empresa", "reasonLabel": "Motivo del acceso", "submitRequest": "<PERSON><PERSON><PERSON> solicitud", "requestReceived": "Solicitud recibida", "thankYou": "<PERSON><PERSON><PERSON> por su interés", "weWillContact": "Revisaremos su solicitud y nos pondremos en contacto pronto", "submitSuccess": "¡Solicitud enviada exitosamente!", "emailPlaceholder": "Ingrese su dirección de correo electrónico", "namePlaceholder": "Ingrese su nombre completo", "institutionPlaceholder": "Ingrese el nombre de su institución o empresa", "reasonPlaceholder": "Por favor, describa cómo planea usar la plataforma", "fillRequired": "Por favor, complete todos los campos obligatorios", "submittingRequest": "Enviando solicitud...", "submitError": "Error al enviar la solicitud", "alreadyPending": "Ya existe una solicitud de acceso pendiente para este correo electrónico", "agreeToTerms": "Al enviar esta solicitud, acepta nuestros"}, "requestAccessForm": {"title": "Solicitar acceso a la plataforma de segmentación de esferoides", "description": "Complete el siguiente formulario para solicitar acceso a nuestra plataforma. Revisaremos su solicitud y nos pondremos en contacto pronto.", "emailLabel": "Su dirección de correo electrónico", "nameLabel": "Su nombre", "institutionLabel": "Institución/Empresa", "reasonLabel": "Motivo del acceso", "submitButton": "<PERSON><PERSON><PERSON> solicitud", "signInPrompt": "¿Ya tiene una cuenta?", "signInLink": "In<PERSON><PERSON>", "thankYouTitle": "<PERSON><PERSON><PERSON> por su interés", "weWillContact": "Revisaremos su solicitud y nos pondremos en contacto pronto", "agreeToTerms": "Al enviar esta solicitud, acepta nuestros", "and": "y"}, "documentation": {"tag": "Guía del usuario", "title": "Documentación de SpheroSeg", "subtitle": "Aprenda a usar eficazmente la plataforma de segmentación de esferoides.", "sidebar": {"title": "Secciones", "introduction": "Introducción", "gettingStarted": "Primeros pasos", "uploadingImages": "Carga de imágenes", "segmentationProcess": "Proceso de segmentación", "apiReference": "Referencia de API"}, "introduction": {"title": "Introducción", "imageAlt": "Ilustración del flujo de trabajo de análisis de esferoides", "whatIs": {"title": "¿Qué es SpheroSeg?", "paragraph1": "SpheroSeg es una plataforma de vanguardia diseñada para la segmentación y análisis de esferoides celulares en imágenes microscópicas. Nuestra herramienta proporciona a los investigadores capacidades precisas de detección y análisis.", "paragraph2": "Utiliza algoritmos avanzados de IA basados en aprendizaje profundo para identificar y segmentar automáticamente esferoides en sus imágenes con alta precisión y consistencia.", "paragraph3": "Esta documentación le guiará a través de todos los aspectos del uso de la plataforma, desde los primeros pasos hasta las funciones avanzadas y la integración de API."}}, "gettingStarted": {"title": "Primeros pasos", "accountCreation": {"title": "Creación de cuenta", "paragraph1": "Para usar SpheroSeg, necesita crear una cuenta. Esto nos permite almacenar de forma segura sus proyectos e imágenes.", "step1Prefix": "Visite la", "step1Link": "página de registro", "step2": "Ingrese su dirección de correo electrónico institucional y cree una contraseña", "step3": "Complete su perfil con su nombre e institución", "step4": "Verifique su dirección de correo electrónico a través del enlace enviado a su bandeja de entrada"}, "creatingProject": {"title": "Creando su primer proyecto", "paragraph1": "Los proyectos le ayudan a organizar su trabajo. Cada proyecto puede contener múltiples imágenes y sus correspondientes resultados de segmentación.", "step1": "En su panel de control, haga clic en \"Nuevo proyecto\"", "step2": "Ingrese un nombre y descripción del proyecto", "step3": "Seleccione el tipo de proyecto (predeterminado: Análisis de esferoides)", "step4": "Haga clic en \"Crear proyecto\" para continuar"}}, "uploadingImages": {"title": "Carga de imágenes", "paragraph1": "SpheroSeg admite varios formatos de imagen comúnmente utilizados en microscopía, incluyendo TIFF, PNG y JPEG.", "methods": {"title": "Métodos de carga", "paragraph1": "Hay varias formas de cargar imágenes:", "step1": "Arrastre y suelte archivos directamente en el área de carga", "step2": "Haga clic en el área de carga para explorar y seleccionar archivos desde su computadora", "step3": "Carga por lotes de múltiples imágenes a la vez"}, "note": {"prefix": "Nota:", "text": "Para obtener resultados óptimos, asegúrese de que sus imágenes microscópicas tengan buen contraste entre el esferoide y el fondo."}}, "segmentationProcess": {"title": "Proceso de segmentación", "paragraph1": "El proceso de segmentación identifica los límites de los esferoides en sus imágenes, permitiendo un análisis preciso de su morfología.", "automatic": {"title": "Segmentación automática", "paragraph1": "Nuestra segmentación automática impulsada por IA puede detectar límites de esferoides con alta precisión:", "step1": "Seleccione una imagen de su proyecto", "step2": "Haga clic en \"Auto-segmentar\" para iniciar el proceso", "step3": "El sistema procesará la imagen y mostrará los límites detectados", "step4": "Revise los resultados en el editor de segmentación"}, "manual": {"title": "Ajustes manuales", "paragraph1": "A veces la segmentación automática puede requerir refinamiento. Nuestro editor proporciona herramientas para:", "step1": "Agregar o eliminar vértices a lo largo del límite", "step2": "Ajustar posiciones de vértices para límites más precisos", "step3": "Dividir o fusionar regiones", "step4": "Agregar o eliminar huecos dentro de los esferoides"}}, "apiReference": {"title": "Referencia de API", "paragraph1": "SpheroSeg ofrece una API RESTful para acceso programático a las funciones de la plataforma. Esto es ideal para la integración con sus flujos de trabajo existentes o procesamiento por lotes.", "endpoint1Desc": "Recupera una lista de todos sus proyectos", "endpoint2Desc": "Recupera todas las imágenes dentro de un proyecto específico", "endpoint3Desc": "Inicia la segmentación para una imagen específica", "contactPrefix": "Para la documentación completa de API y detalles de autenticación, por favor contáctenos en"}, "backToHome": "Volver al inicio", "backToTop": "Volver arriba"}, "hero": {"platformTag": "Plataforma avanzada de segmentación de esferoides", "title": "Análisis celular impulsado por IA para investigación biomédica", "subtitle": "Eleve su análisis de imágenes celulares microscópicas con nuestra plataforma de segmentación de esferoides de vanguardia. Diseñada para investigadores que buscan precisión y eficiencia.", "getStartedButton": "Comenzar", "learnMoreButton": "Más información", "imageAlt1": "Imagen microscópica de esferoide", "imageAlt2": "Imagen microscópica de esferoide con análisis", "welcomeTitle": "Bienvenido a SpheroSeg", "welcomeSubtitle": "Plataforma avanzada para segmentación y análisis de esferoides celulares", "welcomeDescription": "Nuestra plataforma combina algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para la detección y análisis precisos de esferoides celulares en imágenes microscópicas.", "featuresTitle": "Características poderosas", "featuresSubtitle": "Herramientas avanzadas para investigación biomédica", "featureAiSegmentation": "Segmentación avanzada", "featureAiSegmentationDesc": "Detección precisa de esferoides con análisis de límites para mediciones celulares exactas.", "featureEditing": "Análisis impulsado por IA", "featureEditingDesc": "Aproveche los algoritmos de aprendizaje profundo para la detección automatizada y clasificación celular.", "featureAnalytics": "Carga fácil", "featureAnalyticsDesc": "Arrastre y suelte sus imágenes microscópicas para procesamiento y análisis inmediatos.", "featureExport": "Perspectivas estadísticas", "featureExportDesc": "Métricas integrales y visualizaciones para extraer patrones de datos significativos.", "ctaTitle": "¿Listo para transformar su flujo de trabajo de análisis celular?", "ctaSubtitle": "Únase a los investigadores líderes que ya usan nuestra plataforma para acelerar sus descubrimientos.", "ctaButton": "<PERSON><PERSON><PERSON> cuenta"}, "navbar": {"home": "<PERSON><PERSON>o", "features": "Características", "documentation": "Documentación", "terms": "Térm<PERSON>s", "privacy": "Privacidad", "login": "In<PERSON><PERSON>", "requestAccess": "Solicitar acceso"}, "navigation": {"home": "<PERSON><PERSON>o", "projects": "Proyectos", "settings": "Configuración", "profile": "Perfil", "dashboard": "Panel de control", "back": "Atrás"}, "dashboard": {"manageProjects": "Administre y organice sus proyectos de investigación", "viewMode": {"grid": "Vista de cuadrícula", "list": "Vista de lista"}, "sort": {"name": "Nombre", "updatedAt": "Última actualización", "segmentationStatus": "Estado"}, "search": "Buscar proyectos...", "searchImagesPlaceholder": "Buscar imágenes...", "noProjects": "No se encontraron proyectos", "noImagesDescription": "No hay imágenes que coincidan con sus criterios de búsqueda", "createFirst": "Cree su primer proyecto para comenzar", "createNew": "Crear nuevo proyecto", "lastChange": "Último cambio", "statsOverview": "Resumen de estadísticas", "totalProjects": "Total de proyectos", "activeProjects": "Proyectos activos", "totalImages": "Total de imágenes", "totalAnalyses": "Total de análisis", "lastUpdated": "Última actualización", "noProjectsDescription": "Aún no ha creado ningún proyecto. Cree su primer proyecto para comenzar.", "searchProjectsPlaceholder": "Buscar proyectos por nombre...", "sortBy": "Ordenar por", "name": "Nombre", "completed": "Completado", "processing": "Procesando", "pending": "Pendiente", "failed": "Fallido", "selectImagesButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "projects": {"title": "Proyectos", "description": "Administre sus proyectos de investigación", "createNew": "Crear nuevo proyecto", "createProject": "<PERSON><PERSON>r proyecto", "createProjectDesc": "Cree un nuevo proyecto para comenzar a trabajar con imágenes y segmentación.", "projectName": "Nombre del proyecto", "projectDescription": "Descripción del proyecto", "projectNamePlaceholder": "Ingrese el nombre del proyecto", "projectDescriptionPlaceholder": "Ingrese la descripción del proyecto", "projectCreated": "Proyecto creado exitosamente", "projectCreationFailed": "Error al crear el proyecto", "projectDeleted": "Proyecto eliminado exitosamente", "deleteSuccess": "Proyecto eliminado exitosamente", "deleteFailed": "Error al eliminar el proyecto", "deleting": "Eliminando proyecto...", "notFound": "Proyecto no encontrado. Puede haber sido eliminado.", "missingId": "No se puede eliminar el proyecto: falta el identificador del proyecto", "projectDeletionFailed": "Error al eliminar el proyecto", "confirmDelete": "¿Está seguro de que desea eliminar este proyecto?", "confirmDeleteDescription": "Esta acción no se puede deshacer. Todos los datos asociados con este proyecto serán eliminados permanentemente.", "delete": "Eliminar", "deleteProject": "Eliminar proyecto", "deleteProjectDescription": "Esta acción no se puede deshacer. Esto eliminará permanentemente el proyecto y todos los datos asociados.", "deleteWarning": "Está a punto de eliminar el siguiente proyecto:", "typeToConfirm": "Escriba el nombre del proyecto para confirmar", "confirmDeleteError": "Por favor, escriba el nombre del proyecto exactamente para confirmar", "editProject": "Editar proyecto", "viewProject": "Ver proyecto", "projectUpdated": "Proyecto actualizado exitosamente", "projectUpdateFailed": "Error al actualizar el proyecto", "noProjects": "No se encontraron proyectos", "createFirstProject": "Cree su primer proyecto para comenzar", "searchProjects": "Buscar proyectos...", "filterProjects": "Filtrar proyectos", "sortProjects": "Ordenar proyectos", "projectNameRequired": "El nombre del proyecto es obligatorio", "loginRequired": "Debe iniciar sesión para crear un proyecto", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Última actualización", "imageCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Estado", "actions": "Acciones", "loading": "Cargando proyectos...", "error": "Error al cargar proyectos", "retry": "Reintentar", "duplicating": "Duplicando proyecto...", "duplicate": "Duplicar", "duplicateSuccess": "Proyecto duplicado exitosamente", "duplicateFailed": "Error al duplicar el proyecto", "duplicateTitle": "Duplicar proyecto", "duplicateProject": "Duplicar proyecto", "duplicateProjectDescription": "Crear una copia de este proyecto incluyendo todas las imágenes. Puede personalizar las opciones a continuación.", "duplicateCancelled": "Duplicación de proyecto cancelada", "duplicatingProject": "Duplicando proyecto", "duplicatingProjectDescription": "Su proyecto se está duplicando. Esto puede tomar unos momentos.", "duplicateProgress": "Progreso de duplicación", "duplicationComplete": "Duplicación de proyecto completada", "duplicationTaskFetchError": "Error al obtener datos de la tarea", "duplicationCancelError": "Error al cancelar la duplicación", "duplicateProgressDescription": "Su proyecto se está duplicando. Este proceso puede tomar tiempo para proyectos grandes.", "duplicationPending": "Pendiente", "duplicationProcessing": "Procesando", "duplicationCompleted": "Completado", "duplicationFailed": "Fallido", "duplicationCancelled": "Cancelado", "duplicationCancellationFailed": "Error al cancelar la duplicación", "duplicationSuccessMessage": "¡Proyecto duplicado exitosamente! Ahora puede acceder al nuevo proyecto.", "copySegmentations": "Copiar resultados de segmentación", "resetImageStatus": "Restablecer estado de procesamiento de imágenes", "newProjectTitle": "Nuevo título del proyecto", "itemsProcessed": "elementos procesados", "items": "elementos", "unknownProject": "Proyecto desconocido", "activeTasks": "Activas", "allTasks": "<PERSON><PERSON>", "noActiveDuplications": "Sin duplicaciones activas", "noDuplications": "No se encontraron tareas de duplicación", "untitledProject": "Proyecto sin título", "exportProject": "Exportar proyecto", "share": "Compartir", "export": "Exportar", "archived": "Archivado", "completed": "Completado", "draft": "<PERSON><PERSON><PERSON>", "active": "Activo"}, "projectToolbar": {"selectImages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelSelection": "<PERSON><PERSON><PERSON>", "export": "Exportar", "uploadImages": "<PERSON><PERSON>"}, "statsOverview": {"title": "Resumen del panel", "totalProjects": "Total de proyectos", "totalImages": "Total de imágenes", "completedSegmentations": "Segmentaciones completadas", "storageUsed": "Almacenamiento utilizado", "recentActivity": "Actividad reciente", "moreStats": "Ver estadísticas detalladas", "completion": "tasa de finalización", "vsLastMonth": "vs. mes pasado", "thisMonth": "<PERSON>ste mes", "lastMonth": "<PERSON><PERSON> pasado", "projectsCreated": "Proyectos creados", "imagesUploaded": "Imá<PERSON><PERSON> carga<PERSON>", "fetchError": "Error al cargar estadísticas", "storageLimit": "Límite de almacenamiento", "activityTitle": "Actividad reciente", "noActivity": "Sin actividad reciente", "hide": "Ocultar", "activityTypes": {"project_created": "Proyecto creado", "image_uploaded": "Imagen cargada", "segmentation_completed": "Segmentación completada"}}, "footer": {"developerName": "Bc<PERSON> <PERSON><PERSON>", "facultyName": "FNSPE CTU en Praga", "description": "Plataforma avanzada para segmentación y análisis de esferoides", "contactLabel": "<EMAIL>", "developerLabel": "Bc<PERSON> <PERSON><PERSON>", "facultyLabel": "FNSPE CTU en Praga", "resourcesTitle": "Recursos", "documentationLink": "Documentación", "featuresLink": "Características", "tutorialsLink": "Tu<PERSON>les", "researchLink": "Investigación", "legalTitle": "Información legal", "termsLink": "Términos de servicio", "privacyLink": "Política de privacidad", "contactUsLink": "Cont<PERSON><PERSON><PERSON>s", "informationTitle": "Información", "contactTitle": "Contacto", "copyrightNotice": "SpheroSeg. Todos los derechos reservados.", "madeWith": "<PERSON><PERSON> con", "by": "por", "requestAccessLink": "Solicitar acceso"}, "features": {"tag": "Características", "title": "Descubra las capacidades de nuestra plataforma", "subtitle": "Herramientas avanzadas para investigación biomédica", "cards": {"segmentation": {"title": "Segmentación avanzada", "description": "Detección precisa de esferoides con análisis de límites para mediciones celulares exactas"}, "aiAnalysis": {"title": "Análisis impulsado por IA", "description": "Aproveche los algoritmos de aprendizaje profundo para la detección y clasificación automatizada de células"}, "uploads": {"title": "Carga fácil", "description": "Arrastre y suelte sus imágenes microscópicas para procesamiento y análisis inmediatos"}, "insights": {"title": "Perspectivas estadísticas", "description": "Métricas integrales y visualizaciones para extraer patrones de datos significativos"}, "collaboration": {"title": "Colaboración en equipo", "description": "Comparta proyectos y resultados con colegas para una investigación más eficiente"}, "pipeline": {"title": "Canalización automatizada", "description": "Optimice su flujo de trabajo con nuestras herramientas de procesamiento por lotes"}}}, "index": {"about": {"tag": "Acerca de la plataforma", "title": "¿Qué es SpheroSeg?", "imageAlt": "Ejemplo de segmentación de esferoides", "paragraph1": "SpheroSeg es una plataforma avanzada diseñada específicamente para la segmentación y análisis de esferoides celulares en imágenes microscópicas.", "paragraph2": "Nuestra herramienta combina algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para proporcionar a los investigadores detección precisa de límites de esferoides y capacidades analíticas.", "paragraph3": "La plataforma fue desarrollada por Michal Průšek de FNSPE CTU en Praga bajo la supervisión de Adam Novozámský de UTIA CAS, en colaboración con investigadores del Departamento de Bioquímica y Microbiología de UCT Praga.", "contactPrefix": "<EMAIL>"}, "cta": {"title": "¿Listo para transformar su investigación?", "subtitle": "Comience a usar SpheroSeg hoy y descubra nuevas posibilidades en el análisis de esferoides celulares", "boxTitle": "Cree una cuenta gratuita", "boxText": "Obtenga acceso a todas las funciones de la plataforma y comience a analizar sus imágenes microscópicas", "button": "<PERSON><PERSON><PERSON> cuenta"}}, "tools": {"zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>", "resetView": "Restablecer vista", "createPolygon": "Crear nuevo polígono", "exitPolygonCreation": "Salir del modo de creación de polígono", "splitPolygon": "Dividir polí<PERSON> en dos", "exitSlicingMode": "Salir del modo de corte", "addPoints": "Agregar puntos al polígono", "exitPointAddingMode": "Salir del modo de agregar puntos", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "save": "Guardar", "resegment": "Resegmentar", "title": "Herramientas"}, "accessibility": {"skipToContent": "Saltar al contenido principal"}, "profile": {"title": "<PERSON><PERSON><PERSON><PERSON>", "about": "Acerca de", "activity": "Actividad", "projects": "Proyectos", "recentProjects": "Proyectos recientes", "recentAnalyses": "Análisis recientes", "accountDetails": "Detalles de la cuenta", "accountType": "Tipo de cuenta", "joinDate": "<PERSON>cha de registro", "lastActive": "Última actividad", "projectsCreated": "Proyectos creados", "imagesUploaded": "Imá<PERSON><PERSON> carga<PERSON>", "segmentationsCompleted": "Segmentaciones completadas", "pageTitle": "Perfil de usuario", "editProfile": "<PERSON><PERSON> perfil", "joined": "Se unió", "statistics": "Estadísticas", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "analyses": "<PERSON><PERSON><PERSON><PERSON>", "storageUsed": "Almacenamiento utilizado", "recentActivity": "Actividad reciente", "noRecentActivity": "Sin actividad reciente", "fetchError": "Error al cargar datos del perfil", "aboutMe": "Acerca de mí", "noBio": "Sin biografía proporcionada", "avatarHelp": "Haga clic en el icono de la cámara para cargar una foto de perfil", "avatarImageOnly": "Por favor, seleccione un archivo de imagen", "avatarTooLarge": "La imagen debe ser menor de 5MB", "avatarUpdated": "Foto de perfil actualizada", "avatarUploadError": "Error al cargar la foto de perfil", "avatarRemoved": "Foto de perfil eliminada", "avatarRemoveError": "Error al eliminar la foto de perfil", "cropAvatarDescription": "Ajuste el área de recorte para establecer su foto de perfil", "description": "Actualice su información personal y foto de perfil", "saveButton": "Guardar perfil", "username": "Nombre de usuario", "usernamePlaceholder": "Ingrese su nombre de usuario", "fullName": "Nombre completo", "fullNamePlaceholder": "Ingrese su nombre completo", "titlePlaceholder": "ej. <PERSON><PERSON><PERSON><PERSON><PERSON>, Profesor", "organization": "Organización", "organizationPlaceholder": "Ingrese su organización o institución", "bio": "Biografía", "bioPlaceholder": "Cuéntenos sobre usted", "bioDescription": "Una breve descripción sobre usted que será visible en su perfil", "location": "Ubicación", "locationPlaceholder": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, República Checa", "uploadAvatar": "Cargar foto de perfil", "removeAvatar": "Eliminar foto de perfil", "cropAvatar": "Recortar foto de perfil", "activityDescription": "Actividad del sistema", "email": "Correo electrónico", "notProvided": "No proporcionado"}, "termsPage": {"title": "Términos de servicio", "acceptance": {"title": "1. Aceptación de términos", "paragraph1": "Al acceder o usar SpheroSeg, acepta estar sujeto a estos Términos de servicio y todas las leyes y regulaciones aplicables. Si no está de acuerdo con alguno de estos términos, tiene prohibido usar este servicio."}, "useLicense": {"title": "2. Licencia de uso", "paragraph1": "Se otorga permiso para usar temporalmente SpheroSeg solo para fines personales, no comerciales o de investigación académica. Esta es la concesión de una licencia, no una transferencia de título."}, "dataUsage": {"title": "3. <PERSON><PERSON>", "paragraph1": "Todos los datos cargados en SpheroSeg siguen siendo de su propiedad. No reclamamos la propiedad de su contenido, pero requerimos ciertos permisos para proporcionar el servicio."}, "limitations": {"title": "4. Limitaciones", "paragraph1": "En ningún caso SpheroSeg será responsable por daños derivados del uso o la incapacidad de usar la plataforma, incluso si hemos sido advertidos de la posibilidad de tales daños."}, "revisions": {"title": "5. Revisiones y errores", "paragraph1": "Los materiales que aparecen en SpheroSeg podrían incluir errores técnicos, tipográficos o fotográficos. No garantizamos que ninguno de los materiales sea preciso, completo o actual."}, "governingLaw": {"title": "6. <PERSON><PERSON> aplicable", "paragraph1": "Estos términos se regirán e interpretarán de acuerdo con las leyes del país en el que se aloja el servicio, y usted se somete irrevocablemente a la jurisdicción exclusiva de los tribunales en esa ubicación."}}, "privacyPage": {"title": "Política de privacidad", "introduction": {"title": "1. Introducción", "paragraph1": "Esta Política de privacidad explica cómo SpheroSeg (\"nosotros\", \"nos\", \"nuestro\") recopila, usa y comparte su información cuando usa nuestra plataforma de segmentación y análisis de esferoides."}, "informationWeCollect": {"title": "2. Información que recopilamos", "paragraph1": "Recopilamos información que nos proporciona directamente cuando crea una cuenta, carga imágenes, crea proyectos y, de otro modo, interactúa con nuestros servicios."}, "personalInformation": {"title": "2.1 Información personal", "paragraph1": "Esto incluye su nombre, dirección de correo electrónico, institución/organización y otra información que proporciona al crear una cuenta o solicitar acceso a nuestros servicios."}, "researchData": {"title": "2.2 Datos de investigación", "paragraph1": "Esto incluye imágenes que carga, detalles del proyecto, resultados de análisis y otros datos relacionados con la investigación que crea o carga en nuestra plataforma."}, "usageInformation": {"title": "2.3 Información de uso", "paragraph1": "Recopilamos información sobre cómo usa nuestra plataforma, incluidos datos de registro, información del dispositivo y patrones de uso."}, "howWeUse": {"title": "3. Cómo usamos su información", "paragraph1": "Usamos la información que recopilamos para proporcionar, mantener y mejorar nuestros servicios, para comunicarnos con usted y para cumplir con nuestras obligaciones legales."}, "dataSecurity": {"title": "4. <PERSON><PERSON><PERSON><PERSON> <PERSON>", "paragraph1": "Implementamos medidas de seguridad apropiadas para proteger su información personal y datos de investigación contra acceso no autorizado, alteración, divulgación o destrucción."}, "dataSharing": {"title": "5. <PERSON><PERSON><PERSON><PERSON>", "paragraph1": "No vendemos su información personal o datos de investigación. Podemos compartir su información en circunstancias limitadas, como con su consentimiento, para cumplir con obligaciones legales o con proveedores de servicios que nos ayudan a operar nuestra plataforma."}, "yourChoices": {"title": "6. Sus opciones", "paragraph1": "<PERSON><PERSON><PERSON> acceder, actualizar o eliminar la información de su cuenta y los datos de investigación a través de la configuración de su cuenta. También puede contactarnos para solicitar acceso, corrección o eliminación de cualquier información personal que tengamos sobre usted."}, "changes": {"title": "7. Cambios a esta política", "paragraph1": "Podemos actualizar esta Política de privacidad de vez en cuando. Le notificaremos cualquier cambio publicando la nueva Política de privacidad en esta página y actualizando la fecha de \"Última actualización\"."}, "contactUs": {"title": "8. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paragraph1": "Si tiene alguna pregunta sobre esta Política de privacidad, contá<NAME_EMAIL>."}, "lastUpdated": "Última actualización: 1 de julio de 2023"}, "shortcuts": {"button": "<PERSON><PERSON><PERSON>", "editMode": "Cambiar al modo de edición", "sliceMode": "Cambiar al modo de corte", "addPointMode": "Cambiar al modo de agregar puntos", "holdShift": "Mantenga Shift para agregar puntos automáticamente (en modo de edición)", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "deletePolygon": "Eliminar polígono <PERSON>", "cancel": "Cancelar operación actual", "zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>", "resetView": "Restablecer vista", "title": "Atajos de teclado", "viewMode": "Modo de visualización", "editVerticesMode": "Modo de editar vértices", "addPointsMode": "Modo de agregar puntos", "createPolygonMode": "<PERSON><PERSON> de crear p<PERSON>", "save": "Guardar", "description": "Estos atajos funcionan dentro del editor de segmentación para un trabajo más rápido y cómodo."}, "imageProcessor": {"segmentationStarted": "El proceso de segmentación ha comenzado...", "startSegmentationTooltip": "Iniciar segmentación", "processingTooltip": "Procesando...", "savingTooltip": "Guardando...", "completedTooltip": "Segmentación completada", "retryTooltip": "Reintentar segmentación"}, "uploader": {"dragDrop": "Arrastre y suelte imágenes aquí o haga clic para seleccionar archivos", "dropFiles": "Suelte archivos aquí...", "segmentAfterUploadLabel": "Segmentar imágenes inmediatamente después de cargar", "filesToUpload": "Archivos para cargar", "uploadBtn": "<PERSON><PERSON>", "uploadError": "Ocurrió un error durante la carga. Por favor, intente de nuevo.", "clickToUpload": "Haga clic para explorar archivos", "selectProjectLabel": "Seleccionar proyecto", "selectProjectPlaceholder": "Seleccionar proyecto...", "noProjectsFound": "No se encontraron proyectos. Cree uno nuevo primero.", "imageOnly": "(Solo archivos de imagen)", "uploadingImages": "Cargando imágenes...", "uploadComplete": "Carga completada", "uploadFailed": "Carga fallida", "processingImages": "Procesando <PERSON>...", "dragAndDropFiles": "Arrastre y suelte archivos aquí", "or": "o", "clickToSelect": "Haga clic para seleccionar archivos"}, "images": {"uploadImages": "<PERSON><PERSON>", "dragDrop": "Arrastre y suelte imágenes aquí", "clickToSelect": "o haga clic para seleccionar archivos", "acceptedFormats": "Formatos admitidos: JPEG, PNG, TIFF, BMP (máx. 10MB)", "uploadProgress": "Progreso de carga", "uploadingTo": "Cargando a", "currentProject": "Proyecto actual", "autoSegment": "Segmentar automáticamente las imágenes después de cargar", "uploadCompleted": "Carga completada", "uploadFailed": "Carga fallida", "imagesUploaded": "Imágenes cargadas exitosamente", "imagesFailed": "Error al cargar imágenes", "viewAnalyses": "<PERSON><PERSON>", "noAnalysesYet": "Sin análisis aún", "runAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "viewResults": "Ver resultados", "dropImagesHere": "<PERSON>lte imágenes aquí...", "selectProjectFirst": "Por favor, seleccione un proyecto primero", "projectRequired": "Debe seleccionar un proyecto antes de cargar imágenes", "imageOnly": "(Solo archivos de imagen)", "dropFiles": "Suelte archivos aquí...", "filesToUpload": "Archivos para cargar ({{count}})", "uploadBtn": "<PERSON><PERSON> {{count}} imágenes", "uploadError": "Ocurrió un error durante la carga. Por favor, intente de nuevo.", "noProjectsToUpload": "No hay proyectos disponibles. Cree un proyecto primero.", "notFound": "Proyecto \"{{projectName}}\" no encontrado. Puede haber sido eliminado."}, "export": {"formatDescriptions": {"COCO": "Formato JSON Common Objects in Context (COCO) para detección de objetos", "YOLO": "Formato de texto You Only Look Once (YOLO) para detección de objetos", "MASK": "Imágenes de máscara binaria para cada objeto segmentado", "POLYGONS": "Coordenadas de polígono en formato JSON"}, "exportCompleted": "Exportación completada", "exportFailed": "Exportación fallida", "title": "Exportar datos de segmentación", "spheroidMetrics": "Métricas de esferoides", "visualization": "Visualización", "cocoFormat": "Formato COCO", "close": "<PERSON><PERSON><PERSON>", "metricsExported": "Métricas exportadas exitosamente", "options": {"includeMetadata": "Incluir metadatos", "includeSegmentation": "Incluir segmentación", "selectExportFormat": "Seleccionar formato de exportación", "includeObjectMetrics": "Incluir métricas de objetos", "selectMetricsFormat": "Seleccionar formato de métricas", "metricsFormatDescription": {"EXCEL": "Archivo Excel (.xlsx)", "CSV": "Archivo CSV (.csv)"}, "includeImages": "Incluir imágenes originales", "exportMetricsOnly": "Exportar solo métricas", "metricsRequireSegmentation": "La exportación de métricas requiere segmentación completada"}, "formats": {"COCO": "COCO JSON", "YOLO": "YOLO TXT", "MASK": "Máscara (TIFF)", "POLYGONS": "Polígonos (JSON)"}, "metricsFormats": {"EXCEL": "Excel (.xlsx)", "CSV": "CSV (.csv)"}, "selectImagesForExport": "Seleccionar imágenes para exportar"}, "metrics": {"area": "Á<PERSON>", "perimeter": "Perímetro", "circularity": "Circularidad", "sphericity": "Esfericidad", "solidity": "Solidez", "compactness": "Compacidad", "convexity": "Convexidad", "visualization": "Visualización de métricas", "visualizationHelp": "Representación visual de métricas para todos los esferoides en esta imagen", "barChart": "Gráfico de barras", "pieChart": "Gráfico circular", "comparisonChart": "Gráfico de comparación", "keyMetricsComparison": "Comparación de métricas clave", "areaDistribution": "Distribución de área", "shapeMetricsComparison": "Comparación de métricas de forma", "noPolygonsFound": "No se encontraron polígonos para análisis"}, "imageStatus": {"completed": "Procesado", "processing": "Procesando", "pending": "Pendiente", "failed": "Fallido", "noImage": "<PERSON> imagen", "untitledImage": "Imagen sin título"}, "projectActions": {"duplicateTooltip": "Duplicar proyecto", "deleteTooltip": "Eliminar proyecto", "deleteConfirmTitle": "¿E<PERSON>á seguro?", "deleteConfirmDesc": "¿Está seguro de que desea eliminar el proyecto \"{{projectName}}\"? Esta acción no se puede deshacer.", "deleteSuccess": "El proyecto \"{{projectName}}\" ha sido eliminado exitosamente.", "deleteError": "Error al eliminar el proyecto.", "duplicateSuccess": "El proyecto \"{{projectName}}\" ha sido duplicado exitosamente.", "duplicateError": "Error al duplicar el proyecto.", "makePrivateTooltip": "Marcar como privado", "makePublicTooltip": "Marcar como público", "shareTooltip": "Compartir proyecto", "downloadTooltip": "Des<PERSON><PERSON> proyecto", "notFound": "Proyecto \"{{projectName}}\" no encontrado. Puede haber sido eliminado."}, "editor": {"backButtonTooltip": "Volver a la vista general del proyecto", "exportButtonTooltip": "Exportar datos de segmentación actuales", "saveTooltip": "Guardar cambios", "image": "Imagen", "previousImage": "Imagen anterior", "nextImage": "<PERSON><PERSON> sigu<PERSON>e", "resegmentButton": "Resegmentar", "resegmentButtonTooltip": "Ejecutar segmentación nuevamente en esta imagen", "exportMaskButton": "Exportar máscara", "exportMaskButtonTooltip": "Exportar máscara de segmentación para esta imagen", "backButton": "Atrás", "exportButton": "Exportar", "saveButton": "Guardar", "loadingProject": "Cargando proyecto...", "loadingImage": "Cargando imagen...", "sliceErrorInvalidPolygon": "No se puede cortar: Polígono seleccionado inválido.", "sliceWarningInvalidResult": "El corte creó polígonos muy pequeños e inválidos.", "sliceWarningInvalidIntersections": "Corte inválido: La línea de corte debe intersectar el polígono en exactamente dos puntos.", "sliceSuccess": "Polígono cortado exitosamente.", "noPolygonToSlice": "No hay polígonos disponibles para cortar.", "savingTooltip": "Guardando..."}, "segmentationPage": {"noImageSelected": "No se seleccionó ninguna imagen para resegmentación.", "resegmentationStarted": "Iniciando resegmentación usando red neuronal ResUNet...", "resegmentationQueued": "La resegmentación ha sido puesta en cola.", "resegmentationCompleted": "Resegmentación completada exitosamente.", "resegmentationFailed": "La resegmentación falló.", "resegmentationTimeout": "Tiempo de espera agotado para resegmentación. Verifique el estado de la cola.", "resegmentationError": "Error al iniciar la resegmentación.", "resegmentTooltip": "Resegmentar"}, "share": {"accepted": "<PERSON><PERSON><PERSON>", "alreadyShared": "Ya compartido con este usuario", "canEdit": "<PERSON><PERSON><PERSON>ar", "copyToClipboard": "Copiar al portapapeles", "edit": "<PERSON><PERSON>", "email": "Correo electrónico", "failedToCopy": "Error al copiar enlace", "failedToGenerateLink": "Error al generar enlace para compartir", "failedToLoadShares": "Error al cargar usuarios compartidos", "failedToRemove": "Error al eliminar compartición", "failedToShare": "Error al compartir proyecto", "generateLink": "<PERSON><PERSON> enlace", "generateNewLink": "Generar nuevo enlace", "generating": "Generando...", "invalidEmail": "Dirección de correo electrónico inválida", "invalidEmailOrPermission": "Correo electrónico o permiso inválido", "invite": "Invitar", "inviteByEmail": "Invitar por correo electrónico", "inviteByLink": "<PERSON><PERSON>tar por enlace", "linkCopied": "Enlace copiado al portapapeles", "linkGenerated": "Enlace para compartir generado", "linkPermissions": "Permis<PERSON> del enlace", "noPermission": "Sin permiso", "noShares": "Sin usuarios compartidos", "pendingAcceptance": "Pendiente de aceptación", "permissions": "<PERSON><PERSON><PERSON>", "projectNotFound": "Proyecto no encontrado", "removeShare": "Eliminar compartición", "selectAccessLevel": "Seleccionar nivel de acceso", "selectPermission": "Por favor, seleccione un tipo de permiso", "shareDescription": "Compartir este proyecto con otros usuarios", "sharedWith": "Compartido con", "shareLinkDescription": "<PERSON>ual<PERSON>er persona con este enlace puede acceder al proyecto", "shareProject": "Compartir proyecto", "shareProjectTitle": "Compartir proyecto \"{{projectName}}\"", "sharing": "Compartiendo...", "sharedSuccess": "El proyecto \"{{projectName}}\" ha sido compartido con {{email}}", "removedSuccess": "Se ha eliminado la compartición con {{email}}", "status": "Estado", "userEmail": "Correo del usuario", "view": "<PERSON>er", "viewOnly": "Solo ver"}}