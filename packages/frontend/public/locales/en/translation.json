{"common": {"appName": "Spheroid Segmentation", "appNameShort": "SpheroSeg", "loading": "Loading...", "loadingAccount": "Loading your account...", "loadingApplication": "Loading application...", "selectAll": "Select all", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "error": "Error", "success": "Success", "reset": "Reset", "clear": "Clear", "close": "Close", "back": "Back", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "signingIn": "Signing In...", "settings": "Settings", "profile": "Profile", "dashboard": "Dashboard", "project": "Project", "projects": "Projects", "newProject": "New Project", "upload": "Upload", "download": "Download", "removeAll": "Remove All", "uploadImages": "Upload Images", "recentAnalyses": "Recent Analyses", "noProjects": "No projects found", "noImages": "No images found", "createYourFirst": "Create your first project to get started", "tryAgain": "Try Again", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "name": "Name", "description": "Description", "date": "Date", "status": "Status", "image": "Image", "projectName": "Project Name", "projectDescription": "Project Description", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "welcome": "Welcome to the Spheroid Segmentation Platform", "account": "Account", "passwordConfirm": "Confirm Password", "manageAccount": "Manage Account", "changePassword": "Change Password", "deleteAccount": "Delete Account", "requestAccess": "Request Access", "accessRequest": "Access Request", "createAccount": "Create Account", "signInToAccount": "Sign In to Account", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "termsOfServiceLink": "Terms of Service", "privacyPolicyLink": "Privacy Policy", "optional": "Optional", "saveChanges": "Save Changes", "saving": "Saving", "notSpecified": "Not Specified", "enable": "Enable", "disable": "Disable", "backToHome": "Back to Home", "and": "and", "lastChange": "Last Change", "sort": "Sort", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "export": "Export", "selectImages": "Select Images", "noImagesDescription": "Upload images to get started with your project", "yes": "Yes", "no": "No", "images": "Images", "files": "Files", "validationFailed": "Validation failed", "cropAvatar": "Crop Profile Picture", "profileTitle": "Profile", "profileDescription": "Update your profile information visible to other users", "profileUsername": "Username", "profileUsernamePlaceholder": "Enter your username", "profileFullName": "Full Name", "profileFullNamePlaceholder": "Enter your full name", "profileTitlePlaceholder": "e.g. <PERSON><PERSON>, Professor", "profileOrganization": "Organization", "profileOrganizationPlaceholder": "Enter your organization or institution", "profileBio": "Bio", "profileBioPlaceholder": "Write a short bio about yourself", "profileBioDescription": "Brief description of your research interests and expertise", "profileLocation": "Location", "profileLocationPlaceholder": "e.g. Prague, Czech Republic", "profileSaveButton": "Save Profile", "actions": "Actions", "view": "View", "share": "Share", "projectNamePlaceholder": "Enter project name", "projectDescPlaceholder": "Enter project description", "creatingProject": "Creating project...", "createSuccess": "Project created successfully", "unauthorized": "You are not authorized to perform this action", "forbidden": "Access forbidden", "maxFileSize": "Max file size: {{size}}MB", "accepted": "Accepted", "processing": "Processing...", "uploading": "Uploading...", "uploadComplete": "Upload completed", "uploadFailed": "Upload failed", "deletePolygon": "Delete polygon", "editor": {"error": "Error", "success": "Success", "edit": "Edit", "create": "Create"}}, "settings": {"title": "Settings", "pageTitle": "Settings", "profile": "Profile", "account": "Account", "appearance": "Appearance", "profileSettings": "Profile Settings", "accountSettings": "Account <PERSON><PERSON>", "securitySettings": "Security Settings", "preferenceSettings": "Preference Settings", "selectLanguage": "Select Language", "selectTheme": "Select Theme", "updateProfile": "Update Profile", "changePassword": "Change Password", "deleteAccount": "Delete Account", "savedChanges": "Changes saved successfully", "saveChanges": "Save Changes", "profileUpdated": "Profile updated successfully", "languageSettings": "Language Settings", "themeSettings": "Theme Settings", "privacySettings": "Privacy Settings", "exportData": "Export Data", "importData": "Import Data", "uploadAvatar": "Upload Profile Picture", "removeAvatar": "Remove Profile Picture", "twoFactorAuth": "Two-Factor Authentication", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "weeklyDigest": "Weekly Digest", "monthlyReport": "Monthly Report", "displaySettings": "Display Settings", "accessibilitySettings": "Accessibility Settings", "advancedSettings": "Advanced Settings", "useBrowserLanguage": "Use browser language", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "languageUpdated": "Language updated successfully", "themeUpdated": "Theme updated successfully", "toggleTheme": "Toggle theme", "languageDescription": "Choose your preferred language", "themeDescription": "Choose your preferred theme", "profileLoadError": "Failed to load profile", "appearanceDescription": "Customize the appearance of the application", "personal": "Personal Information", "fullName": "Full Name", "organization": "Organization", "department": "Department", "publicProfile": "Public Profile", "makeProfileVisible": "Make my profile visible to other researchers", "passwordSettings": "Password Settings", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "dangerZone": "Danger Zone", "deleteAccountWarning": "Once you delete your account, there is no going back. All your data will be permanently deleted.", "savingChanges": "Saving changes...", "savePreferences": "Save Preferences", "usernameTaken": "This username is already taken", "deleteAccountDescription": "This action is irreversible. All your data will be permanently deleted.", "confirmUsername": "Confirm your email", "password": "Password", "enterPassword": "Enter your password", "passwordChangeError": "Error changing password", "passwordChangeSuccess": "Password changed successfully", "passwordsDoNotMatch": "Passwords do not match", "accountDeleteSuccess": "Account deleted successfully", "accountDeleteError": "Error deleting account", "passwordChanged": "Password changed", "confirmPasswordLabel": "Confirm Password", "changePasswordDescription": "Change your password to keep your account secure", "dangerZoneDescription": "These actions are irreversible and will permanently remove your data", "deletingAccount": "Deleting account...", "deleteAccountError": "Error deleting account"}, "segmentation": {"contextMenu": {"editPolygon": "Edit polygon", "splitPolygon": "Split polygon", "deletePolygon": "Delete polygon", "confirmDeleteTitle": "Are you sure you want to delete polygon?", "confirmDeleteMessage": "This action is irreversible. The polygon will be permanently removed from segmentation.", "duplicateVertex": "Duplicate vertex", "deleteVertex": "Delete vertex"}, "title": "Segmentation Editor", "resolution": "{width}x{height}", "queue": {"title": "Segmentation Queue", "summary": "{{total}} tasks total ({{running}} processing, {{queued}} queued)", "noRunningTasks": "No running tasks", "noQueuedTasks": "No queued tasks", "task": "Task", "statusRunning": "Segmentation: {{count}} running{{queued}}", "statusQueued": ", {{count}} queued", "statusOnlyQueued": "Segmentation: {{count}} queued", "statusOnlyQueued_one": "Segmentation: 1 queued", "statusOnlyQueued_other": "Segmentation: {{count}} queued", "processing": "Processing", "queued": "Queued", "statusProcessing": "Segmentation: {{count}} processing", "statusReady": "Ready", "tasksTotal": "{{total}} tasks total ({{running}} processing, {{queued}} queued)"}, "selectPolygonForEdit": "Select a polygon to edit", "selectPolygonForSlice": "Select a polygon to slice", "selectPolygonForAddPoints": "Select a polygon to add points", "clickToAddPoint": "Click to add a point", "clickToCompletePolygon": "<PERSON>lick on the first point to complete the polygon", "clickToAddFirstSlicePoint": "Click to add the first slice point", "clickToAddSecondSlicePoint": "Click to add the second slice point", "polygonCreationMode": "Polygon Creation Mode", "polygonEditMode": "Polygon Edit Mode", "polygonSliceMode": "Polygon Slice Mode", "polygonAddPointsMode": "Add Points Mode", "viewMode": "View Mode", "totalPolygons": "Total Polygons", "totalVertices": "Total Vertices", "vertices": "Vertices", "zoom": "Zoom", "mode": "Mode", "selected": "Selected", "none": "None", "polygons": "Polygons", "imageNotFound": "Image not found", "returnToProject": "Return to project", "backToProject": "Back to project", "previousImage": "Previous image", "nextImage": "Next image", "toggleShortcuts": "Toggle shortcuts", "modes": {"view": "View Mode", "edit": "Edit Mode", "create": "Create Mode", "slice": "Slice Mode", "addPoints": "Add Points Mode", "deletePolygon": "Delete Polygon Mode", "createPolygon": "Create Polygon Mode", "editVertices": "Edit Vertices Mode", "editMode": "Edit Mode", "slicingMode": "Slicing Mode", "pointAddingMode": "Point Adding Mode"}, "status": {"processing": "Processing", "queued": "Queued", "completed": "Completed", "failed": "Failed", "pending": "Pending"}, "autoSave": {"enabled": "Auto-save: Enabled", "disabled": "Auto-save: Disabled", "idle": "Auto-save: Idle", "pending": "Pending...", "saving": "Saving...", "success": "Saved", "error": "Error"}, "loading": "Loading segmentation...", "polygon": "Polygon", "unsavedChanges": "Unsaved changes", "noData": "No segmentation data available", "noPolygons": "No polygons found", "regions": "Segmentation", "position": "Position", "polygonDeleted": "Polygon successfully deleted", "saveSuccess": "Segmentation saved successfully", "resegmentSuccess": "Resegmentation started successfully", "resegmentComplete": "Resegmentation completed successfully", "resegmentError": "Failed to resegment image", "resegmentButton": "Resegment", "completedSegmentation": "Completed", "resegmentButtonTooltip": "Resegment with Neural Network", "helpTips": {"title": "Tips:", "edit": {"createPoint": "Click to create a new point", "shiftPoints": "Hold Shift to automatically create a sequence of points", "closePolygon": "Close the polygon by clicking on the first point"}, "slice": {"start": "Click to start slice", "finish": "Click again to finish slice", "cancel": "Esc to cancel slicing"}, "addPoint": {"hover": "Hover over polygon line", "click": "Click to add point to selected polygon", "exit": "Esc to exit add mode"}}}, "errors": {"somethingWentWrong": "Something went wrong", "componentError": "An error occurred in this component", "errorDetails": "<PERSON><PERSON><PERSON>", "tryAgain": "Try again", "reloadPage": "Reload Page", "goBack": "Go back", "notFound": "Page not found", "pageNotFoundMessage": "The page you requested could not be found", "returnToHome": "Return to Home", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "serverError": "Server error", "networkError": "Network error", "timeoutError": "Request timed out", "validationError": "Validation error", "unknownError": "Unknown error", "goHome": "Go to home page", "fetchSegmentationFailed": "Failed to fetch segmentation", "fetchImageFailed": "Failed to fetch image", "saveSegmentationFailed": "Failed to save segmentation", "missingPermissions": "Insufficient permissions", "invalidInput": "Invalid input", "resourceNotFound": "Resource not found"}, "project": {"detail": {"noImagesSelected": "No images selected", "triggeringResegmentation": "Triggering re-segmentation for {{count}} images...", "deleteConfirmation": "Are you sure you want to delete {{count}} images? This action cannot be undone.", "deletingImages": "Deleting {{count}} images...", "deleteSuccess": "Successfully deleted {{count}} images", "deleteFailed": "Failed to delete {{count}} images", "preparingExport": "Preparing export of {{count}} images..."}, "segmentation": {"processingInBatches": "Starting segmentation for {{count}} images in {{batches}} batches...", "batchQueued": "Batch {{current}}/{{total}} successfully queued", "batchQueuedFallback": "Batch {{current}}/{{total}} successfully queued (fallback endpoint)", "batchError": "Error processing batch {{current}}/{{total}}", "partialSuccess": "Segmentation: {{success}} images successfully queued, {{failed}} failed", "allSuccess": "Segmentation: All {{count}} images successfully queued", "allFailed": "Segmentation: All {{count}} images failed", "startedImages": "Segmentation started for {{count}} images", "queuedLocallyWarning": "Segmentation queued locally for {{count}} images. Server connection failed."}, "loading": "Loading project...", "notFound": "Project not found", "error": "Error loading project", "empty": "This project is empty", "noImages": {"title": "No Images Yet", "description": "This project doesn't have any images yet. Upload images to get started.", "uploadButton": "Upload Images"}, "addImages": "Add images to get started", "deleteProject": "Delete Project", "deleteConfirmation": "Are you sure you want to delete the project \"{{projectName}}\"? This action cannot be undone.", "duplicateProject": "Duplicate Project", "duplicateDescription": "Create a copy of this project. The new project will be created with the name you provide below.", "newProjectName": "New Project Name", "enterProjectName": "Enter the new project name", "duplicate": "Duplicate"}, "projectsPage": {"title": "Projects", "description": "Manage research projects", "createNew": "Create new project", "createProject": "Create project", "createProjectDesc": "Start a new research project", "projectName": "Project name", "projectDescription": "Project description", "projectNamePlaceholder": "Enter project name", "projectDescriptionPlaceholder": "Enter project description", "projectCreated": "Project successfully created", "projectCreationFailed": "Project creation failed", "projectDeleted": "Project successfully deleted", "projectDeletionFailed": "Project deletion failed", "confirmDelete": "Are you sure you want to delete this project?", "confirmDeleteDescription": "This action cannot be undone. All data associated with this project will be permanently deleted.", "deleteProject": "Delete project", "editProject": "Edit project", "viewProject": "View project", "projectUpdated": "Project successfully updated", "projectUpdateFailed": "Project update failed", "noProjects": "No projects found", "createFirstProject": "Create your first project to get started", "searchProjects": "Search projects...", "filterProjects": "Filter projects", "sortProjects": "Sort projects", "projectNameRequired": "Project name is required", "loginRequired": "You must be logged in to create a project", "createdAt": "Created", "updatedAt": "Last updated", "imageCount": "Images", "status": "Status", "actions": "Actions", "loading": "Loading projects...", "error": "Error loading projects", "retry": "Try again", "duplicating": "Duplicating project...", "duplicate": "Duplicate", "duplicateSuccess": "Project successfully duplicated", "duplicateFailed": "Failed to duplicate project", "duplicateTitle": "Duplicate project", "duplicateProject": "Duplicate project", "duplicateProjectDescription": "Create a copy of this project including all images. You can customize the options below.", "duplicateCancelled": "Project duplication cancelled", "duplicatingProject": "Duplicating project", "duplicatingProjectDescription": "Your project is being duplicated. This may take a few moments.", "duplicateProgress": "Duplication progress", "duplicationComplete": "Project duplication completed", "duplicationTaskFetchError": "Error fetching task data", "duplicationCancelError": "Error cancelling duplication", "duplicateProgressDescription": "Your project is being duplicated. This process may take some time for large projects.", "duplicationPending": "Pending", "duplicationProcessing": "Processing", "duplicationCompleted": "Completed", "duplicationFailed": "Failed", "duplicationCancelled": "Cancelled", "duplicationCancellationFailed": "Failed to cancel duplication", "duplicationSuccessMessage": "Project successfully duplicated! You can now access the new project.", "copySegmentations": "Copy segmentation results", "resetImageStatus": "Reset image processing status", "newProjectTitle": "New project title", "itemsProcessed": "items processed", "items": "items", "unknownProject": "Unknown project", "activeTasks": "Active", "allTasks": "All", "noActiveDuplications": "No active duplications", "noDuplications": "No duplication tasks found", "deleteProjectDescription": "This action will permanently delete the project and all associated data.", "deleteWarning": "This action cannot be undone. All data associated with this project will be permanently deleted.", "untitledProject": "Untitled project", "typeToConfirm": "Type \"delete\" to confirm", "deleteConfirm": "Are you sure you want to delete this project?", "exportProject": "Export project", "archived": "Archived", "completed": "Completed", "draft": "Draft", "active": "Active", "createDate": "Created", "lastModified": "Last modified", "projectDescPlaceholder": "Enter project description", "creatingProject": "Creating project...", "noImages": {"title": "No images yet", "description": "This project doesn't have any images yet. Upload images to start segmentation.", "uploadButton": "Upload Images"}}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "signingIn": "Signing In...", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "signInWithGoogle": "Sign In with Google", "signInWithGithub": "Sign In with GitHub", "or": "or", "signInTitle": "Sign In", "signInDescription": "Sign in to your account", "noAccount": "Don't have an account?", "emailAddressLabel": "Email address", "passwordLabel": "Password", "currentPasswordLabel": "Current password", "newPasswordLabel": "New password", "confirmPasswordLabel": "Confirm password", "rememberMe": "Remember me", "emailRequired": "Email is required", "passwordRequired": "Password is required", "alreadyLoggedInTitle": "You're already logged in", "alreadyLoggedInMessage": "You are already logged in to your account", "goToDashboardLink": "Go to Dashboard", "invalidEmail": "Invalid email address", "passwordTooShort": "Password must be at least 6 characters", "passwordsDontMatch": "Passwords don't match", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "resetLinkSent": "Password reset link sent to your email", "resetSuccess": "Password reset successfully", "signInSuccess": "Signed in successfully", "signOutSuccess": "Signed out successfully", "sessionExpired": "Your session has expired. Please sign in again.", "unauthorized": "You are not authorized to access this resource", "verifyEmail": "Please verify your email address", "verificationLinkSent": "Verification link sent to your email", "verificationSuccess": "Email verified successfully", "resendVerification": "Resend verification email", "requestAccess": "Request Access", "termsAndPrivacy": "By signing up, you agree to our Terms of Service and Privacy Policy.", "forgotPasswordLink": "Forgot password?", "passwordChanged": "Password changed successfully", "currentPasswordIncorrect": "Current password is incorrect", "registerTitle": "Create Account", "registerDescription": "Sign up for a new account", "registerSuccess": "Registration successful! You can now sign in.", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "firstNamePlaceholder": "e.g. <PERSON>", "lastNamePlaceholder": "e.g. <PERSON>", "passwordConfirmPlaceholder": "Confirm your password", "signUpTitle": "Create Account", "signUpDescription": "Sign up for a new account", "enterInfoCreateAccount": "Enter your information to create an account", "creatingAccount": "Creating Account...", "emailAlreadyExists": "This email is already registered. Please use a different email or sign in.", "emailHasPendingRequest": "This email already has a pending access request. Please wait for approval.", "signUpSuccessEmail": "Registration successful! Please check your email or wait for admin approval.", "signUpFailed": "Registration failed. Please try again.", "alreadyHaveAccess": "Already have access?", "forgotPasswordTitle": "Reset Your Password", "checkYourEmail": "Check your email for a new password", "enterEmailForReset": "Enter your email address and we will send you a new password", "passwordResetLinkSent": "If an account exists for this email, a new password has been sent", "passwordResetFailed": "Failed to send new password. Please try again.", "enterEmail": "Please enter your email address", "sendingResetLink": "Sending new password...", "sendResetLink": "Send New Password", "backToSignIn": "Back to Sign In"}, "requestAccess": {"and": "and", "title": "Request Access to Spheroid Segmentation Platform", "description": "Fill out the following form to request access to our platform. We will review your request and contact you soon.", "emailLabel": "Your Email Address", "nameLabel": "Your Name", "institutionLabel": "Institution/Company", "reasonLabel": "Reason for Access", "submitRequest": "Submit Request", "requestReceived": "Request Received", "thankYou": "Thank you for your interest", "weWillContact": "We will review your request and contact you soon", "submitSuccess": "Request submitted successfully!", "emailPlaceholder": "Enter your email address", "namePlaceholder": "Enter your full name", "institutionPlaceholder": "Enter your institution or company name", "reasonPlaceholder": "Please describe how you plan to use the platform", "fillRequired": "Please fill in all required fields", "submittingRequest": "Submitting Request...", "submitError": "Failed to submit request", "alreadyPending": "An access request for this email is already pending", "agreeToTerms": "By submitting this request, you agree to our"}, "requestAccessForm": {"title": "Request Access to Spheroid Segmentation Platform", "description": "Fill out the following form to request access to our platform. We will review your request and contact you soon.", "emailLabel": "Your Email Address", "nameLabel": "Your Name", "institutionLabel": "Institution/Company", "reasonLabel": "Reason for Access", "submitButton": "Submit Request", "signInPrompt": "Already have an account?", "signInLink": "Sign In", "thankYouTitle": "Thank you for your interest", "weWillContact": "We will review your request and contact you soon", "agreeToTerms": "By submitting this request, you agree to our", "and": "and"}, "documentation": {"tag": "User Guide", "title": "SpheroSeg Documentation", "subtitle": "Learn how to use the Spheroid Segmentation Platform effectively.", "sidebar": {"title": "Sections", "introduction": "Introduction", "gettingStarted": "Getting Started", "uploadingImages": "Uploading Images", "segmentationProcess": "Segmentation Process", "apiReference": "API Reference"}, "introduction": {"title": "Introduction", "imageAlt": "Illustration of spheroid analysis workflow", "whatIs": {"title": "What is SpheroSeg?", "paragraph1": "SpheroSeg is a cutting-edge platform designed for the segmentation and analysis of cell spheroids in microscopic images. Our tool provides researchers with precise detection and analytical capabilities.", "paragraph2": "It utilizes advanced AI algorithms based on deep learning to automatically identify and segment spheroids in your images with high accuracy and consistency.", "paragraph3": "This documentation will guide you through all aspects of using the platform, from getting started to advanced features and API integration."}}, "gettingStarted": {"title": "Getting Started", "accountCreation": {"title": "Account Creation", "paragraph1": "To use SpheroSeg, you need to create an account. This allows us to securely store your projects and images.", "step1Prefix": "Visit the", "step1Link": "sign-up page", "step2": "Enter your institutional email address and create a password", "step3": "Complete your profile with your name and institution", "step4": "Verify your email address via the link sent to your inbox"}, "creatingProject": {"title": "Creating Your First Project", "paragraph1": "Projects help you organize your work. Each project can contain multiple images and their corresponding segmentation results.", "step1": "On your dashboard, click on \"New Project\"", "step2": "Enter a project name and description", "step3": "Select project type (default: Spheroid Analysis)", "step4": "Click \"Create Project\" to continue"}}, "uploadingImages": {"title": "Uploading Images", "paragraph1": "SpheroSeg supports various image formats commonly used in microscopy, including TIFF, PNG, and JPEG.", "methods": {"title": "Upload Methods", "paragraph1": "There are several ways to upload images:", "step1": "Drag and drop files directly into the upload area", "step2": "Click on the upload area to browse and select files from your computer", "step3": "Batch upload multiple images at once"}, "note": {"prefix": "Note:", "text": "For optimal results, ensure your microscopy images have good contrast between the spheroid and background."}}, "segmentationProcess": {"title": "Segmentation Process", "paragraph1": "The segmentation process identifies the boundaries of spheroids in your images, allowing for precise analysis of their morphology.", "automatic": {"title": "Automatic Segmentation", "paragraph1": "Our AI-powered automatic segmentation can detect spheroid boundaries with high accuracy:", "step1": "Select an image from your project", "step2": "Click on \"Auto-Segment\" to initiate the process", "step3": "The system will process the image and display the detected boundaries", "step4": "Review the results in the segmentation editor"}, "manual": {"title": "Manual Adjustments", "paragraph1": "Sometimes automatic segmentation may require refinement. Our editor provides tools for:", "step1": "Adding or removing vertices along the boundary", "step2": "Adjusting vertex positions for more accurate boundaries", "step3": "Splitting or merging regions", "step4": "Adding or removing holes within spheroids"}}, "apiReference": {"title": "API Reference", "paragraph1": "SpheroSeg offers a RESTful API for programmatic access to the platform's features. This is ideal for integration with your existing workflows or batch processing.", "endpoint1Desc": "Retrieves a list of all your projects", "endpoint2Desc": "Retrieves all images within a specific project", "endpoint3Desc": "Initiates segmentation for a specific image", "contactPrefix": "For full API documentation and authentication details, please contact us at"}, "backToHome": "Back to Home", "backToTop": "Back to Top"}, "hero": {"platformTag": "Advanced Spheroid Segmentation Platform", "title": "AI-powered Cell Analysis for Biomedical Research", "subtitle": "Elevate your microscopic cell image analysis with our cutting-edge spheroid segmentation platform. Designed for researchers seeking precision and efficiency.", "getStartedButton": "Get Started", "learnMoreButton": "Learn More", "imageAlt1": "Spheroid microscopy image", "imageAlt2": "Spheroid microscopy image with analysis", "welcomeTitle": "Welcome to SpheroSeg", "welcomeSubtitle": "Advanced platform for cell spheroid segmentation and analysis", "welcomeDescription": "Our platform combines cutting-edge artificial intelligence algorithms with an intuitive interface for precise detection and analysis of cell spheroids in microscopic images.", "featuresTitle": "Powerful Features", "featuresSubtitle": "Advanced tools for biomedical research", "featureAiSegmentation": "Advanced Segmentation", "featureAiSegmentationDesc": "Precise spheroid detection with boundary analysis for accurate cell measurements.", "featureEditing": "AI-powered Analysis", "featureEditingDesc": "Leverage deep learning algorithms for automated detection and cell classification.", "featureAnalytics": "Easy Uploading", "featureAnalyticsDesc": "Drag and drop your microscopy images for immediate processing and analysis.", "featureExport": "Statistical Insights", "featureExportDesc": "Comprehensive metrics and visualizations for extracting meaningful data patterns.", "ctaTitle": "Ready to transform your cell analysis workflow?", "ctaSubtitle": "Join leading researchers already using our platform to accelerate their discoveries.", "ctaButton": "Create Account"}, "navbar": {"home": "Home", "features": "Features", "documentation": "Documentation", "terms": "Terms", "privacy": "Privacy", "login": "Sign In", "requestAccess": "Request Access"}, "navigation": {"home": "Home", "projects": "Projects", "settings": "Settings", "profile": "Profile", "dashboard": "Dashboard", "back": "Back"}, "dashboard": {"manageProjects": "Manage and organize your research projects", "viewMode": {"grid": "Grid View", "list": "List View"}, "sort": {"name": "Name", "updatedAt": "Last Updated", "segmentationStatus": "Status"}, "search": "Search projects...", "searchImagesPlaceholder": "Search images...", "noProjects": "No projects found", "noImagesDescription": "No images match your search criteria", "createFirst": "Create your first project to get started", "createNew": "Create New Project", "lastChange": "Last Change", "statsOverview": "Stats Overview", "totalProjects": "Total Projects", "activeProjects": "Active Projects", "totalImages": "Total Images", "totalAnalyses": "Total Analyses", "lastUpdated": "Last Updated", "noProjectsDescription": "You haven't created any projects yet. Create your first project to get started.", "searchProjectsPlaceholder": "Search projects by name...", "sortBy": "Sort by", "name": "Name", "completed": "Completed", "processing": "Processing", "pending": "Pending", "failed": "Failed", "selectImagesButton": "Select Images"}, "projects": {"title": "Projects", "description": "Manage your research projects", "createNew": "Create New Project", "createProject": "Create Project", "createProjectDesc": "Create a new project to start working with images and segmentation.", "projectName": "Project Name", "projectDescription": "Project Description", "projectNamePlaceholder": "Enter project name", "projectDescriptionPlaceholder": "Enter project description", "projectCreated": "Project created successfully", "projectCreationFailed": "Failed to create project", "projectDeleted": "Project deleted successfully", "deleteSuccess": "Project deleted successfully", "deleteFailed": "Failed to delete project", "deleting": "Deleting project...", "notFound": "Project not found. It may have been deleted already.", "missingId": "Cannot delete project: missing project identifier", "projectDeletionFailed": "Failed to delete project", "confirmDelete": "Are you sure you want to delete this project?", "confirmDeleteDescription": "This action cannot be undone. All data associated with this project will be permanently deleted.", "delete": "Delete", "deleteProject": "Delete Project", "deleteProjectDescription": "This action cannot be undone. This will permanently delete the project and all associated data.", "deleteWarning": "You are about to delete the following project:", "typeToConfirm": "Type the project name to confirm", "confirmDeleteError": "Please type the project name exactly to confirm", "editProject": "Edit Project", "viewProject": "View Project", "projectUpdated": "Project updated successfully", "projectUpdateFailed": "Failed to update project", "noProjects": "No projects found", "createFirstProject": "Create your first project to get started", "searchProjects": "Search projects...", "filterProjects": "Filter projects", "sortProjects": "Sort projects", "projectNameRequired": "Project name is required", "loginRequired": "You must be logged in to create a project", "createdAt": "Created", "updatedAt": "Last updated", "imageCount": "Images", "status": "Status", "actions": "Actions", "loading": "Loading projects...", "error": "Error loading projects", "retry": "Retry", "duplicating": "Duplicating project...", "duplicate": "Duplicate", "duplicateSuccess": "Project duplicated successfully", "duplicateFailed": "Failed to duplicate project", "duplicateTitle": "Duplicate Project", "duplicateProject": "Duplicate Project", "duplicateProjectDescription": "Create a copy of this project including all images. You can customize the options below.", "duplicateCancelled": "Project duplication cancelled", "duplicatingProject": "Duplicating Project", "duplicatingProjectDescription": "Your project is being duplicated. This may take a few moments.", "duplicateProgress": "Duplication Progress", "duplicationComplete": "Project duplication completed", "duplicationTaskFetchError": "Error fetching task data", "duplicationCancelError": "Error cancelling duplication", "duplicateProgressDescription": "Your project is being duplicated. This process may take some time for large projects.", "duplicationPending": "Pending", "duplicationProcessing": "Processing", "duplicationCompleted": "Completed", "duplicationFailed": "Failed", "duplicationCancelled": "Cancelled", "duplicationCancellationFailed": "Failed to cancel duplication", "duplicationSuccessMessage": "Project duplicated successfully! You can now access the new project.", "copySegmentations": "Copy segmentation results", "resetImageStatus": "Reset image processing status", "newProjectTitle": "New Project Title", "itemsProcessed": "items processed", "items": "items", "unknownProject": "Unknown Project", "activeTasks": "Active", "allTasks": "All", "noActiveDuplications": "No active duplications", "noDuplications": "No duplication tasks found", "untitledProject": "Untitled Project", "exportProject": "Export Project", "share": "Share", "export": "Export", "archived": "Archived", "completed": "Completed", "draft": "Draft", "active": "Active"}, "projectToolbar": {"selectImages": "Select Images", "cancelSelection": "Cancel Selection", "export": "Export", "uploadImages": "Upload Images"}, "statsOverview": {"title": "Dashboard Overview", "totalProjects": "Total Projects", "totalImages": "Total Images", "completedSegmentations": "Completed Segmentations", "storageUsed": "Storage Used", "recentActivity": "Recent Activity", "moreStats": "View Detailed Statistics", "completion": "completion rate", "vsLastMonth": "vs. last month", "thisMonth": "This Month", "lastMonth": "Last Month", "projectsCreated": "Projects Created", "imagesUploaded": "Images Uploaded", "fetchError": "Failed to load statistics", "storageLimit": "Storage Limit", "activityTitle": "Recent Activity", "noActivity": "No recent activity", "hide": "<PERSON>de", "activityTypes": {"project_created": "Created project", "image_uploaded": "Uploaded image", "segmentation_completed": "Completed segmentation"}}, "footer": {"developerName": "Bc<PERSON> <PERSON><PERSON>", "facultyName": "FNSPE CTU in Prague", "description": "Advanced platform for spheroid segmentation and analysis", "contactLabel": "<EMAIL>", "developerLabel": "Bc<PERSON> <PERSON><PERSON>", "facultyLabel": "FNSPE CTU in Prague", "resourcesTitle": "Resources", "documentationLink": "Documentation", "featuresLink": "Features", "tutorialsLink": "Tutorials", "researchLink": "Research", "legalTitle": "Legal Information", "termsLink": "Terms of Service", "privacyLink": "Privacy Policy", "contactUsLink": "Contact Us", "informationTitle": "Information", "contactTitle": "Contact", "copyrightNotice": "SpheroSeg. All rights reserved.", "madeWith": "Made with", "by": "by", "requestAccessLink": "Request Access"}, "features": {"tag": "Features", "title": "Discover Our Platform Capabilities", "subtitle": "Advanced tools for biomedical research", "cards": {"segmentation": {"title": "Advanced Segmentation", "description": "Precise spheroid detection with boundary analysis for accurate cell measurements"}, "aiAnalysis": {"title": "AI-powered Analysis", "description": "Leverage deep learning algorithms for automated cell detection and classification"}, "uploads": {"title": "Easy Uploading", "description": "Drag and drop your microscopy images for immediate processing and analysis"}, "insights": {"title": "Statistical Insights", "description": "Comprehensive metrics and visualizations to extract meaningful data patterns"}, "collaboration": {"title": "Team Collaboration", "description": "Share projects and results with colleagues for more efficient research"}, "pipeline": {"title": "Automated Pipeline", "description": "Streamline your workflow with our batch processing tools"}}}, "index": {"about": {"tag": "About the Platform", "title": "What is SpheroSeg?", "imageAlt": "Spheroid segmentation example", "paragraph1": "SpheroSeg is an advanced platform specifically designed for the segmentation and analysis of cell spheroids in microscopic images.", "paragraph2": "Our tool combines cutting-edge artificial intelligence algorithms with an intuitive interface to provide researchers with precise spheroid boundary detection and analytical capabilities.", "paragraph3": "The platform was developed by <PERSON><PERSON> from FNSPE CTU in Prague under the supervision of <PERSON> from UTIA CAS, in collaboration with researchers from the Department of Biochemistry and Microbiology at UCT Prague.", "contactPrefix": "<EMAIL>"}, "cta": {"title": "Ready to transform your research?", "subtitle": "Start using SpheroSeg today and discover new possibilities in cell spheroid analysis", "boxTitle": "Create a free account", "boxText": "Get access to all platform features and start analyzing your microscopy images", "button": "Create Account"}}, "tools": {"zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetView": "Reset View", "createPolygon": "Create New Polygon", "exitPolygonCreation": "Exit Polygon Creation Mode", "splitPolygon": "Split Polygon in Two", "exitSlicingMode": "Exit Slicing Mode", "addPoints": "Add Points to Polygon", "exitPointAddingMode": "Exit Point Adding Mode", "undo": "Undo", "redo": "Redo", "save": "Save", "resegment": "Resegment", "title": "Tools"}, "accessibility": {"skipToContent": "Skip to main content"}, "profile": {"title": "Title", "about": "About", "activity": "Activity", "projects": "Projects", "recentProjects": "Recent Projects", "recentAnalyses": "Recent Analyses", "accountDetails": "Account Details", "accountType": "Account Type", "joinDate": "Join Date", "lastActive": "Last Active", "projectsCreated": "Projects Created", "imagesUploaded": "Images Uploaded", "segmentationsCompleted": "Completed Segmentations", "pageTitle": "User Profile", "editProfile": "Edit Profile", "joined": "Joined", "statistics": "Statistics", "images": "Images", "analyses": "Analyses", "storageUsed": "Storage Used", "recentActivity": "Recent Activity", "noRecentActivity": "No recent activity", "fetchError": "Failed to load profile data", "aboutMe": "About Me", "noBio": "No bio provided", "avatarHelp": "Click the camera icon to upload a profile picture", "avatarImageOnly": "Please select an image file", "avatarTooLarge": "Image must be smaller than 5MB", "avatarUpdated": "Profile picture updated", "avatarUploadError": "Failed to upload profile picture", "avatarRemoved": "Profile picture removed", "avatarRemoveError": "Failed to remove profile picture", "cropAvatarDescription": "Adjust the cropping area to set your profile picture", "description": "Update your personal information and profile picture", "saveButton": "Save Profile", "username": "Username", "usernamePlaceholder": "Enter your username", "fullName": "Full Name", "fullNamePlaceholder": "Enter your full name", "titlePlaceholder": "e.g. <PERSON><PERSON>, Professor", "organization": "Organization", "organizationPlaceholder": "Enter your organization or institution", "bio": "Bio", "bioPlaceholder": "Tell us about yourself", "bioDescription": "A brief description about you that will be visible on your profile", "location": "Location", "locationPlaceholder": "e.g. Prague, Czech Republic", "uploadAvatar": "Upload Profile Picture", "removeAvatar": "Remove Profile Picture", "cropAvatar": "Crop Profile Picture", "activityDescription": "System activity", "email": "Email", "notProvided": "Not provided"}, "termsPage": {"title": "Terms of Service", "acceptance": {"title": "1. Acceptance of Terms", "paragraph1": "By accessing or using SpheroSeg, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using this service."}, "useLicense": {"title": "2. Use License", "paragraph1": "Permission is granted to temporarily use SpheroSeg for personal, non-commercial, or academic research purposes only. This is the grant of a license, not a transfer of title."}, "dataUsage": {"title": "3. Data Usage", "paragraph1": "All data uploaded to SpheroSeg remains your property. We do not claim ownership of your content, but we require certain permissions to provide the service."}, "limitations": {"title": "4. Limitations", "paragraph1": "In no event shall SpheroSeg be liable for any damages arising from the use or inability to use the platform, even if we have been advised of the possibility of such damage."}, "revisions": {"title": "5. Revisions and Errors", "paragraph1": "The materials appearing on SpheroSeg could include technical, typographical, or photographic errors. We do not warrant that any of the materials are accurate, complete or current."}, "governingLaw": {"title": "6. Governing Law", "paragraph1": "These terms shall be governed and construed in accordance with the laws of the country in which the service is hosted, and you irrevocably submit to the exclusive jurisdiction of the courts in that location."}}, "privacyPage": {"title": "Privacy Policy", "introduction": {"title": "1. Introduction", "paragraph1": "This Privacy Policy explains how SpheroSeg (\"we\", \"us\", \"our\") collects, uses, and shares your information when you use our spheroid segmentation and analysis platform."}, "informationWeCollect": {"title": "2. Information We Collect", "paragraph1": "We collect information that you directly provide to us when you create an account, upload images, create projects, and otherwise interact with our services."}, "personalInformation": {"title": "2.1 Personal Information", "paragraph1": "This includes your name, email address, institution/organization, and other information you provide when creating an account or requesting access to our services."}, "researchData": {"title": "2.2 Research Data", "paragraph1": "This includes images you upload, project details, analysis results, and other research-related data that you create or upload to our platform."}, "usageInformation": {"title": "2.3 Usage Information", "paragraph1": "We collect information about how you use our platform, including log data, device information, and usage patterns."}, "howWeUse": {"title": "3. How We Use Your Information", "paragraph1": "We use the information we collect to provide, maintain, and improve our services, to communicate with you, and to fulfill our legal obligations."}, "dataSecurity": {"title": "4. Data Security", "paragraph1": "We implement appropriate security measures to protect your personal information and research data from unauthorized access, alteration, disclosure, or destruction."}, "dataSharing": {"title": "5. Data Sharing", "paragraph1": "We do not sell your personal information or research data. We may share your information under limited circumstances, such as with your consent, to fulfill legal obligations, or with service providers who help us operate our platform."}, "yourChoices": {"title": "6. Your Choices", "paragraph1": "You can access, update, or delete your account information and research data through your account settings. You may also contact us to request access, correction, or deletion of any personal information that we have about you."}, "changes": {"title": "7. Changes to this Policy", "paragraph1": "We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the \"Last Updated\" date."}, "contactUs": {"title": "8. Contact Us", "paragraph1": "If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>."}, "lastUpdated": "Last updated: July 1, 2023"}, "shortcuts": {"button": "Shortcuts", "editMode": "Switch to Edit Mode", "sliceMode": "Switch to Slice Mode", "addPointMode": "Switch to Add Point Mode", "holdShift": "Hold Shift to auto-add points (in Edit Mode)", "undo": "Undo", "redo": "Redo", "deletePolygon": "Delete selected polygon", "cancel": "Cancel current operation", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "resetView": "Reset view", "title": "Keyboard Shortcuts", "viewMode": "View Mode", "editVerticesMode": "Edit Vertices Mode", "addPointsMode": "Add Points Mode", "createPolygonMode": "Create Polygon Mode", "save": "Save", "description": "These shortcuts work within the segmentation editor for faster and more comfortable work."}, "imageProcessor": {"segmentationStarted": "Segmentation process has started...", "startSegmentationTooltip": "Start Segmentation", "processingTooltip": "Processing...", "savingTooltip": "Saving...", "completedTooltip": "Segmentation completed", "retryTooltip": "Retry segmentation"}, "uploader": {"dragDrop": "Drag and drop images here or click to select files", "dropFiles": "Drop files here...", "segmentAfterUploadLabel": "Segment images immediately after upload", "filesToUpload": "Files to upload", "uploadBtn": "Upload", "uploadError": "An error occurred during upload. Please try again.", "clickToUpload": "Click to browse files", "selectProjectLabel": "Select project", "selectProjectPlaceholder": "Select project...", "noProjectsFound": "No projects found. Create a new one first.", "imageOnly": "(Image files only)", "uploadingImages": "Uploading images...", "uploadComplete": "Upload complete", "uploadFailed": "Upload failed", "processingImages": "Processing images...", "dragAndDropFiles": "Drag and drop files here", "or": "or", "clickToSelect": "Click to select files"}, "images": {"uploadImages": "Upload Images", "dragDrop": "Drag and drop images here", "clickToSelect": "or click to select files", "acceptedFormats": "Supported formats: JPEG, PNG, TIFF, BMP (max 10MB)", "uploadProgress": "Upload Progress", "uploadingTo": "Uploading to", "currentProject": "Current Project", "autoSegment": "Automatically segment images after upload", "uploadCompleted": "Upload Completed", "uploadFailed": "Upload Failed", "imagesUploaded": "Images uploaded successfully", "imagesFailed": "Image upload failed", "viewAnalyses": "View Analyses", "noAnalysesYet": "No analyses yet", "runAnalysis": "Run Analysis", "viewResults": "View Results", "dropImagesHere": "Drop images here...", "selectProjectFirst": "Please select a project first", "projectRequired": "You must select a project before uploading images", "imageOnly": "(Image files only)", "dropFiles": "Drop files here...", "filesToUpload": "Files to upload ({{count}})", "uploadBtn": "Upload {{count}} images", "uploadError": "An error occurred during upload. Please try again.", "noProjectsToUpload": "No projects available. Create a project first.", "notFound": "Project \"{{projectName}}\" not found. It may have been deleted."}, "export": {"formatDescriptions": {"COCO": "Common Objects in Context (COCO) JSON format for object detection", "YOLO": "You Only Look Once (YOLO) text format for object detection", "MASK": "Binary mask images for each segmented object", "POLYGONS": "Polygon coordinates in JSON format"}, "exportCompleted": "Export completed", "exportFailed": "Export failed", "title": "Export Segmentation Data", "spheroidMetrics": "Spheroid Metrics", "visualization": "Visualization", "cocoFormat": "COCO Format", "close": "Close", "metricsExported": "Metrics exported successfully", "options": {"includeMetadata": "Include metadata", "includeSegmentation": "Include segmentation", "selectExportFormat": "Select export format", "includeObjectMetrics": "Include object metrics", "selectMetricsFormat": "Select metrics format", "metricsFormatDescription": {"EXCEL": "Excel file (.xlsx)", "CSV": "CSV file (.csv)"}, "includeImages": "Include original images", "exportMetricsOnly": "Export metrics only", "metricsRequireSegmentation": "Exporting metrics requires completed segmentation"}, "formats": {"COCO": "COCO JSON", "YOLO": "YOLO TXT", "MASK": "Mask (TIFF)", "POLYGONS": "Polygons (JSON)"}, "metricsFormats": {"EXCEL": "Excel (.xlsx)", "CSV": "CSV (.csv)"}, "selectImagesForExport": "Select images for export"}, "metrics": {"area": "Area", "perimeter": "Perimeter", "circularity": "Circularity", "sphericity": "Sphericity", "solidity": "Solidity", "compactness": "Compactness", "convexity": "Convexity", "visualization": "Metrics Visualization", "visualizationHelp": "Visual representation of metrics for all spheroids in this image", "barChart": "Bar Chart", "pieChart": "Pie Chart", "comparisonChart": "Comparison Chart", "keyMetricsComparison": "Key Metrics Comparison", "areaDistribution": "Area Distribution", "shapeMetricsComparison": "<PERSON><PERSON><PERSON> Metrics Comparison", "noPolygonsFound": "No polygons found for analysis"}, "imageStatus": {"completed": "Processed", "processing": "Processing", "pending": "Pending", "failed": "Failed", "noImage": "No image", "untitledImage": "Untitled image"}, "projectActions": {"duplicateTooltip": "Duplicate project", "deleteTooltip": "Delete project", "deleteConfirmTitle": "Are you sure?", "deleteConfirmDesc": "Are you sure you want to delete the project \"{{projectName}}\"? This action cannot be undone.", "deleteSuccess": "Project \"{{projectName}}\" has been successfully deleted.", "deleteError": "Project deletion failed.", "duplicateSuccess": "Project \"{{projectName}}\" has been successfully duplicated.", "duplicateError": "Project duplication failed.", "makePrivateTooltip": "<PERSON> as private", "makePublicTooltip": "Mark as public", "shareTooltip": "Share project", "downloadTooltip": "Download project", "notFound": "Project \"{{projectName}}\" not found. It may have already been deleted."}, "editor": {"backButtonTooltip": "Back to project overview", "exportButtonTooltip": "Export current segmentation data", "saveTooltip": "Save changes", "image": "Image", "previousImage": "Previous image", "nextImage": "Next image", "resegmentButton": "Resegment", "resegmentButtonTooltip": "Run segmentation again on this image", "exportMaskButton": "Export mask", "exportMaskButtonTooltip": "Export segmentation mask for this image", "backButton": "Back", "exportButton": "Export", "saveButton": "Save", "loadingProject": "Loading project...", "loadingImage": "Loading image...", "sliceErrorInvalidPolygon": "Cannot slice: Invalid polygon selected.", "sliceWarningInvalidResult": "Slicing created polygons that are too small and invalid.", "sliceWarningInvalidIntersections": "Invalid slice: Slice line must intersect the polygon at exactly two points.", "sliceSuccess": "Polygon successfully sliced.", "noPolygonToSlice": "No polygons available to slice.", "savingTooltip": "Saving..."}, "segmentationPage": {"noImageSelected": "No image selected for resegmentation.", "resegmentationStarted": "Starting resegmentation using ResUNet neural network...", "resegmentationQueued": "Resegmentation has been queued.", "resegmentationCompleted": "Resegmentation completed successfully.", "resegmentationFailed": "Resegmentation failed.", "resegmentationTimeout": "Resegmentation timed out. Check queue status.", "resegmentationError": "Failed to start resegmentation.", "resegmentTooltip": "Resegment"}, "share": {"accepted": "Accepted", "alreadyShared": "Already shared with this user", "canEdit": "Can edit", "copyToClipboard": "Copy to clipboard", "edit": "Edit", "email": "Email", "failedToCopy": "Failed to copy link", "failedToGenerateLink": "Failed to generate share link", "failedToLoadShares": "Failed to load shared users", "failedToRemove": "Failed to remove share", "failedToShare": "Failed to share project", "generateLink": "Generate link", "generateNewLink": "Generate new link", "generating": "Generating...", "invalidEmail": "Invalid email address", "invalidEmailOrPermission": "Invalid email or permission", "invite": "Invite", "inviteByEmail": "Invite by email", "inviteByLink": "Invite by link", "linkCopied": "Link copied to clipboard", "linkGenerated": "Share link generated", "linkPermissions": "Link permissions", "noPermission": "No permission", "noShares": "No shared users", "pendingAcceptance": "Pending acceptance", "permissions": "Permissions", "projectNotFound": "Project not found", "removeShare": "Remove share", "selectAccessLevel": "Select access level", "selectPermission": "Please select a permission type", "shareDescription": "Share this project with other users", "sharedWith": "Shared with", "shareLinkDescription": "Anyone with this link can access the project", "shareProject": "Share Project", "shareProjectTitle": "Share project \"{{projectName}}\"", "sharing": "Sharing...", "sharedSuccess": "Project \"{{projectName}}\" has been shared with {{email}}", "removedSuccess": "Share with {{email}} has been removed", "status": "Status", "userEmail": "User email", "view": "View", "viewOnly": "View only"}}