{"common": {"appName": "Sphäroid-Segmentierung", "appNameShort": "SpheroSeg", "loading": "Wird geladen...", "loadingAccount": "<PERSON>hr Konto wird geladen...", "loadingApplication": "Anwendung wird geladen...", "selectAll": "Alle auswählen", "save": "Speichern", "cancel": "Abbrechen", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "error": "<PERSON><PERSON>", "success": "Erfolg", "reset": "Z<PERSON>ücksetzen", "clear": "<PERSON><PERSON>", "close": "Schließen", "back": "Zurück", "signIn": "Anmelden", "signUp": "Registrieren", "signOut": "Abmelden", "signingIn": "Anmeldung läuft...", "settings": "Einstellungen", "profile": "Profil", "dashboard": "Übersicht", "project": "Projekt", "projects": "Projekte", "newProject": "Neues Projekt", "upload": "Hochladen", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeAll": "Alle entfernen", "uploadImages": "Bilder hochladen", "recentAnalyses": "Aktuel<PERSON>", "noProjects": "<PERSON>ine Projekte gefunden", "noImages": "<PERSON><PERSON> Bilder gefunden", "createYourFirst": "<PERSON>rstellen Sie Ihr erstes Projekt, um zu beginnen", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "email": "E-Mail", "password": "Passwort", "confirmPassword": "Passwort bestätigen", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "username": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "description": "Beschreibung", "date": "Datum", "status": "Status", "image": "Bild", "projectName": "Projektname", "projectDescription": "Projektbeschreibung", "language": "<PERSON><PERSON><PERSON>", "theme": "<PERSON>a", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "system": "System", "welcome": "Willkommen bei der Sphäroid-Segmentierungsplattform", "account": "Ko<PERSON>", "passwordConfirm": "Passwort bestätigen", "manageAccount": "<PERSON><PERSON> ver<PERSON>ten", "changePassword": "Passwort ändern", "deleteAccount": "Konto löschen", "requestAccess": "<PERSON><PERSON><PERSON>", "accessRequest": "Zugangsanfrage", "createAccount": "<PERSON><PERSON> er<PERSON>", "signInToAccount": "Bei Konto anmelden", "termsOfService": "Nutzungsbedingungen", "privacyPolicy": "Datenschutzrichtlinie", "termsOfServiceLink": "Nutzungsbedingungen", "privacyPolicyLink": "Datenschutzrichtlinie", "optional": "Optional", "saveChanges": "Änderungen speichern", "saving": "Speichern", "notSpecified": "Nicht angegeben", "enable": "Aktivieren", "disable": "Deaktivieren", "backToHome": "Zurück zur Startseite", "and": "und", "lastChange": "Letzte Änderung", "sort": "<PERSON><PERSON><PERSON><PERSON>", "emailPlaceholder": "Geben Sie Ihre E-Mail ein", "passwordPlaceholder": "Geben Sie Ihr Passwort ein", "export": "Exportieren", "selectImages": "Bilder auswählen", "noImagesDescription": "Laden Sie Bilder hoch, um mit Ihrem Projekt zu beginnen", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "images": "Bilder", "files": "<PERSON><PERSON>", "validationFailed": "Validierung fehlgeschlagen", "cropAvatar": "Profilbild zuschneiden", "profileTitle": "Profil", "profileDescription": "Aktualisieren Sie Ihre für andere Benutzer sichtbaren Profilinformationen", "profileUsername": "<PERSON><PERSON><PERSON><PERSON>", "profileUsernamePlaceholder": "<PERSON><PERSON>en Sie Ihren Benutzernamen ein", "profileFullName": "Vollständiger Name", "profileFullNamePlaceholder": "Geben Sie Ihren vollständigen Namen ein", "profileTitlePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, Professor", "profileOrganization": "Organisation", "profileOrganizationPlaceholder": "Geben Sie Ihre Organisation oder Institution ein", "profileBio": "Bio", "profileBioPlaceholder": "Schreiben Sie eine kurze Bio über sich", "profileBioDescription": "Kurze Beschreibung Ihrer Forschungsinteressen und Expertise", "profileLocation": "<PERSON><PERSON>", "profileLocationPlaceholder": "z.B. Berlin, Deutschland", "profileSaveButton": "<PERSON><PERSON>", "actions": "Aktionen", "view": "<PERSON><PERSON><PERSON>", "share": "Teilen", "projectNamePlaceholder": "Projektnamen eingeben", "projectDescPlaceholder": "Projektbeschreibung eingeben", "creatingProject": "Projekt wird erstellt...", "createSuccess": "Projekt erfolgreich erstellt", "unauthorized": "Sie sind nicht berechtigt, diese Aktion durchzuführen", "forbidden": "<PERSON><PERSON><PERSON> verweigert", "maxFileSize": "<PERSON><PERSON>: {{size}}MB", "accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "processing": "Wird verarbeitet...", "uploading": "Wird hochgeladen...", "uploadComplete": "Upload abgeschlossen", "uploadFailed": "Upload fehlgeschlagen", "deletePolygon": "Polygon löschen", "editor": {"error": "<PERSON><PERSON>", "success": "Erfolg", "edit": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>"}}, "auth": {"signIn": "Anmelden", "signUp": "Registrieren", "signOut": "Abmelden", "signingIn": "Anmeldung läuft...", "forgotPassword": "Passwort vergessen?", "resetPassword": "Passwort zurücksetzen", "dontHaveAccount": "<PERSON>ben <PERSON> kein Konto?", "alreadyHaveAccount": "Haben <PERSON> bereits ein Konto?", "createAccount": "<PERSON><PERSON> er<PERSON>", "signInWithGoogle": "Mit Google anmelden", "signInWithGithub": "Mit GitHub anmelden", "or": "oder", "signInTitle": "<PERSON><PERSON><PERSON><PERSON>", "signInDescription": "Melden Sie sich bei Ihrem <PERSON> an", "noAccount": "<PERSON>ben <PERSON> kein Konto?", "emailAddressLabel": "E-Mail-Adresse", "passwordLabel": "Passwort", "currentPasswordLabel": "Aktuelles Passwort", "newPasswordLabel": "Neues Passwort", "confirmPasswordLabel": "Passwort bestätigen", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "emailRequired": "E-Mail ist erforderlich", "passwordRequired": "Passwort ist erforderlich", "alreadyLoggedInTitle": "Sie sind bereits angemeldet", "alreadyLoggedInMessage": "Sie sind bereits bei Ihrem Konto angemeldet", "goToDashboardLink": "Zur Übersicht", "invalidEmail": "Ungültige E-Mail-Adresse", "passwordTooShort": "Passwort muss mindestens 6 <PERSON>eichen lang sein", "passwordsDontMatch": "Passwörter stimmen nicht überein", "invalidCredentials": "Ungültige E-Mail oder Passwort", "accountCreated": "Konto erfolgreich erstellt", "resetLinkSent": "Link zum Zurücksetzen des Passworts wurde an Ihre E-Mail gesendet", "resetSuccess": "Passwort erfolgreich zurückgesetzt", "signInSuccess": "Erfolgreich angemeldet", "signOutSuccess": "Erfolgreich abgemeldet", "sessionExpired": "Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.", "unauthorized": "<PERSON>e sind nicht berechtigt, auf diese Ressource zuzugreifen", "verifyEmail": "Bitte verifizieren Sie Ihre E-Mail-Adresse", "verificationLinkSent": "Verifizierungslink wurde an Ihre E-Mail gesendet", "verificationSuccess": "E-Mail erfolgreich verifiziert", "resendVerification": "Verifizierungs-E-Mail erneut senden", "requestAccess": "<PERSON><PERSON><PERSON>", "termsAndPrivacy": "Mit der Registrierung stimmen Sie unseren Nutzungsbedingungen und Datenschutzrichtlinien zu.", "forgotPasswordLink": "Passwort vergessen?", "passwordChanged": "Passwort erfolgreich geändert", "currentPasswordIncorrect": "Aktuelles Passwort ist falsch", "registerTitle": "<PERSON><PERSON> er<PERSON>", "registerDescription": "Registrieren Sie sich für ein neues Konto", "registerSuccess": "Registrierung erfolgreich! Sie können sich jetzt anmelden.", "emailPlaceholder": "Geben Sie Ihre E-Mail ein", "passwordPlaceholder": "Geben Sie Ihr Passwort ein", "firstNamePlaceholder": "z.B. Max", "lastNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "passwordConfirmPlaceholder": "Bestätigen Sie Ihr Passwort", "signUpTitle": "<PERSON><PERSON> er<PERSON>", "signUpDescription": "Registrieren Sie sich für ein neues Konto", "enterInfoCreateAccount": "<PERSON><PERSON>en Sie Ihre Informationen ein, um ein Konto zu erstellen", "creatingAccount": "Konto wird erstellt...", "emailAlreadyExists": "Diese E-Mail ist bereits registriert. Bitte verwenden Si<PERSON> eine andere E-Mail oder melden Sie sich an.", "emailHasPendingRequest": "Diese E-Mail hat bereits eine ausstehende Zugangsanfrage. Bitte warten Si<PERSON> auf die Genehmigung.", "signUpSuccessEmail": "Registrierung erfolgreich! Bitte überprüfen Sie Ihre E-Mail oder warten Sie auf die Genehmigung durch den Administrator.", "signUpFailed": "Registrierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "alreadyHaveAccess": "Haben Sie bereits Zugang?", "forgotPasswordTitle": "Passwort zurücksetzen", "checkYourEmail": "Überprüfen Sie Ihre E-Mail für ein neues Passwort", "enterEmailForReset": "Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen ein neues Passwort", "passwordResetLinkSent": "Falls ein Konto für diese E-Mail existiert, wurde ein neues Passwort gesendet", "passwordResetFailed": "Fehler beim Senden des neuen Passworts. Bitte versuchen Sie es erneut.", "enterEmail": "<PERSON>te geben Sie Ihre E-Mail-Adresse ein", "sendingResetLink": "Neues Passwort wird gesendet...", "sendResetLink": "Neues Passwort senden", "backToSignIn": "Zurück zur Anmeldung"}, "requestAccess": {"and": "und", "title": "Zugang zur Sphäroid-Segmentierungsplattform anfordern", "description": "Füllen Sie das folgende Formular aus, um Zugang zu unserer Plattform anzufordern. Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren.", "emailLabel": "Ihre E-Mail-Adresse", "nameLabel": "Ihr Name", "institutionLabel": "Institution/Unternehmen", "reasonLabel": "Grund für den Zugang", "submitRequest": "<PERSON><PERSON><PERSON> senden", "requestReceived": "Anfrage erhalten", "thankYou": "Vielen Dank für Ihr Interesse", "weWillContact": "Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren", "submitSuccess": "Anfrage erfolgreich gesendet!", "emailPlaceholder": "Geben Sie Ihre E-Mail-Adresse ein", "namePlaceholder": "Geben Sie Ihren vollständigen Namen ein", "institutionPlaceholder": "Geb<PERSON> Sie den Namen Ihrer Institution oder Ihres Unternehmens ein", "reasonPlaceholder": "<PERSON>te beschreiben Sie, wie Sie die Plattform nutzen möchten", "fillRequired": "Bitte füllen Si<PERSON> alle erforderlichen Felder aus", "submittingRequest": "Anfrage wird gesendet...", "submitError": "Fehler beim Senden der Anfrage", "alreadyPending": "Eine Zugangsanfrage für diese E-Mail ist bereits ausstehend", "agreeToTerms": "Mit dem Absenden dieser Anfrage stimmen Si<PERSON> unseren"}, "requestAccessForm": {"title": "Zugang zur Sphäroid-Segmentierungsplattform anfordern", "description": "Füllen Sie das folgende Formular aus, um Zugang zu unserer Plattform anzufordern. Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren.", "emailLabel": "Ihre E-Mail-Adresse", "nameLabel": "Ihr Name", "institutionLabel": "Institution/Unternehmen", "reasonLabel": "Grund für den Zugang", "submitButton": "<PERSON><PERSON><PERSON> senden", "signInPrompt": "Haben <PERSON> bereits ein Konto?", "signInLink": "Anmelden", "thankYouTitle": "Vielen Dank für Ihr Interesse", "weWillContact": "Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren", "agreeToTerms": "Mit dem Absenden dieser Anfrage stimmen Si<PERSON> unseren", "and": "und"}, "documentation": {"tag": "Benutzerhandbuch", "title": "SpheroSeg Dokumentation", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, wie Sie die Sphäroid-Segmentierungsplattform effektiv nutzen.", "sidebar": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "introduction": "Einführung", "gettingStarted": "<PERSON><PERSON><PERSON>", "uploadingImages": "Bilder hochladen", "segmentationProcess": "Segmentierungsprozess", "apiReference": "API-Referenz"}, "introduction": {"title": "Einführung", "imageAlt": "Darstellung des Sphäroid-Analyse-Workflows", "whatIs": {"title": "Was ist SpheroSeg?", "paragraph1": "SpheroSeg ist eine hochmoderne Plattform für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern. Unser Tool bietet Forschern präzise Detektions- und Analysefähigkeiten.", "paragraph2": "Es nutzt fortschrittliche KI-Algorithmen basierend auf Deep Learning, um Sphäroide in Ihren Bildern automatisch mit hoher Genauigkeit und Konsistenz zu identifizieren und zu segmentieren.", "paragraph3": "Diese Dokumentation führt Sie durch alle Aspekte der Plattformnutzung, von den ersten Schritten bis zu erweiterten Funktionen und API-Integration."}}, "gettingStarted": {"title": "<PERSON><PERSON><PERSON>", "accountCreation": {"title": "Kontoerstellung", "paragraph1": "Um SpheroSeg zu nutzen, müssen <PERSON> ein Konto erstellen. Dies ermöglicht uns, Ihre Projekte und Bilder sicher zu speichern.", "step1Prefix": "Besuchen Sie die", "step1Link": "Registrierungsseite", "step2": "Geben Sie Ihre institutionelle E-Mail-Adresse ein und erstellen Sie ein Passwort", "step3": "Vervollständigen Sie Ihr Profil mit Ihrem Namen und Ihrer Institution", "step4": "Verifizieren Sie Ihre E-Mail-Adresse über den Link in Ihrem Posteingang"}, "creatingProject": {"title": "Ihr erstes Projekt erstellen", "paragraph1": "Projekte helf<PERSON>, <PERSON>hre Arbeit zu organisieren. Jedes Projekt kann mehrere Bilder und deren entsprechende Segmentierungsergebnisse enthalten.", "step1": "<PERSON><PERSON><PERSON> Sie in Ihrer Übersicht auf \"Neues Projekt\"", "step2": "Geben Sie einen Projektnamen und eine Beschreibung ein", "step3": "Wählen Sie den Projekttyp (Standard: Sphäroid-Analyse)", "step4": "Klicken Sie auf \"Projekt erstellen\" um fortzufahren"}}, "uploadingImages": {"title": "Bilder hochladen", "paragraph1": "SpheroSeg unterstützt verschiedene Bildformate, die in der Mikroskopie häufig verwendet werden, einschließlich TIFF, PNG und JPEG.", "methods": {"title": "Upload-Methoden", "paragraph1": "<PERSON><PERSON> gibt mehrere Möglichkeiten, Bilder hochzu<PERSON>n:", "step1": "Ziehen Sie Dateien direkt in den Upload-Bereich", "step2": "<PERSON><PERSON><PERSON> Sie auf den Upload-<PERSON><PERSON>ich, um <PERSON><PERSON> von Ihrem Computer auszuwählen", "step3": "Batch-Upload mehr<PERSON> Bilder auf einmal"}, "note": {"prefix": "<PERSON><PERSON><PERSON><PERSON>:", "text": "<PERSON>ür optimale Ergebnisse stellen Si<PERSON> sicher, dass Ihre mikroskopischen Bilder einen guten Kontrast zwischen Sphäroid und Hintergrund haben."}}, "segmentationProcess": {"title": "Segmentierungsprozess", "paragraph1": "Der Segmentierungsprozess identifiziert die Grenzen von Sphäroiden in Ihren Bildern und ermöglicht eine präzise Analyse ihrer Morphologie.", "automatic": {"title": "Automatische Segmentierung", "paragraph1": "Unsere KI-gesteuerte automatische Segmentierung kann Sphäroidgrenzen mit hoher Genauigkeit erkennen:", "step1": "<PERSON>ählen Sie ein Bild aus Ihrem Projekt", "step2": "Klicken Sie auf \"Auto-Segmentieren\" um den Prozess zu starten", "step3": "Das System verarbeitet das Bild und zeigt die erkannten Grenzen an", "step4": "Überprüfen Sie die Ergebnisse im Segmentierungseditor"}, "manual": {"title": "<PERSON><PERSON>", "paragraph1": "Manch<PERSON> erfordert die automatische Segmentierung eine Verfeinerung. Unser Editor bietet Werkzeuge für:", "step1": "Hinzufügen oder Entfernen von Scheitelpunkten entlang der Grenze", "step2": "<PERSON><PERSON><PERSON> von Scheitelpunktpositionen für genauere Grenzen", "step3": "Aufteilen oder Zusammenführen von Regionen", "step4": "Hinzufügen oder Entfernen von Löchern innerhalb von Sphäroiden"}}, "apiReference": {"title": "API-Referenz", "paragraph1": "SpheroSeg bietet eine RESTful API für programmatischen Zugriff auf die Plattformfunktionen. Dies ist ideal für die Integration in Ihre bestehenden Workflows oder Batch-Verarbeitung.", "endpoint1Desc": "Ruft eine Liste aller Ihrer Projekte ab", "endpoint2Desc": "Ruft alle Bilder innerhalb eines bestimmten Projekts ab", "endpoint3Desc": "Initiiert die Segmentierung für ein bestimmtes Bild", "contactPrefix": "Für vollständige API-Dokumentation und Authentifizierungsdetails kontaktieren Sie uns bitte unter"}, "backToHome": "Zurück zur Startseite", "backToTop": "Zurück nach oben"}, "hero": {"platformTag": "Erweiterte Sphäroid-Segmentierungsplattform", "title": "KI-gestützte Zellanalyse für biomedizinische Forschung", "subtitle": "Verbessern Sie Ihre mikroskopische Zellbildanalyse mit unserer hochmodernen Sphäroid-Segmentierungsplattform. Entwickelt für Forscher, die Präzision und Effizienz suchen.", "getStartedButton": "Jetzt starten", "learnMoreButton": "<PERSON><PERSON> er<PERSON>", "imageAlt1": "Sphäroid-Mikroskopbild", "imageAlt2": "Sphäroid-Mikroskopbild mit Analyse", "welcomeTitle": "Willkommen bei SpheroSeg", "welcomeSubtitle": "Erweiterte Plattform für Zellsphäroid-Segmentierung und -Analyse", "welcomeDescription": "Unsere Plattform kombiniert modernste Algorithmen der künstlichen Intelligenz mit einer intuitiven Benutzeroberfläche für präzise Erkennung und Ana<PERSON><PERSON> von Zellsphäroiden in mikroskopischen Bildern.", "featuresTitle": "Leistungsstarke Funktionen", "featuresSubtitle": "Erweiterte Werkzeuge für die biomedizinische Forschung", "featureAiSegmentation": "Erweiterte Segmentierung", "featureAiSegmentationDesc": "Präzise Sphäroiderkennung mit Grenzanalyse für genaue Zellmessungen.", "featureEditing": "KI-gestützte Analyse", "featureEditingDesc": "Nutzen Sie Deep-Learning-Algorithmen für automatische Erkennung und Zellklassifizierung.", "featureAnalytics": "Einfaches Hochladen", "featureAnalyticsDesc": "Ziehen Sie Ihre mikroskopischen Bilder für sofortige Verarbeitung und Analyse.", "featureExport": "Statistische Einblicke", "featureExportDesc": "Umfassende Metriken und Visualisierungen zur Extraktion aussagekräftiger Datenmuster.", "ctaTitle": "<PERSON><PERSON><PERSON>, Ihren Zellanalyse-Workflow zu transformieren?", "ctaSubtitle": "Schl<PERSON>ßen Si<PERSON> sich führenden Forschern an, die bereits unsere Plattform nutzen, um ihre Entdeckungen zu beschleunigen.", "ctaButton": "<PERSON><PERSON> er<PERSON>"}, "navbar": {"home": "Startseite", "features": "Funktionen", "documentation": "Dokumentation", "terms": "Bedingungen", "privacy": "Datenschutz", "login": "Anmelden", "requestAccess": "<PERSON><PERSON><PERSON>"}, "navigation": {"home": "Startseite", "projects": "Projekte", "settings": "Einstellungen", "profile": "Profil", "dashboard": "Übersicht", "back": "Zurück"}, "dashboard": {"manageProjects": "Verwalten und organisieren Sie Ihre Forschungsprojekte", "viewMode": {"grid": "Rasteransicht", "list": "Listenansicht"}, "sort": {"name": "Name", "updatedAt": "Zuletzt aktualisiert", "segmentationStatus": "Status"}, "search": "Projekte suchen...", "searchImagesPlaceholder": "Bilder suchen...", "noProjects": "<PERSON>ine Projekte gefunden", "noImagesDescription": "<PERSON><PERSON> Bilder entsprechen Ihren Suchkriterien", "createFirst": "<PERSON>rstellen Sie Ihr erstes Projekt, um zu beginnen", "createNew": "Neues Projekt erstellen", "lastChange": "Letzte Änderung", "statsOverview": "Statistikübersicht", "totalProjects": "Projekte gesamt", "activeProjects": "Aktive Projekte", "totalImages": "Bilder gesamt", "totalAnalyses": "<PERSON><PERSON><PERSON> g<PERSON>", "lastUpdated": "Zuletzt aktualisiert", "noProjectsDescription": "Sie haben noch keine Projekte erstellt. Erstellen Sie Ihr erstes Projekt, um zu beginnen.", "searchProjectsPlaceholder": "Projekte nach Namen suchen...", "sortBy": "Sortieren nach", "name": "Name", "completed": "Abgeschlossen", "processing": "In Bearbeitung", "pending": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Fehlgeschlagen", "selectImagesButton": "Bilder auswählen"}, "projects": {"title": "Projekte", "description": "Verwalten Sie Ihre Forschungsprojekte", "createNew": "Neues Projekt erstellen", "createProject": "Projekt erstellen", "createProjectDesc": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Projekt, um mit Bildern und Segmentierung zu arbeiten.", "projectName": "Projektname", "projectDescription": "Projektbeschreibung", "projectNamePlaceholder": "Projektnamen eingeben", "projectDescriptionPlaceholder": "Projektbeschreibung eingeben", "projectCreated": "Projekt erfolgreich erstellt", "projectCreationFailed": "Projekterstellung fehlgeschlagen", "projectDeleted": "Projekt erfolgreich <PERSON>", "deleteSuccess": "Projekt erfolgreich <PERSON>", "deleteFailed": "Projektlöschung fehlgeschlagen", "deleting": "Projekt wird gelöscht...", "notFound": "Projekt nicht gefunden. Es wurde möglicherweise bereits gelöscht.", "missingId": "Projekt kann nicht gelöscht werden: Projektkennung fehlt", "projectDeletionFailed": "Projektlöschung fehlgeschlagen", "confirmDelete": "Möchten Sie dieses Projekt wirklich löschen?", "confirmDeleteDescription": "Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.", "delete": "Löschen", "deleteProject": "Projekt löschen", "deleteProjectDescription": "Diese Aktion kann nicht rückgängig gemacht werden. Das Projekt und alle zugehörigen Daten werden dauerhaft gelöscht.", "deleteWarning": "<PERSON><PERSON> sind dabei, das folgende Projekt zu löschen:", "typeToConfirm": "Geben Sie den Projektnamen zur Bestätigung ein", "confirmDeleteError": "Bitte geben Sie den Projektnamen genau ein, um zu bestätigen", "editProject": "Projekt bearbeiten", "viewProject": "Projekt anzeigen", "projectUpdated": "Projekt erfolgreich aktualisiert", "projectUpdateFailed": "Projektaktualisierung fehlgeschlagen", "noProjects": "<PERSON>ine Projekte gefunden", "createFirstProject": "<PERSON>rstellen Sie Ihr erstes Projekt, um zu beginnen", "searchProjects": "Projekte suchen...", "filterProjects": "Projekte filtern", "sortProjects": "Projekte sortieren", "projectNameRequired": "Projektname ist erforderlich", "loginRequired": "<PERSON>e müssen angemeldet sein, um ein Projekt zu erstellen", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "Zuletzt aktualisiert", "imageCount": "Bilder", "status": "Status", "actions": "Aktionen", "loading": "Projekte werden geladen...", "error": "Fehler beim Laden der Projekte", "retry": "<PERSON><PERSON><PERSON> versuchen", "duplicating": "Projekt wird dupliziert...", "duplicate": "Duplizieren", "duplicateSuccess": "Projekt erfolgreich dupliziert", "duplicateFailed": "Projektduplizierung fehlgeschlagen", "duplicateTitle": "Projekt duplizieren", "duplicateProject": "Projekt duplizieren", "duplicateProjectDescription": "<PERSON><PERSON><PERSON><PERSON> Sie eine Kopie dieses Projekts einschließlich aller Bilder. Sie können die Optionen unten anpassen.", "duplicateCancelled": "Projektduplizierung abgebrochen", "duplicatingProject": "Projekt wird dupliziert", "duplicatingProjectDescription": "Ihr Projekt wird dupliziert. Dies kann einige Momente dauern.", "duplicateProgress": "Duplizierungsfortschritt", "duplicationComplete": "Projektduplizierung abgeschlossen", "duplicationTaskFetchError": "Fehler beim Abrufen der Aufgabendaten", "duplicationCancelError": "Fehler beim Abbrechen der Duplizierung", "duplicateProgressDescription": "Ihr Projekt wird dupliziert. Dieser Vorgang kann bei großen Projekten einige Zeit dauern.", "duplicationPending": "<PERSON><PERSON><PERSON><PERSON>", "duplicationProcessing": "In Bearbeitung", "duplicationCompleted": "Abgeschlossen", "duplicationFailed": "Fehlgeschlagen", "duplicationCancelled": "Abgebrochen", "duplicationCancellationFailed": "Abbruch der Duplizierung fehlgeschlagen", "duplicationSuccessMessage": "Projekt erfolgreich dupliziert! Sie können jetzt auf das neue Projekt zugreifen.", "copySegmentations": "Segmentierungsergebnisse kopieren", "resetImageStatus": "Bildverarbeitungsstatus zurücksetzen", "newProjectTitle": "Neuer Projekttitel", "itemsProcessed": "Elemente verarbeitet", "items": "Elemente", "unknownProject": "Unbekanntes Projekt", "activeTasks": "Aktiv", "allTasks": "Alle", "noActiveDuplications": "Keine aktiven Duplizierungen", "noDuplications": "<PERSON><PERSON>lizierungsaufgaben gefunden", "untitledProject": "Unbenanntes Projekt", "exportProject": "Projekt exportieren", "share": "Teilen", "export": "Exportieren", "archived": "<PERSON><PERSON><PERSON><PERSON>", "completed": "Abgeschlossen", "draft": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktiv"}, "projectToolbar": {"selectImages": "Bilder auswählen", "cancelSelection": "Auswahl abbrechen", "export": "Exportieren", "uploadImages": "Bilder hochladen"}, "statsOverview": {"title": "Dashboard-Übersicht", "totalProjects": "Projekte gesamt", "totalImages": "Bilder gesamt", "completedSegmentations": "Abgeschlossene Segmentierungen", "storageUsed": "<PERSON><PERSON><PERSON>icher", "recentActivity": "Aktuelle Aktivität", "moreStats": "Detaillierte Statistiken anzeigen", "completion": "Abschlussrate", "vsLastMonth": "vs. <PERSON><PERSON><PERSON>", "thisMonth": "<PERSON><PERSON>", "lastMonth": "Letzten Monat", "projectsCreated": "Projekte erstellt", "imagesUploaded": "Bilder ho<PERSON><PERSON><PERSON>n", "fetchError": "Fehler beim Laden der Statistiken", "storageLimit": "Speicherlimit", "activityTitle": "Aktuelle Aktivität", "noActivity": "Keine aktuelle Aktivität", "hide": "Verbergen", "activityTypes": {"project_created": "Projekt erstellt", "image_uploaded": "Bild hoch<PERSON>aden", "segmentation_completed": "Segmentierung abgeschlossen"}}, "footer": {"developerName": "Bc<PERSON> <PERSON><PERSON>", "facultyName": "FNSPE CTU in Prag", "description": "Erweiterte Plattform für Sphäroid-Segmentierung und -Analyse", "contactLabel": "<EMAIL>", "developerLabel": "Bc<PERSON> <PERSON><PERSON>", "facultyLabel": "FNSPE CTU in Prag", "resourcesTitle": "Ressourcen", "documentationLink": "Dokumentation", "featuresLink": "Funktionen", "tutorialsLink": "Tutorials", "researchLink": "Forschung", "legalTitle": "Rechtliche Informationen", "termsLink": "Nutzungsbedingungen", "privacyLink": "Datenschutzrichtlinie", "contactUsLink": "Kontaktieren Sie uns", "informationTitle": "Informationen", "contactTitle": "Kontakt", "copyrightNotice": "SpheroSeg. Alle Rechte vorbehalten.", "madeWith": "Erstellt mit", "by": "von", "requestAccessLink": "<PERSON><PERSON><PERSON>"}, "features": {"tag": "Funktionen", "title": "Entdecken Sie unsere Plattformfähigkeiten", "subtitle": "Erweiterte Werkzeuge für die biomedizinische Forschung", "cards": {"segmentation": {"title": "Erweiterte Segmentierung", "description": "Präzise Sphäroiderkennung mit Grenzanalyse für genaue Zellmessungen"}, "aiAnalysis": {"title": "KI-gestützte Analyse", "description": "Nutzen Sie Deep-Learning-Algorithmen für automatische Zellerkennung und -klassifizierung"}, "uploads": {"title": "Einfaches Hochladen", "description": "Ziehen Sie Ihre mikroskopischen Bilder für sofortige Verarbeitung und Analyse"}, "insights": {"title": "Statistische Einblicke", "description": "Umfassende Metriken und Visualisierungen zur Extraktion aussagekräftiger Datenmuster"}, "collaboration": {"title": "Team-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Teilen Sie Projekte und Ergebnisse mit Kollegen für effizientere Forschung"}, "pipeline": {"title": "Automatisierte Pipeline", "description": "Optimieren Sie Ihren Workflow mit unseren Batch-Verarbeitungstools"}}}, "index": {"about": {"tag": "Über die Plattform", "title": "Was ist SpheroSeg?", "imageAlt": "Beispiel einer Sphäroidsegmentierung", "paragraph1": "SpheroSeg ist eine erweiterte Plattform, die speziell für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern entwickelt wurde.", "paragraph2": "Unser Tool kombiniert modernste Algorithmen der künstlichen Intelligenz mit einer intuitiven Benutzeroberfläche, um Forschern präzise Sphäroidgrenzenerkennung und analytische Fähigkeiten zu bieten.", "paragraph3": "Die Plattform wurde von <PERSON><PERSON> von der FNSPE CTU in Prag unter der Leitung von Adam Novozámský vom UTIA CAS in Zusammenarbeit mit Forschern der Abteilung für Biochemie und Mikrobiologie an der UCT Prag entwickelt.", "contactPrefix": "<EMAIL>"}, "cta": {"title": "Be<PERSON><PERSON>, Ihre Forschung zu transformieren?", "subtitle": "Beginnen Si<PERSON> noch heute mit SpheroSeg und entdecken Sie neue Möglichkeiten in der Zellsphäroidanalyse", "boxTitle": "Kostenloses <PERSON> er<PERSON>llen", "boxText": "Erhalten Sie Zugriff auf alle Plattformfunktionen und beginnen Sie mit der Analyse Ihrer mikroskopischen Bilder", "button": "<PERSON><PERSON> er<PERSON>"}}, "tools": {"zoomIn": "Vergrößern", "zoomOut": "Verkleinern", "resetView": "<PERSON><PERSON><PERSON>", "createPolygon": "Neues Polygon erstellen", "exitPolygonCreation": "Polygon-Erstellungsmodus beenden", "splitPolygon": "Polygon in zwei teilen", "exitSlicingMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>den", "addPoints": "Punkte zu Polygon hinzufügen", "exitPointAddingMode": "Punkt-Hinzufügen-Modus beenden", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Speichern", "resegment": "Neu segmentieren", "title": "Werkzeuge"}, "settings": {"title": "Einstellungen", "pageTitle": "Einstellungen", "profile": "Profil", "account": "Ko<PERSON>", "appearance": "Erscheinungsbild", "profileSettings": "Profileinstellungen", "accountSettings": "Kontoeinstellungen", "securitySettings": "Sicherheitseinstellungen", "preferenceSettings": "Präferenzeinstellungen", "selectLanguage": "Sprache auswählen", "selectTheme": "Thema auswählen", "updateProfile": "Profil aktualisieren", "changePassword": "Passwort ändern", "deleteAccount": "Konto löschen", "savedChanges": "Änderungen erfolgreich gespeichert", "saveChanges": "Änderungen speichern", "profileUpdated": "<PERSON>il er<PERSON><PERSON>g<PERSON>ich aktualisiert", "languageSettings": "Spracheinstellungen", "themeSettings": "Themeneinstellungen", "privacySettings": "Datenschutzeinstellungen", "exportData": "Daten exportieren", "importData": "Daten importieren", "uploadAvatar": "<PERSON>il<PERSON><PERSON> ho<PERSON>n", "removeAvatar": "Profilbild entfernen", "twoFactorAuth": "Zwei-Faktor-Authentifizierung", "emailNotifications": "E-Mail-Benachrichtigungen", "pushNotifications": "Push-Benachrichtigungen", "weeklyDigest": "Wöchentliche Zusammenfassung", "monthlyReport": "Monatsbericht", "displaySettings": "Anzeigeeinstellungen", "accessibilitySettings": "Barrierefreiheitseinstellungen", "advancedSettings": "Erweiterte Einstellungen", "useBrowserLanguage": "Browsersprache verwenden", "language": "<PERSON><PERSON><PERSON>", "theme": "<PERSON>a", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "system": "System", "languageUpdated": "Sprache erfolgreich aktualisiert", "themeUpdated": "Thema erfolgreich aktualisiert", "toggleTheme": "<PERSON>a wechs<PERSON>n", "languageDescription": "Wählen Sie Ihre bevorzugte Sprache", "themeDescription": "Wählen Sie Ihr bevorzugtes Thema", "profileLoadError": "Fehler beim Laden des Profils", "appearanceDescription": "Passen Sie das Erscheinungsbild der Anwendung an", "personal": "Persönliche Informationen", "fullName": "Vollständiger Name", "organization": "Organisation", "department": "Abteilung", "publicProfile": "Öffentliches Profil", "makeProfileVisible": "Mein Profil für andere Forscher sichtbar machen", "passwordSettings": "Passworteinstellungen", "currentPassword": "Aktuelles Passwort", "newPassword": "Neues Passwort", "confirmNewPassword": "Neues Passwort bestätigen", "dangerZone": "Gefahrenzone", "deleteAccountWarning": "<PERSON><PERSON>d Sie Ihr Konto löschen, gibt es kein Zurück mehr. Alle Ihre Daten werden dauerhaft gelöscht.", "savingChanges": "Änderungen werden gespeichert...", "savePreferences": "Präferenzen speichern", "usernameTaken": "Dieser Benutzername ist bereits vergeben", "deleteAccountDescription": "Diese Aktion ist unwiderruflich. Alle Ihre Daten werden dauerhaft gelöscht.", "confirmUsername": "Bestätigen Sie Ihre E-Mail", "password": "Passwort", "enterPassword": "Geben Sie Ihr Passwort ein", "passwordChangeError": "Fehler beim Ändern des Passworts", "passwordChangeSuccess": "Passwort erfolgreich geändert", "passwordsDoNotMatch": "Passwörter stimmen nicht überein", "accountDeleteSuccess": "Konto erfolgreich <PERSON>", "accountDeleteError": "Fehler beim Löschen des Kontos", "passwordChanged": "Passwort geändert", "confirmPasswordLabel": "Passwort bestätigen", "changePasswordDescription": "Ändern Sie Ihr Passwort, um Ihr Konto zu sichern", "dangerZoneDescription": "Diese Aktionen sind unwiderruflich und werden Ihre Daten dauerhaft entfernen", "deletingAccount": "Konto wird gelöscht...", "deleteAccountError": "Fehler beim Löschen des Kontos"}, "accessibility": {"skipToContent": "Zum Hauptinhalt springen"}, "profile": {"title": "Titel", "about": "<PERSON><PERSON>", "activity": "Aktivität", "projects": "Projekte", "recentProjects": "Aktuelle Projekte", "recentAnalyses": "Aktuel<PERSON>", "accountDetails": "Kontodetails", "accountType": "Kontotyp", "joinDate": "Beitrittsdatum", "lastActive": "Zuletzt aktiv", "projectsCreated": "Projekte erstellt", "imagesUploaded": "Bilder ho<PERSON><PERSON><PERSON>n", "segmentationsCompleted": "Abgeschlossene Segmentierungen", "pageTitle": "Benutzerprofil", "editProfile": "<PERSON><PERSON>", "joined": "Beigetreten", "statistics": "Statistiken", "images": "Bilder", "analyses": "<PERSON><PERSON><PERSON>", "storageUsed": "<PERSON><PERSON><PERSON>icher", "recentActivity": "Aktuelle Aktivität", "noRecentActivity": "Keine aktuelle Aktivität", "fetchError": "Fehler beim Laden der Profildaten", "aboutMe": "<PERSON>ber mich", "noBio": "<PERSON>ine Bio angegeben", "avatarHelp": "Klicken Sie auf das Kamerasymbol, um ein Profilbild hochzuladen", "avatarImageOnly": "Bitte wählen Sie eine Bilddatei", "avatarTooLarge": "Bild muss kleiner als 5MB sein", "avatarUpdated": "Profilbild aktualisiert", "avatarUploadError": "Fehler beim Hochladen des Profilbilds", "avatarRemoved": "Profil<PERSON><PERSON> entfernt", "avatarRemoveError": "Fehler beim Entfernen des Profilbilds", "cropAvatarDescription": "Passen Sie den Zuschneidebereich an, um Ihr Profilbild festzulegen", "description": "Aktualisieren Sie Ihre persönlichen Informationen und Ihr Profilbild", "saveButton": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "usernamePlaceholder": "<PERSON><PERSON>en Sie Ihren Benutzernamen ein", "fullName": "Vollständiger Name", "fullNamePlaceholder": "Geben Sie Ihren vollständigen Namen ein", "titlePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, Professor", "organization": "Organisation", "organizationPlaceholder": "Geben Sie Ihre Organisation oder Institution ein", "bio": "Bio", "bioPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> uns etwas über sich", "bioDescription": "Eine kurze Beschreibung über Sie, die auf Ihrem Profil sichtbar ist", "location": "<PERSON><PERSON>", "locationPlaceholder": "z.B. Berlin, Deutschland", "uploadAvatar": "<PERSON>il<PERSON><PERSON> ho<PERSON>n", "removeAvatar": "Profilbild entfernen", "cropAvatar": "Profilbild zuschneiden", "activityDescription": "Systemaktivität", "email": "E-Mail", "notProvided": "Nicht angegeben"}, "termsPage": {"title": "Nutzungsbedingungen", "acceptance": {"title": "1. <PERSON><PERSON><PERSON>", "paragraph1": "Durch den Zugriff auf oder die Nutzung von SpheroSeg stimmen Si<PERSON> zu, an diese Nutzungsbedingungen und alle geltenden Gesetze und Vorschriften gebunden zu sein. Wenn Sie mit diesen Bedingungen nicht einverstanden sind, ist Ihnen die Nutzung dieses Dienstes untersagt."}, "useLicense": {"title": "2. Nutzungslizenz", "paragraph1": "Die Erlaubnis wird erteilt, SpheroSeg vorübergehend nur für persönliche, nicht-kommerzielle oder akademische Forschungszwecke zu nutzen. Dies ist die Gewährung einer Lizenz, nicht eine Übertragung des Eigentums."}, "dataUsage": {"title": "3. <PERSON><PERSON><PERSON><PERSON>", "paragraph1": "Alle in SpheroSeg hochgeladenen Daten bleiben Ihr Eigentum. Wir beanspruchen kein Eigentum an Ihren Inhalten, benöti<PERSON> jedoch bestimmte Berechtigungen zur Bereitstellung des Dienstes."}, "limitations": {"title": "4. Einschränkungen", "paragraph1": "SpheroSeg haftet in keinem Fall für Schäden, die aus der Nutzung oder Unmöglichkeit der Nutzung der Plattform entstehen, selbst wenn wir auf die Möglichkeit solcher Schäden hingewiesen wurden."}, "revisions": {"title": "5. <PERSON><PERSON><PERSON> und Fehler", "paragraph1": "Die auf SpheroSeg erscheinenden Materialien können technische, typografische oder fotografische Fehler enthalten. Wir gewährleisten nicht, dass die Materialien genau, vollständig oder aktuell sind."}, "governingLaw": {"title": "6. <PERSON><PERSON><PERSON><PERSON><PERSON>", "paragraph1": "Diese Bedingungen unterliegen den Gesetzen des Landes, in dem der Dienst gehostet wird, und Sie unterwerfen sich unwiderruflich der ausschließlichen Gerichtsbarkeit der Gerichte an diesem Ort."}}, "privacyPage": {"title": "Datenschutzrichtlinie", "introduction": {"title": "1. Einführung", "paragraph1": "<PERSON>se Datenschutzrichtlinie erkl<PERSON>rt, wie SpheroSeg (\"wir\", \"uns\", \"unser\") Ihre Informationen sammelt, verwendet und teilt, wenn Sie unsere Plattform für Sphäroidsegmentierung und -analyse nutzen."}, "informationWeCollect": {"title": "2. <PERSON><PERSON>, die wir sammeln", "paragraph1": "<PERSON>ir sammeln Informationen, die Sie uns direkt zur Verfügung stellen, wenn <PERSON> ein Konto erstellen, <PERSON><PERSON><PERSON>, Projekte erstellen und anderweitig mit unseren Diensten interagieren."}, "personalInformation": {"title": "2.1 Persönliche Informationen", "paragraph1": "<PERSON>zu gehören Ihr Name, Ihre E-Mail-Adresse, Institution/Organisation und andere Informationen, die Sie bei der Erstellung eines Kontos oder der Anforderung des Zugangs zu unseren Diensten angeben."}, "researchData": {"title": "2.2 Forschungsdaten", "paragraph1": "<PERSON><PERSON> g<PERSON><PERSON><PERSON> Bilder, die <PERSON><PERSON> hochladen, Projektdetails, Analyseergebnisse und andere forschungsbezogene Daten, die Sie auf unserer Plattform erstellen oder hochladen."}, "usageInformation": {"title": "2.3 Nutzungsinformationen", "paragraph1": "Wir sammeln Informationen darüber, wie Sie unsere Plattform nutzen, einsch<PERSON><PERSON>lich Protokolldaten, Geräteinformationen und Nutzungsmuster."}, "howWeUse": {"title": "3. Wie wir Ihre Informationen verwenden", "paragraph1": "Wir verwenden die gesammelten Informationen, um unsere Dienste bereitzustellen, zu warten und zu verbessern, mit Ihnen zu kommunizieren und unseren rechtlichen Verpflichtungen nachzukommen."}, "dataSecurity": {"title": "4. Datensicher<PERSON><PERSON>", "paragraph1": "Wir implementieren angemessene Sicherheitsmaßnahmen zum Schutz Ihrer persönlichen Informationen und Forschungsdaten vor unbefugtem Zugriff, Änderung, Offenlegung oder Zerstörung."}, "dataSharing": {"title": "5. Date<PERSON>weitergabe", "paragraph1": "Wir verkaufen Ihre persönlichen Informationen oder Forschungsdaten nicht. Wir können Ihre Informationen unter begrenzten Umständen teilen, z.B<PERSON> mit Ihrer Zustimmung, zur Erfüllung rechtlicher Verpflichtungen oder mit Dienstleistern, die uns beim Betrieb unserer Plattform helfen."}, "yourChoices": {"title": "6. <PERSON><PERSON><PERSON>", "paragraph1": "Sie können über Ihre Kontoeinstellungen auf Ihre Kontoinformationen und Forschungsdaten zugreifen, diese aktualisieren oder löschen. Sie können uns auch kontaktieren, um <PERSON>ng, Korrektur oder Löschung persönlicher Informationen zu beantragen, die wir über Si<PERSON> haben."}, "changes": {"title": "7. <PERSON><PERSON><PERSON> dieser Richtlinie", "paragraph1": "Wir können diese Datenschutzrichtlinie von Zeit zu Zeit aktualisieren. Wir werden Sie über Änderungen informieren, indem wir die neue Datenschutzrichtlinie auf dieser Seite veröffentlichen und das \"Zuletzt aktualisiert\"-Datum aktualisieren."}, "contactUs": {"title": "8. Kontaktier<PERSON> Sie uns", "paragraph1": "<PERSON><PERSON> <PERSON> Fragen zu dieser Datenschutzrichtlinie haben, kontaktieren Si<PERSON> uns <NAME_EMAIL>."}, "lastUpdated": "Zuletzt aktualisiert: 1. Juli 2023"}, "shortcuts": {"button": "Tastenkürzel", "editMode": "In Bearbeitungsmodus wechseln", "sliceMode": "In Schnittmodus wechseln", "addPointMode": "In Punkt-Hinzufügen-Modus wechseln", "holdShift": "Shift gedrückt halten für automatisches Hinzufügen von Punkten (im Bearbeitungsmodus)", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deletePolygon": "Ausgewähltes Polygon löschen", "cancel": "Aktuelle Operation abbrechen", "zoomIn": "Vergrößern", "zoomOut": "Verkleinern", "resetView": "<PERSON><PERSON><PERSON>", "title": "Tastenkürzel", "viewMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editVerticesMode": "Scheitelpunkt-Bearbeitungsmodus", "addPointsMode": "Punkt-Hinzufügen-Modus", "createPolygonMode": "Polygon-Erstellungsmodus", "save": "Speichern", "description": "Diese Tastenkürzel funktionieren im Segmentierungseditor für schnelleres und komfortableres Arbeiten."}, "imageProcessor": {"segmentationStarted": "Segmentierungsprozess wurde gestartet...", "startSegmentationTooltip": "Segmentierung starten", "processingTooltip": "Wird verarbeitet...", "savingTooltip": "Wird gespeichert...", "completedTooltip": "Segmentierung abgeschlossen", "retryTooltip": "Segmentierung wiederholen"}, "uploader": {"dragDrop": "Bilder hier ablegen oder klicken, um Dateien auszuwählen", "dropFiles": "<PERSON><PERSON> hier ablegen...", "segmentAfterUploadLabel": "Bilder sofort nach dem Hochladen segmentieren", "filesToUpload": "Hochzuladende Dateien", "uploadBtn": "Hochladen", "uploadError": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "clickToUpload": "<PERSON><PERSON><PERSON>, um Dateien zu durchsuchen", "selectProjectLabel": "Projekt auswählen", "selectProjectPlaceholder": "Projekt auswählen...", "noProjectsFound": "<PERSON>ine Projekte gefunden. <PERSON><PERSON><PERSON>n Si<PERSON> zu<PERSON>t ein neues.", "imageOnly": "(Nur Bilddateien)", "uploadingImages": "Bilder werden hochgeladen...", "uploadComplete": "Upload abgeschlossen", "uploadFailed": "Upload fehlgeschlagen", "processingImages": "Bilder werden verarbeitet...", "dragAndDropFiles": "<PERSON><PERSON> hier <PERSON>gen", "or": "oder", "clickToSelect": "<PERSON><PERSON><PERSON>, um Dateien auszuwählen"}, "images": {"uploadImages": "Bilder hochladen", "dragDrop": "Bilder hier <PERSON>gen", "clickToSelect": "oder klicken, um Dateien auszuwählen", "acceptedFormats": "Unterstützte Formate: JPEG, PNG, TIFF, BMP (max 10MB)", "uploadProgress": "Upload-Fortschritt", "uploadingTo": "<PERSON><PERSON><PERSON><PERSON> zu", "currentProject": "Aktuelles Projekt", "autoSegment": "Bilder nach dem Hochladen automatisch segmentieren", "uploadCompleted": "Upload abgeschlossen", "uploadFailed": "Upload fehlgeschlagen", "imagesUploaded": "Bilder er<PERSON><PERSON><PERSON><PERSON><PERSON> hoch<PERSON>n", "imagesFailed": "Bild-Upload fehlgeschlagen", "viewAnalyses": "<PERSON><PERSON><PERSON>", "noAnalysesYet": "<PERSON><PERSON> keine <PERSON>", "runAnalysis": "<PERSON><PERSON><PERSON> au<PERSON>", "viewResults": "Ergebnisse anzeigen", "dropImagesHere": "Bilder hier ablegen...", "selectProjectFirst": "Bitte wählen Si<PERSON> zu<PERSON>t ein Projekt", "projectRequired": "<PERSON>e müssen ein Projekt auswählen, bevor <PERSON> Bilder hoch<PERSON>n", "imageOnly": "(Nur Bilddateien)", "dropFiles": "<PERSON><PERSON> hier ablegen...", "filesToUpload": "Hochzuladende Dateien ({{count}})", "uploadBtn": "{{count}} Bilder hoch<PERSON>n", "uploadError": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "noProjectsToUpload": "<PERSON>ine Projekte verfügbar. <PERSON><PERSON><PERSON>n Si<PERSON> zu<PERSON>t ein Projekt.", "notFound": "Projekt \"{{projectName}}\" nicht gefunden. Es wurde möglicherweise gelöscht."}, "export": {"formatDescriptions": {"COCO": "Common Objects in Context (COCO) JSON-Format für Objekterkennung", "YOLO": "You Only Look Once (YOLO) Textformat für Objekterkennung", "MASK": "Binäre Maskenbilder für jedes segmentierte Objekt", "POLYGONS": "Polygonkoordinaten im JSON-Format"}, "exportCompleted": "Export abgeschlossen", "exportFailed": "Export fehlgeschlagen", "title": "Segmentierungsdaten exportieren", "spheroidMetrics": "Sphäroid-Metriken", "visualization": "Visualisierung", "cocoFormat": "COCO-Format", "close": "Schließen", "metricsExported": "Metriken erfolgreich exportiert", "options": {"includeMetadata": "Metadaten e<PERSON>chließen", "includeSegmentation": "Segmentierung einschließen", "selectExportFormat": "Exportformat auswählen", "includeObjectMetrics": "Objektmetriken einschließen", "selectMetricsFormat": "Metrikenformat auswählen", "metricsFormatDescription": {"EXCEL": "Excel-Datei (.xlsx)", "CSV": "CSV-Datei (.csv)"}, "includeImages": "Originalbilder einschließen", "exportMetricsOnly": "Nur Metriken exportieren", "metricsRequireSegmentation": "Der Export von Metriken erfordert eine abgeschlossene Segmentierung"}, "formats": {"COCO": "COCO JSON", "YOLO": "YOLO TXT", "MASK": "Maske (TIFF)", "POLYGONS": "Polygone (JSON)"}, "metricsFormats": {"EXCEL": "Excel (.xlsx)", "CSV": "CSV (.csv)"}, "selectImagesForExport": "Bilder für Export auswählen"}, "metrics": {"area": "Fläche", "perimeter": "Umfang", "circularity": "Kreisförmigkeit", "sphericity": "Sphärizität", "solidity": "Festigkeit", "compactness": "Kompaktheit", "convexity": "Konvexität", "visualization": "Metrik-Visualisierung", "visualizationHelp": "Visuelle Darstellung der Metriken für alle Sphäroide in diesem Bild", "barChart": "Balkendiagramm", "pieChart": "Kreisdiagramm", "comparisonChart": "Vergleichsdiagramm", "keyMetricsComparison": "Vergleich der Schlüsselmetriken", "areaDistribution": "Flächenverteilung", "shapeMetricsComparison": "Vergleich der Formmetriken", "noPolygonsFound": "<PERSON>ine Polygone für die Analyse gefunden"}, "imageStatus": {"completed": "Verarbeitet", "processing": "In Bearbeitung", "pending": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Fehlgeschlagen", "noImage": "<PERSON><PERSON>", "untitledImage": "Unbenanntes Bild"}, "projectActions": {"duplicateTooltip": "Projekt duplizieren", "deleteTooltip": "Projekt löschen", "deleteConfirmTitle": "Sind Sie sicher?", "deleteConfirmDesc": "Möchten Sie das Projekt \"{{projectName}}\" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "deleteSuccess": "Projekt \"{{projectName}}\" wurde erfolgreich gelö<PERSON>t.", "deleteError": "Projektlöschung fehlgeschlagen.", "duplicateSuccess": "Projekt \"{{projectName}}\" wurde erfolgreich dupliziert.", "duplicateError": "Projektduplizierung fehlgeschlagen.", "makePrivateTooltip": "Als privat markieren", "makePublicTooltip": "Als öffentlich markieren", "shareTooltip": "Projekt teilen", "downloadTooltip": "<PERSON>jekt <PERSON>", "notFound": "Projekt \"{{projectName}}\" nicht gefunden. Es wurde möglicherweise bereits gelöscht."}, "editor": {"backButtonTooltip": "Zurück zur Projektübersicht", "exportButtonTooltip": "Aktuelle Segmentierungsdaten exportieren", "saveTooltip": "Änderungen speichern", "image": "Bild", "previousImage": "Vorheriges Bild", "nextImage": "Nächstes Bild", "resegmentButton": "Neu segmentieren", "resegmentButtonTooltip": "Segmentierung für dieses Bild erneut ausführen", "exportMaskButton": "Maske exportieren", "exportMaskButtonTooltip": "Segmentierungsmaske für dieses Bild exportieren", "backButton": "Zurück", "exportButton": "Exportieren", "saveButton": "Speichern", "loadingProject": "Projekt wird geladen...", "loadingImage": "Bild wird geladen...", "sliceErrorInvalidPolygon": "<PERSON>nn nicht schneiden: Ungültiges Polygon ausgewählt.", "sliceWarningInvalidResult": "Schneiden hat Polygone erstellt, die zu klein und ungültig sind.", "sliceWarningInvalidIntersections": "Ungültiger Schnitt: Schnittlinie muss das Polygon an genau zwei Punkten schneiden.", "sliceSuccess": "Polygon erfolgreich geschnitten.", "noPolygonToSlice": "<PERSON>ine Polygone zum Schneiden verfügbar.", "savingTooltip": "Wird gespeichert..."}, "segmentationPage": {"noImageSelected": "<PERSON>in Bild für die Resegmentierung ausgewählt.", "resegmentationStarted": "Starte Resegmentierung mit ResUNet Neuronalnetz...", "resegmentationQueued": "Resegmentierung wurde in die Warteschlange eingereiht.", "resegmentationCompleted": "Resegmentierung erfolgreich abgeschlossen.", "resegmentationFailed": "Resegmentierung fehlgeschlagen.", "resegmentationTimeout": "Resegmentierung hat das Zeitlimit überschritten. Überprüfen Sie den Warteschlangenstatus.", "resegmentationError": "Fehler beim Starten der Resegmentierung.", "resegmentTooltip": "Neu segmentieren"}, "share": {"accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alreadyShared": "Bereits mit diesem Benutzer geteilt", "canEdit": "Kann bearbeiten", "copyToClipboard": "In Zwischenablage kopieren", "edit": "<PERSON><PERSON><PERSON>", "email": "E-Mail", "failedToCopy": "Fehler beim Kopieren des Links", "failedToGenerateLink": "Fehler beim Generieren des Freigabelinks", "failedToLoadShares": "Fehler beim Laden der freigegebenen Benutzer", "failedToRemove": "Fehler beim Entfernen der Freigabe", "failedToShare": "Fehler beim Teilen des Projekts", "generateLink": "<PERSON>", "generateNewLink": "Neuen Link generieren", "generating": "Wird generiert...", "invalidEmail": "Ungültige E-Mail-Adresse", "invalidEmailOrPermission": "Ungültige E-Mail oder Berechtigung", "invite": "Einladen", "inviteByEmail": "Per E-Mail einladen", "inviteByLink": "<PERSON> <PERSON> e<PERSON>", "linkCopied": "Link in Zwischenablage kopiert", "linkGenerated": "Freigabelink generiert", "linkPermissions": "Link-Berechtigungen", "noPermission": "<PERSON><PERSON>", "noShares": "<PERSON><PERSON> freigegeben<PERSON>", "pendingAcceptance": "<PERSON><PERSON><PERSON>", "permissions": "Berechtigungen", "projectNotFound": "Projekt nicht gefunden", "removeShare": "Freigabe entfernen", "selectAccessLevel": "Zugriffsebene auswählen", "selectPermission": "Bitte wählen Sie einen Berechtigungstyp", "shareDescription": "<PERSON><PERSON>n Sie dieses Projekt mit anderen Benutzern", "sharedWith": "Geteilt mit", "shareLinkDescription": "<PERSON><PERSON> mit diesem Link kann auf das Projekt zugreifen", "shareProject": "Projekt teilen", "shareProjectTitle": "Projekt \"{{projectName}}\" teilen", "sharing": "Wird geteilt...", "sharedSuccess": "Projekt \"{{projectName}}\" wurde mit {{email}} geteilt", "removedSuccess": "Freigabe für {{email}} wurde entfernt", "status": "Status", "userEmail": "Benutzer-E-Mail", "view": "<PERSON><PERSON><PERSON>", "viewOnly": "<PERSON><PERSON>"}, "segmentation": {"contextMenu": {"editPolygon": "Polygon bearbeiten", "splitPolygon": "Polygon teilen", "deletePolygon": "Polygon löschen", "confirmDeleteTitle": "Möchten Sie das Polygon wirklich löschen?", "confirmDeleteMessage": "Diese Aktion ist unwiderruflich. Das Polygon wird dauerhaft aus der Segmentierung entfernt.", "duplicateVertex": "Scheitelpunkt duplizieren", "deleteVertex": "Scheitelpunkt löschen"}, "title": "Segmentierungseditor", "resolution": "{width}x{height}", "queue": {"title": "Segmentierungswarteschlange", "summary": "{{total}} Aufgaben insgesamt ({{running}} in Bearbeitung, {{queued}} in Warteschlange)", "noRunningTasks": "<PERSON><PERSON> la<PERSON> Aufgaben", "noQueuedTasks": "<PERSON><PERSON> Aufgaben in Warteschlange", "task": "Aufgabe", "statusRunning": "Segmentierung: {{count}} läuft{{queued}}", "statusQueued": ", {{count}} in Warteschlange", "statusOnlyQueued": "Segmentierung: {{count}} in Warteschlange", "statusOnlyQueued_one": "Segmentierung: 1 in Warteschlange", "statusOnlyQueued_other": "Segmentierung: {{count}} in Warteschlange", "processing": "In Bearbeitung", "queued": "In Warteschlange", "statusProcessing": "Segmentierung: {{count}} in Bearbeitung", "statusReady": "Bereit", "tasksTotal": "{{total}} Aufgaben insgesamt ({{running}} in Bearbeitung, {{queued}} in Warteschlange)"}, "selectPolygonForEdit": "<PERSON>ä<PERSON>en Si<PERSON> ein Polygon zum Bearbeiten", "selectPolygonForSlice": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Polygon zum Schneiden", "selectPolygonForAddPoints": "<PERSON>ählen Sie ein Polygon zum Hinzufügen von Punkten", "clickToAddPoint": "<PERSON><PERSON><PERSON>, um einen Punkt hinzuzufügen", "clickToCompletePolygon": "<PERSON>licken Sie auf den ersten Punkt, um das Polygon zu schließen", "clickToAddFirstSlicePoint": "<PERSON><PERSON><PERSON>, um den ersten Schnittpunkt hinzuzufügen", "clickToAddSecondSlicePoint": "<PERSON><PERSON><PERSON>, um den zweiten Schnittpunkt hinzuzufügen", "polygonCreationMode": "Polygon-Erstellungsmodus", "polygonEditMode": "Polygon-Bearbeitungsmodus", "polygonSliceMode": "Polygon-Schnittmodus", "polygonAddPointsMode": "Punkt-Hinzufügen-Modus", "viewMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalPolygons": "Polygone gesamt", "totalVertices": "Scheitelpunkte gesamt", "vertices": "Scheitelpunkte", "zoom": "Zoom", "mode": "Modus", "selected": "Ausgewählt", "none": "<PERSON><PERSON>", "polygons": "Polygone", "imageNotFound": "Bild nicht gefunden", "returnToProject": "Zurück zum Projekt", "backToProject": "Zurück zum Projekt", "previousImage": "Vorheriges Bild", "nextImage": "Nächstes Bild", "toggleShortcuts": "Tastenkürzel umschalten", "modes": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Bearbeitungsmo<PERSON>", "create": "Erstellungsmodus", "slice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addPoints": "Punkt-Hinzufügen-Modus", "deletePolygon": "Polygon-Löschungsmodus", "createPolygon": "Polygon-Erstellungsmodus", "editVertices": "Scheitelpunkt-Bearbeitungsmodus", "editMode": "Bearbeitungsmo<PERSON>", "slicingMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointAddingMode": "Punkt-Hinzufügen-Modus"}, "status": {"processing": "In Bearbeitung", "queued": "In Warteschlange", "completed": "Abgeschlossen", "failed": "Fehlgeschlagen", "pending": "<PERSON><PERSON><PERSON><PERSON>"}, "autoSave": {"enabled": "Auto-Speichern: Aktiviert", "disabled": "Auto-Speichern: Deaktiviert", "idle": "Auto-Speichern: Inaktiv", "pending": "Ausstehend...", "saving": "Wird gespeichert...", "success": "Gespe<PERSON>rt", "error": "<PERSON><PERSON>"}, "loading": "Segmentierung wird geladen...", "polygon": "Polygon", "unsavedChanges": "Ungespeicherte Änderungen", "noData": "Keine Segmentierungsdaten verfügbar", "noPolygons": "<PERSON><PERSON> Polygone gefunden", "regions": "Segmentierung", "position": "Position", "polygonDeleted": "Polygon erfolgreich <PERSON>", "saveSuccess": "Segmentierung erfolgreich gespeichert", "resegmentSuccess": "Resegmentierung erfolgreich gestartet", "resegmentComplete": "Resegmentierung erfolgreich abgeschlossen", "resegmentError": "<PERSON><PERSON> beim Resegmentieren des Bildes", "resegmentButton": "Resegmentieren", "completedSegmentation": "Abgeschlossen", "resegmentButtonTooltip": "Mit neuronalem Netzwerk resegmentieren", "helpTips": {"title": "Tipps:", "edit": {"createPoint": "<PERSON><PERSON><PERSON>, um einen neuen Punkt zu erstellen", "shiftPoints": "Halten Sie Shift gedrückt, um automatisch eine Punktfolge zu erstellen", "closePolygon": "Schließen Sie das Polygon, indem Sie auf den ersten Punkt klicken"}, "slice": {"start": "<PERSON><PERSON><PERSON>, um den Schnitt zu beginnen", "finish": "<PERSON>lick<PERSON> Si<PERSON> erneut, um den Schnitt zu beenden", "cancel": "Esc zum Abbrechen des Schneidens"}, "addPoint": {"hover": "Fahren Sie mit der Maus über die Polygonlinie", "click": "<PERSON><PERSON><PERSON>, um einen Punkt zum ausgewählten Polygon hinzuzufügen", "exit": "Esc zum Beenden des Hinzufügen-Modus"}}}, "errors": {"somethingWentWrong": "Etwas ist schiefgelaufen", "componentError": "In dieser Komponente ist ein Fehler aufgetreten", "errorDetails": "Fehlerdetails", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "reloadPage": "Seite neu laden", "goBack": "Zurück", "notFound": "Seite nicht gefunden", "pageNotFoundMessage": "Die angeforderte Seite konnte nicht gefunden werden", "returnToHome": "Zur Startseite zurückkehren", "unauthorized": "Nicht autorisierter Zugriff", "forbidden": "<PERSON><PERSON><PERSON> verweigert", "serverError": "<PERSON><PERSON><PERSON>", "networkError": "Netzwerkfehler", "timeoutError": "Anfrage-Timeout", "validationError": "Validierungsfehler", "unknownError": "Unbekannter Fehler", "goHome": "Zur Startsei<PERSON>", "fetchSegmentationFailed": "Fehler beim Abrufen der Segmentierung", "fetchImageFailed": "Fehler beim Abrufen des Bildes", "saveSegmentationFailed": "Fehler beim Speichern der Segmentierung", "missingPermissions": "Unzureichende Berechtigungen", "invalidInput": "Ungültige Eingabe", "resourceNotFound": "Ressource nicht gefunden"}, "project": {"detail": {"noImagesSelected": "<PERSON><PERSON> Bilder ausgewählt", "triggeringResegmentation": "Starte Re-Segmentierung für {{count}} Bilder...", "deleteConfirmation": "<PERSON><PERSON>cht<PERSON> Si<PERSON> wirklich {{count}} Bilder löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "deletingImages": "<PERSON><PERSON><PERSON> {{count}} Bilder...", "deleteSuccess": "{{count}} Bilder <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteFailed": "<PERSON><PERSON> beim Löschen von {{count}} <PERSON><PERSON><PERSON>", "preparingExport": "Bereite Export von {{count}} Bildern vor..."}, "segmentation": {"processingInBatches": "Starte Segmentierung für {{count}} Bilder in {{batches}} Stapeln...", "batchQueued": "Stapel {{current}}/{{total}} erfolgreich in Warteschlange", "batchQueuedFallback": "Stapel {{current}}/{{total}} erfolgreich in Warteschlange (Fallback-Endpunkt)", "batchError": "Fehler bei Verarbeitung von Stapel {{current}}/{{total}}", "partialSuccess": "Segmentierung: {{success}} Bilder erfolgre<PERSON> in Warteschlange, {{failed}} fehlgeschlagen", "allSuccess": "Segmentierung: Alle {{count}} Bilder erfolgreich in Warteschlange", "allFailed": "Segmentierung: Alle {{count}} Bilder fehlgeschlagen", "startedImages": "Segmentierung für {{count}} Bilder gestartet", "queuedLocallyWarning": "Segmentierung für {{count}} Bilder lokal in Warteschlange. Serververbindung fehlgeschlagen."}, "loading": "Projekt wird geladen...", "notFound": "Projekt nicht gefunden", "error": "Fehler beim Laden des Projekts", "empty": "Dieses Projekt ist leer", "noImages": {"title": "<PERSON>ch keine Bilder", "description": "Dieses Projekt enthält noch keine Bilder. Laden Sie Bilder hoch, um zu beginnen.", "uploadButton": "Bilder hochladen"}, "addImages": "Fügen Sie Bilder hinzu, um zu beginnen", "deleteProject": "Projekt löschen", "deleteConfirmation": "Möchten Sie das Projekt \"{{projectName}}\" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "duplicateProject": "Projekt duplizieren", "duplicateDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine Kopie dieses Projekts. Das neue Projekt wird mit dem Namen erstellt, den Si<PERSON> unten angeben.", "newProjectName": "Neuer Projektname", "enterProjectName": "Geben Sie den neuen Projektnamen ein", "duplicate": "Duplizieren"}, "projectsPage": {"title": "Projekte", "description": "Forschungsprojekte verwalten", "createNew": "Neues Projekt erstellen", "createProject": "Projekt erstellen", "createProjectDesc": "Neues Forschungsprojekt starten", "projectName": "Projektname", "projectDescription": "Projektbeschreibung", "projectNamePlaceholder": "Projektnamen eingeben", "projectDescriptionPlaceholder": "Projektbeschreibung eingeben", "projectCreated": "Projekt erfolgreich erstellt", "projectCreationFailed": "Projekterstellung fehlgeschlagen", "projectDeleted": "Projekt erfolgreich <PERSON>", "projectDeletionFailed": "Projektlöschung fehlgeschlagen", "confirmDelete": "Möchten Sie dieses Projekt wirklich löschen?", "confirmDeleteDescription": "Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.", "deleteProject": "Projekt löschen", "editProject": "Projekt bearbeiten", "viewProject": "Projekt anzeigen", "projectUpdated": "Projekt erfolgreich aktualisiert", "projectUpdateFailed": "Projektaktualisierung fehlgeschlagen", "noProjects": "<PERSON>ine Projekte gefunden", "createFirstProject": "<PERSON>rstellen Sie Ihr erstes Projekt, um zu beginnen", "searchProjects": "Projekte suchen...", "filterProjects": "Projekte filtern", "sortProjects": "Projekte sortieren", "projectNameRequired": "Projektname ist erforderlich", "loginRequired": "<PERSON>e müssen angemeldet sein, um ein Projekt zu erstellen", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "Zuletzt aktualisiert", "imageCount": "Bilder", "status": "Status", "actions": "Aktionen", "loading": "Projekte werden geladen...", "error": "Fehler beim Laden der Projekte", "retry": "<PERSON><PERSON><PERSON> versuchen", "duplicating": "Projekt wird dupliziert...", "duplicate": "Duplizieren", "duplicateSuccess": "Projekt erfolgreich dupliziert", "duplicateFailed": "Projektduplizierung fehlgeschlagen", "duplicateTitle": "Projekt duplizieren", "duplicateProject": "Projekt duplizieren", "duplicateProjectDescription": "<PERSON><PERSON><PERSON><PERSON> Sie eine Kopie dieses Projekts einschließlich aller Bilder. Sie können die Optionen unten anpassen.", "duplicateCancelled": "Projektduplizierung abgebrochen", "duplicatingProject": "Projekt wird dupliziert", "duplicatingProjectDescription": "Ihr Projekt wird dupliziert. Dies kann einige Momente dauern.", "duplicateProgress": "Duplizierungsfortschritt", "duplicationComplete": "Projektduplizierung abgeschlossen", "duplicationTaskFetchError": "Fehler beim Abrufen der Aufgabendaten", "duplicationCancelError": "Fehler beim Abbrechen der Duplizierung", "duplicateProgressDescription": "Ihr Projekt wird dupliziert. Dieser Vorgang kann bei großen Projekten einige Zeit dauern.", "duplicationPending": "<PERSON><PERSON><PERSON><PERSON>", "duplicationProcessing": "In Bearbeitung", "duplicationCompleted": "Abgeschlossen", "duplicationFailed": "Fehlgeschlagen", "duplicationCancelled": "Abgebrochen", "duplicationCancellationFailed": "Abbruch der Duplizierung fehlgeschlagen", "duplicationSuccessMessage": "Projekt erfolgreich dupliziert! Sie können jetzt auf das neue Projekt zugreifen.", "copySegmentations": "Segmentierungsergebnisse kopieren", "resetImageStatus": "Bildverarbeitungsstatus zurücksetzen", "newProjectTitle": "Neuer Projekttitel", "itemsProcessed": "Elemente verarbeitet", "items": "Elemente", "unknownProject": "Unbekanntes Projekt", "activeTasks": "Aktiv", "allTasks": "Alle", "noActiveDuplications": "Keine aktiven Duplizierungen", "noDuplications": "<PERSON><PERSON>lizierungsaufgaben gefunden", "deleteProjectDescription": "Diese Aktion löscht das Projekt und alle zugehörigen Daten dauerhaft.", "deleteWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.", "untitledProject": "Unbenanntes Projekt", "typeToConfirm": "<PERSON><PERSON><PERSON> \"löschen\" zur Bestätigung ein", "deleteConfirm": "Möchten Sie dieses Projekt wirklich löschen?", "exportProject": "Projekt exportieren", "archived": "<PERSON><PERSON><PERSON><PERSON>", "completed": "Abgeschlossen", "draft": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktiv", "createDate": "<PERSON><PERSON><PERSON><PERSON>", "lastModified": "Zuletzt geändert", "projectDescPlaceholder": "Projektbeschreibung eingeben", "creatingProject": "Projekt wird erstellt...", "noImages": {"title": "<PERSON>ch keine Bilder", "description": "Dieses Projekt enthält noch keine Bilder. Laden Sie Bilder hoch, um mit der Segmentierung zu beginnen.", "uploadButton": "Bilder hochladen"}}}