import { config } from '../config';
import { SEGMENTATION_STATUS, type SegmentationStatus } from '../constants/segmentationStatus';

export interface ImageData {
  id: string;
  project_id: string;
  user_id: string;
  name: string;
  storage_filename: string;
  storage_path: string;
  file_size: number;
  width: number;
  height: number;
  format: string;
  thumbnail_path: string;
  thumbnail_url?: string;
  url?: string;
  metadata: Record<string, any>;
  status: SegmentationStatus;
  segmentation_status: SegmentationStatus;
  segmentationStatus?: SegmentationStatus; // For backward compatibility
  created_at: string;
  updated_at: string;
}

export interface ImageListResponse {
  data: ImageData[];
  total: number;
  pagination: {
    limit: number;
    offset: number;
    page: number;
    totalPages: number;
    hasMore: boolean;
  };
}

class ImageService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = config.api.endpoints.images;
  }

  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  async fetchProjectImages(projectId: string, page = 1, limit = 50): Promise<ImageListResponse> {
    try {
      const response = await fetch(
        `${this.baseUrl.replace(':projectId', projectId)}?page=${page}&limit=${limit}`,
        {
          headers: this.getAuthHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch images: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Normalize status field
      if (data.data) {
        data.data = data.data.map((image: ImageData) => ({
          ...image,
          // Ensure we have a consistent status field
          status: image.segmentationStatus || image.segmentation_status || image.status || SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
          segmentationStatus: image.segmentationStatus || image.segmentation_status || image.status || SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
        }));
      }

      return data;
    } catch (error) {
      console.error('Error fetching images:', error);
      throw error;
    }
  }

  async uploadImages(projectId: string, files: File[]): Promise<ImageData[]> {
    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append('images', file);
      });

      const token = localStorage.getItem('accessToken');
      const response = await fetch(
        `${this.baseUrl.replace(':projectId', projectId)}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            // Don't set Content-Type for FormData, let browser set it
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to upload images: ${response.statusText}`);
      }

      const uploadedImages = await response.json();
      
      // Normalize status field for uploaded images
      return uploadedImages.map((image: ImageData) => ({
        ...image,
        status: image.segmentationStatus || image.segmentation_status || image.status || SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
        segmentationStatus: image.segmentationStatus || image.segmentation_status || image.status || SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
      }));
    } catch (error) {
      console.error('Error uploading images:', error);
      throw error;
    }
  }

  async deleteImage(projectId: string, imageId: string): Promise<void> {
    try {
      const response = await fetch(
        `${this.baseUrl.replace(':projectId', projectId)}/${imageId}`,
        {
          method: 'DELETE',
          headers: this.getAuthHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to delete image: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  }

  async triggerSegmentation(projectId: string, imageId: string): Promise<void> {
    try {
      const response = await fetch(
        `/api/images/${imageId}/segmentation`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({}),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to trigger segmentation: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error triggering segmentation:', error);
      throw error;
    }
  }

  async batchTriggerSegmentation(imageIds: string[]): Promise<void> {
    try {
      const response = await fetch(
        `/api/segmentation/batch/trigger`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ imageIds }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to trigger batch segmentation: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error triggering batch segmentation:', error);
      throw error;
    }
  }
}

export const imageService = new ImageService();