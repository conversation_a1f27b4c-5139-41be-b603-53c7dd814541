import { io, Socket } from 'socket.io-client';
import { SEGMENTATION_STATUS } from '../constants/segmentationStatus';

interface WebSocketOptions {
  auth?: {
    token: string;
  };
}

class UnifiedWebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Map<string, Set<Function>> = new Map();

  connect(options?: WebSocketOptions): void {
    if (this.socket?.connected) {
      console.log('WebSocket already connected');
      return;
    }

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
    
    this.socket = io(apiUrl, {
      transports: ['websocket', 'polling'],
      auth: options?.auth,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.emit('ws:connected');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.emit('ws:disconnected', reason);
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.reconnectAttempts++;
      this.emit('ws:error', error);
    });

    // Handle segmentation updates
    this.socket.on('segmentation_update', (data) => {
      console.log('Segmentation update received:', data);
      this.emit('segmentation:update', data);
    });

    // Handle legacy segmentation updates for backward compatibility
    this.socket.on('segmentation_update_legacy', (data) => {
      // Convert legacy status to new format
      const normalizedData = {
        ...data,
        status: data.newStatus || data.status,
      };
      this.emit('segmentation:update', normalizedData);
    });

    // Handle image updates
    this.socket.on('image:created', (data) => {
      console.log('Image created:', data);
      this.emit('image:created', data);
    });

    this.socket.on('image:updated', (data) => {
      console.log('Image updated:', data);
      this.emit('image:updated', data);
    });

    this.socket.on('image:deleted', (data) => {
      console.log('Image deleted:', data);
      this.emit('image:deleted', data);
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  joinProject(projectId: string): void {
    if (!this.socket?.connected) {
      console.warn('Cannot join project: WebSocket not connected');
      return;
    }

    // Support multiple join event formats for compatibility
    this.socket.emit('join_project', { projectId });
    this.socket.emit('join-project', projectId);
    this.socket.emit('join', `project-${projectId}`);
    
    console.log(`Joined project room: ${projectId}`);
  }

  leaveProject(projectId: string): void {
    if (!this.socket?.connected) {
      console.warn('Cannot leave project: WebSocket not connected');
      return;
    }

    this.socket.emit('leave_project', { projectId });
    console.log(`Left project room: ${projectId}`);
  }

  // Event emitter pattern for internal events
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event)!.add(handler);
  }

  off(event: string, handler?: Function): void {
    if (!this.eventHandlers.has(event)) return;
    
    if (handler) {
      this.eventHandlers.get(event)!.delete(handler);
    } else {
      this.eventHandlers.delete(event);
    }
  }

  private emit(event: string, ...args: any[]): void {
    if (!this.eventHandlers.has(event)) return;
    
    this.eventHandlers.get(event)!.forEach(handler => {
      try {
        handler(...args);
      } catch (error) {
        console.error(`Error in event handler for ${event}:`, error);
      }
    });
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

export const unifiedWebSocketService = new UnifiedWebSocketService();