import React, { useState, useCallback } from 'react';
import { useProjectImages } from '../hooks/useProjectImages';
import { SEGMENTATION_STATUS, getStatusLabel, getStatusColor } from '../constants/segmentationStatus';
import type { ImageData } from '../services/imageService';

interface ImageGalleryProps {
  projectId: string;
}

interface ImageCardProps {
  image: ImageData;
  isSelected: boolean;
  onSelect: (imageId: string) => void;
  onDelete: (imageId: string) => void;
  onSegment: (imageId: string) => void;
}

const ImageCard: React.FC<ImageCardProps> = ({ 
  image, 
  isSelected, 
  onSelect, 
  onDelete, 
  onSegment 
}) => {
  const status = image.segmentationStatus || image.status;
  const statusLabel = getStatusLabel(status);
  const statusColor = getStatusColor(status);

  return (
    <div className="image-card" style={{ border: isSelected ? '2px solid #2196f3' : '1px solid #ddd' }}>
      <div className="image-wrapper" style={{ position: 'relative' }}>
        <img 
          src={image.thumbnail_url || image.url} 
          alt={image.name}
          style={{ width: '100%', height: '200px', objectFit: 'cover' }}
          loading="lazy"
        />
        
        {/* Selection checkbox */}
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onSelect(image.id)}
          style={{ position: 'absolute', top: 8, left: 8 }}
        />
        
        {/* Status badge */}
        <div 
          className="status-badge"
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            padding: '4px 8px',
            backgroundColor: statusColor,
            color: 'white',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 'bold',
          }}
        >
          {statusLabel}
        </div>
      </div>
      
      <div className="image-info" style={{ padding: '8px' }}>
        <h4 style={{ margin: '0 0 8px 0', fontSize: '14px' }}>{image.name}</h4>
        <div style={{ fontSize: '12px', color: '#666' }}>
          {image.width} × {image.height} • {(image.file_size / 1024 / 1024).toFixed(1)} MB
        </div>
      </div>
      
      <div className="image-actions" style={{ padding: '0 8px 8px', display: 'flex', gap: '8px' }}>
        {status === SEGMENTATION_STATUS.WITHOUT_SEGMENTATION && (
          <button 
            onClick={() => onSegment(image.id)}
            style={{ flex: 1, padding: '4px 8px', fontSize: '12px' }}
          >
            Segment
          </button>
        )}
        {status === SEGMENTATION_STATUS.COMPLETED && (
          <button 
            onClick={() => onSegment(image.id)}
            style={{ flex: 1, padding: '4px 8px', fontSize: '12px' }}
          >
            Re-segment
          </button>
        )}
        <button 
          onClick={() => onDelete(image.id)}
          style={{ padding: '4px 8px', fontSize: '12px', backgroundColor: '#f44336', color: 'white' }}
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export const ImageGallery: React.FC<ImageGalleryProps> = ({ projectId }) => {
  const {
    images,
    loading,
    error,
    totalImages,
    currentPage,
    totalPages,
    uploadImages,
    triggerSegmentation,
    batchTriggerSegmentation,
    removeImage,
  } = useProjectImages({ projectId, pageSize: 20 });

  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [isUploading, setIsUploading] = useState(false);

  // Handle file upload
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setIsUploading(true);
    try {
      await uploadImages(files);
      // Clear file input
      event.target.value = '';
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
    }
  }, [uploadImages]);

  // Handle image selection
  const handleSelectImage = useCallback((imageId: string) => {
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageId)) {
        newSet.delete(imageId);
      } else {
        newSet.add(imageId);
      }
      return newSet;
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback(() => {
    if (selectedImages.size === images.length) {
      setSelectedImages(new Set());
    } else {
      setSelectedImages(new Set(images.map(img => img.id)));
    }
  }, [images, selectedImages.size]);

  // Handle batch segmentation
  const handleBatchSegment = useCallback(async () => {
    const imageIds = Array.from(selectedImages);
    if (imageIds.length === 0) return;

    try {
      await batchTriggerSegmentation(imageIds);
      setSelectedImages(new Set());
    } catch (error) {
      console.error('Batch segmentation failed:', error);
    }
  }, [selectedImages, batchTriggerSegmentation]);

  // Handle single image deletion
  const handleDeleteImage = useCallback(async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return;
    
    try {
      await removeImage(imageId);
    } catch (error) {
      console.error('Delete failed:', error);
    }
  }, [removeImage]);

  // Handle single image segmentation
  const handleSegmentImage = useCallback(async (imageId: string) => {
    try {
      await triggerSegmentation(imageId);
    } catch (error) {
      console.error('Segmentation failed:', error);
    }
  }, [triggerSegmentation]);

  if (loading && images.length === 0) {
    return <div>Loading images...</div>;
  }

  if (error) {
    return <div>Error loading images: {error}</div>;
  }

  return (
    <div className="image-gallery">
      {/* Upload section */}
      <div className="upload-section" style={{ marginBottom: '20px' }}>
        <input
          type="file"
          id="image-upload"
          multiple
          accept="image/*"
          onChange={handleFileUpload}
          disabled={isUploading}
          style={{ display: 'none' }}
        />
        <label 
          htmlFor="image-upload"
          style={{
            display: 'inline-block',
            padding: '8px 16px',
            backgroundColor: '#2196f3',
            color: 'white',
            borderRadius: '4px',
            cursor: isUploading ? 'not-allowed' : 'pointer',
            opacity: isUploading ? 0.6 : 1,
          }}
        >
          {isUploading ? 'Uploading...' : 'Upload Images'}
        </label>
        
        {selectedImages.size > 0 && (
          <>
            <button onClick={handleBatchSegment} style={{ marginLeft: '10px' }}>
              Segment Selected ({selectedImages.size})
            </button>
            <button onClick={() => setSelectedImages(new Set())} style={{ marginLeft: '10px' }}>
              Clear Selection
            </button>
          </>
        )}
        
        <button onClick={handleSelectAll} style={{ marginLeft: '10px' }}>
          {selectedImages.size === images.length ? 'Deselect All' : 'Select All'}
        </button>
      </div>

      {/* Gallery info */}
      <div style={{ marginBottom: '10px' }}>
        <p>Total images: {totalImages} | Page {currentPage} of {totalPages}</p>
      </div>

      {/* Image grid */}
      {images.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <p>No images uploaded yet.</p>
          <p>Click "Upload Images" to get started.</p>
        </div>
      ) : (
        <div 
          className="image-grid"
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
            gap: '16px',
          }}
        >
          {images.map(image => (
            <ImageCard
              key={image.id}
              image={image}
              isSelected={selectedImages.has(image.id)}
              onSelect={handleSelectImage}
              onDelete={handleDeleteImage}
              onSegment={handleSegmentImage}
            />
          ))}
        </div>
      )}
    </div>
  );
};