/**
 * Segmentation status constants
 * Must match backend values from packages/backend/src/constants/segmentationStatus.ts
 */

export const SEGMENTATION_STATUS = {
  WITHOUT_SEGMENTATION: 'without_segmentation',
  QUEUED: 'queued',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export type SegmentationStatus = typeof SEGMENTATION_STATUS[keyof typeof SEGMENTATION_STATUS];

// Helper function to get status label
export function getStatusLabel(status: SegmentationStatus): string {
  switch (status) {
    case SEGMENTATION_STATUS.WITHOUT_SEGMENTATION:
      return 'Not Segmented';
    case SEGMENTATION_STATUS.QUEUED:
      return 'Queued';
    case SEGMENTATION_STATUS.PROCESSING:
      return 'Processing';
    case SEGMENTATION_STATUS.COMPLETED:
      return 'Completed';
    case SEGMENTATION_STATUS.FAILED:
      return 'Failed';
    default:
      return 'Unknown';
  }
}

// Helper function to get status color
export function getStatusColor(status: SegmentationStatus): string {
  switch (status) {
    case SEGMENTATION_STATUS.WITHOUT_SEGMENTATION:
      return '#9e9e9e'; // gray
    case SEGMENTATION_STATUS.QUEUED:
      return '#ff9800'; // orange
    case SEGMENTATION_STATUS.PROCESSING:
      return '#2196f3'; // blue
    case SEGMENTATION_STATUS.COMPLETED:
      return '#4caf50'; // green
    case SEGMENTATION_STATUS.FAILED:
      return '#f44336'; // red
    default:
      return '#9e9e9e'; // gray
  }
}