import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useProjectImages } from '../hooks/useProjectImages';
import { imageService } from '../services/imageService';
import { unifiedWebSocketService } from '../services/unifiedWebSocketService';
import { SEGMENTATION_STATUS } from '../constants/segmentationStatus';

// Mock the services
vi.mock('../services/imageService');
vi.mock('../services/unifiedWebSocketService');
vi.mock('../store', () => ({
  useStore: () => ({
    addNotification: vi.fn(),
  }),
}));

describe('Image Gallery Integration Tests', () => {
  const mockProjectId = 'test-project-123';
  const mockImages = [
    {
      id: 'img-1',
      project_id: mockProjectId,
      user_id: 'user-1',
      name: 'test1.jpg',
      storage_filename: 'test1.jpg',
      storage_path: '/uploads/test1.jpg',
      file_size: 1024000,
      width: 800,
      height: 600,
      format: 'jpeg',
      thumbnail_path: '/uploads/thumb-test1.jpg',
      thumbnail_url: 'http://localhost/uploads/thumb-test1.jpg',
      url: 'http://localhost/uploads/test1.jpg',
      metadata: {},
      status: SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
      segmentation_status: SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
      segmentationStatus: SEGMENTATION_STATUS.WITHOUT_SEGMENTATION,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'img-2',
      project_id: mockProjectId,
      user_id: 'user-1',
      name: 'test2.jpg',
      storage_filename: 'test2.jpg',
      storage_path: '/uploads/test2.jpg',
      file_size: 2048000,
      width: 1024,
      height: 768,
      format: 'jpeg',
      thumbnail_path: '/uploads/thumb-test2.jpg',
      thumbnail_url: 'http://localhost/uploads/thumb-test2.jpg',
      url: 'http://localhost/uploads/test2.jpg',
      metadata: {},
      status: SEGMENTATION_STATUS.COMPLETED,
      segmentation_status: SEGMENTATION_STATUS.COMPLETED,
      segmentationStatus: SEGMENTATION_STATUS.COMPLETED,
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock WebSocket service
    vi.mocked(unifiedWebSocketService).joinProject = vi.fn();
    vi.mocked(unifiedWebSocketService).leaveProject = vi.fn();
    vi.mocked(unifiedWebSocketService).on = vi.fn();
    vi.mocked(unifiedWebSocketService).off = vi.fn();
    
    // Mock image service
    vi.mocked(imageService).fetchProjectImages = vi.fn().mockResolvedValue({
      data: mockImages,
      total: 2,
      pagination: {
        limit: 50,
        offset: 0,
        page: 1,
        totalPages: 1,
        hasMore: false,
      },
    });
    
    vi.mocked(imageService).uploadImages = vi.fn();
    vi.mocked(imageService).deleteImage = vi.fn();
    vi.mocked(imageService).triggerSegmentation = vi.fn();
    vi.mocked(imageService).batchTriggerSegmentation = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('useProjectImages Hook', () => {
    it('should fetch images on mount', async () => {
      const { result } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      expect(result.current.loading).toBe(true);
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.images).toEqual(mockImages);
      expect(result.current.totalImages).toBe(2);
      expect(imageService.fetchProjectImages).toHaveBeenCalledWith(mockProjectId, 1, 50);
    });

    it('should join project room on mount', async () => {
      renderHook(() => useProjectImages({ projectId: mockProjectId }));

      await waitFor(() => {
        expect(unifiedWebSocketService.joinProject).toHaveBeenCalledWith(mockProjectId);
      });
    });

    it('should handle image upload', async () => {
      const newImage = {
        ...mockImages[0],
        id: 'img-3',
        name: 'test3.jpg',
      };

      vi.mocked(imageService).uploadImages.mockResolvedValue([newImage]);

      const { result } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const files = [new File(['test'], 'test3.jpg', { type: 'image/jpeg' })];
      
      await act(async () => {
        await result.current.uploadImages(files);
      });

      expect(imageService.uploadImages).toHaveBeenCalledWith(mockProjectId, files);
      expect(result.current.images).toContainEqual(newImage);
    });

    it('should handle WebSocket segmentation updates', async () => {
      let segmentationUpdateHandler: any;
      
      vi.mocked(unifiedWebSocketService).on.mockImplementation((event, handler) => {
        if (event === 'segmentation:update') {
          segmentationUpdateHandler = handler;
        }
      });

      const { result } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Simulate WebSocket update
      act(() => {
        segmentationUpdateHandler({
          imageId: 'img-1',
          status: SEGMENTATION_STATUS.PROCESSING,
        });
      });

      expect(result.current.images[0].status).toBe(SEGMENTATION_STATUS.PROCESSING);
    });

    it('should trigger segmentation for single image', async () => {
      const { result } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.triggerSegmentation('img-1');
      });

      expect(imageService.triggerSegmentation).toHaveBeenCalledWith(mockProjectId, 'img-1');
      expect(result.current.images[0].status).toBe(SEGMENTATION_STATUS.QUEUED);
    });

    it('should handle batch segmentation', async () => {
      const { result } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const imageIds = ['img-1', 'img-2'];
      
      await act(async () => {
        await result.current.batchTriggerSegmentation(imageIds);
      });

      expect(imageService.batchTriggerSegmentation).toHaveBeenCalledWith(imageIds);
      expect(result.current.images[0].status).toBe(SEGMENTATION_STATUS.QUEUED);
      expect(result.current.images[1].status).toBe(SEGMENTATION_STATUS.QUEUED);
    });

    it('should remove image from list on delete', async () => {
      vi.mocked(imageService).deleteImage.mockResolvedValue(undefined);

      const { result } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.images).toHaveLength(2);

      act(() => {
        result.current.removeImage('img-1');
      });

      expect(result.current.images).toHaveLength(1);
      expect(result.current.images[0].id).toBe('img-2');
    });

    it('should start polling when images are processing', async () => {
      vi.useFakeTimers();

      const processingImages = [
        { ...mockImages[0], status: SEGMENTATION_STATUS.PROCESSING },
      ];

      vi.mocked(imageService).fetchProjectImages
        .mockResolvedValueOnce({
          data: processingImages,
          total: 1,
          pagination: {
            limit: 50,
            offset: 0,
            page: 1,
            totalPages: 1,
            hasMore: false,
          },
        })
        .mockResolvedValueOnce({
          data: [{ ...processingImages[0], status: SEGMENTATION_STATUS.COMPLETED }],
          total: 1,
          pagination: {
            limit: 50,
            offset: 0,
            page: 1,
            totalPages: 1,
            hasMore: false,
          },
        });

      renderHook(() => 
        useProjectImages({ projectId: mockProjectId, autoRefresh: true })
      );

      await waitFor(() => {
        expect(imageService.fetchProjectImages).toHaveBeenCalledTimes(1);
      });

      // Fast-forward 5 seconds
      act(() => {
        vi.advanceTimersByTime(5000);
      });

      await waitFor(() => {
        expect(imageService.fetchProjectImages).toHaveBeenCalledTimes(2);
      });

      vi.useRealTimers();
    });

    it('should clean up on unmount', async () => {
      const { unmount } = renderHook(() => 
        useProjectImages({ projectId: mockProjectId })
      );

      await waitFor(() => {
        expect(unifiedWebSocketService.joinProject).toHaveBeenCalled();
      });

      unmount();

      expect(unifiedWebSocketService.leaveProject).toHaveBeenCalledWith(mockProjectId);
      expect(unifiedWebSocketService.off).toHaveBeenCalledWith(
        'segmentation:update',
        expect.any(Function)
      );
    });
  });

  describe('Status Enum Consistency', () => {
    it('should use correct status values from constants', () => {
      expect(SEGMENTATION_STATUS.WITHOUT_SEGMENTATION).toBe('without_segmentation');
      expect(SEGMENTATION_STATUS.QUEUED).toBe('queued');
      expect(SEGMENTATION_STATUS.PROCESSING).toBe('processing');
      expect(SEGMENTATION_STATUS.COMPLETED).toBe('completed');
      expect(SEGMENTATION_STATUS.FAILED).toBe('failed');
    });

    it('should not contain any "pending" status', () => {
      const statusValues = Object.values(SEGMENTATION_STATUS);
      expect(statusValues).not.toContain('pending');
    });
  });
});