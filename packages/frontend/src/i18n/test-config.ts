import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

/**
 * i18n Test Configuration
 * Simplified i18n setup for testing
 */

const testTranslations = {
  en: {
    translation: {
      common: {
        save: 'Save',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        create: 'Create',
        update: 'Update',
        submit: 'Submit',
        close: 'Close',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        finish: 'Finish',
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        warning: 'Warning',
        info: 'Info',
        confirm: 'Confirm',
        yes: 'Yes',
        no: 'No',
        search: 'Search',
        filter: 'Filter',
        sort: 'Sort',
        export: 'Export',
        import: 'Import',
        download: 'Download',
        upload: 'Upload',
        refresh: 'Refresh',
        reset: 'Reset',
        clear: 'Clear',
        select: 'Select',
        selectAll: 'Select All',
        deselectAll: 'Deselect All',
        required: 'Required',
        optional: 'Optional',
      },
      auth: {
        login: 'Login',
        logout: 'Logout',
        register: 'Register',
        forgotPassword: 'Forgot Password',
        resetPassword: 'Reset Password',
        email: 'Email',
        password: 'Password',
        confirmPassword: 'Confirm Password',
        rememberMe: 'Remember Me',
        loginSuccess: 'Login successful',
        logoutSuccess: 'Logout successful',
        registerSuccess: 'Registration successful',
        invalidCredentials: 'Invalid email or password',
      },
      project: {
        title: 'Projects',
        create: 'Create Project',
        edit: 'Edit Project',
        delete: 'Delete Project',
        name: 'Project Name',
        description: 'Description',
        createdAt: 'Created At',
        updatedAt: 'Updated At',
        imageCount: 'Images',
        public: 'Public',
        private: 'Private',
        collaborators: 'Collaborators',
        settings: 'Settings',
        noProjects: 'No projects found',
      },
      image: {
        title: 'Images',
        upload: 'Upload Images',
        delete: 'Delete Image',
        view: 'View Image',
        download: 'Download Image',
        filename: 'Filename',
        size: 'Size',
        dimensions: 'Dimensions',
        format: 'Format',
        uploadedAt: 'Uploaded At',
        status: 'Status',
        noImages: 'No images found',
      },
      segmentation: {
        title: 'Segmentation',
        start: 'Start Segmentation',
        stop: 'Stop Segmentation',
        edit: 'Edit Segmentation',
        export: 'Export Results',
        algorithm: 'Algorithm',
        parameters: 'Parameters',
        cellCount: 'Cell Count',
        processingTime: 'Processing Time',
        status: {
          pending: 'Pending',
          processing: 'Processing',
          completed: 'Completed',
          failed: 'Failed',
        },
      },
      cell: {
        title: 'Cells',
        area: 'Area',
        perimeter: 'Perimeter',
        circularity: 'Circularity',
        classification: 'Classification',
        confidence: 'Confidence',
        features: 'Features',
      },
      export: {
        title: 'Export',
        format: 'Format',
        options: 'Options',
        includeImages: 'Include Images',
        includeMetadata: 'Include Metadata',
        includeSegmentation: 'Include Segmentation',
        selectFormat: 'Select Format',
        exportSuccess: 'Export successful',
        exportFailed: 'Export failed',
      },
      error: {
        notFound: 'Not Found',
        unauthorized: 'Unauthorized',
        forbidden: 'Forbidden',
        serverError: 'Server Error',
        networkError: 'Network Error',
        validationError: 'Validation Error',
        unknownError: 'Unknown Error',
      },
      validation: {
        required: 'This field is required',
        email: 'Invalid email address',
        minLength: 'Must be at least {{min}} characters',
        maxLength: 'Must be at most {{max}} characters',
        min: 'Must be at least {{min}}',
        max: 'Must be at most {{max}}',
        pattern: 'Invalid format',
        passwordMatch: 'Passwords do not match',
      },
    },
  },
};

// Initialize i18n for tests
i18n
  .use(initReactI18next)
  .init({
    lng: 'en',
    fallbackLng: 'en',
    ns: ['translation'],
    defaultNS: 'translation',
    resources: testTranslations,
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

export default i18n;