import { useState, useEffect, useCallback, useRef } from 'react';
import { imageService, type ImageData } from '../services/imageService';
import { unifiedWebSocketService } from '../services/unifiedWebSocketService';
import { SEGMENTATION_STATUS } from '../constants/segmentationStatus';
import { useStore } from '../store';

interface UseProjectImagesOptions {
  projectId: string;
  pageSize?: number;
  autoRefresh?: boolean;
}

interface UseProjectImagesReturn {
  images: ImageData[];
  loading: boolean;
  error: string | null;
  totalImages: number;
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
  
  // Actions
  fetchImages: (page?: number) => Promise<void>;
  addImages: (newImages: ImageData[]) => void;
  updateImage: (imageId: string, updates: Partial<ImageData>) => void;
  removeImage: (imageId: string) => void;
  uploadImages: (files: File[]) => Promise<void>;
  triggerSegmentation: (imageId: string) => Promise<void>;
  batchTriggerSegmentation: (imageIds: string[]) => Promise<void>;
  refreshImages: () => Promise<void>;
}

export function useProjectImages(options: UseProjectImagesOptions): UseProjectImagesReturn {
  const { projectId, pageSize = 50, autoRefresh = true } = options;
  
  const [images, setImages] = useState<ImageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalImages, setTotalImages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  
  const addNotification = useStore((state) => state.addNotification);
  const mountedRef = useRef(true);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch images from API
  const fetchImages = useCallback(async (page = 1) => {
    if (!mountedRef.current) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await imageService.fetchProjectImages(projectId, page, pageSize);
      
      if (!mountedRef.current) return;
      
      console.log('[useProjectImages] Fetched images:', {
        count: response.data.length,
        sample: response.data.slice(0, 2).map(img => ({
          id: img.id,
          name: img.name,
          status: img.status,
          segmentationStatus: img.segmentationStatus
        }))
      });
      
      setImages(response.data);
      setTotalImages(response.total);
      setCurrentPage(response.pagination.page);
      setTotalPages(response.pagination.totalPages);
      setHasMore(response.pagination.hasMore);
    } catch (err) {
      if (!mountedRef.current) return;
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch images';
      setError(errorMessage);
      addNotification({
        type: 'error',
        title: 'Error loading images',
        message: errorMessage,
      });
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [projectId, pageSize, addNotification]);

  // Add new images to the list
  const addImages = useCallback((newImages: ImageData[]) => {
    setImages(prev => [...newImages, ...prev]);
    setTotalImages(prev => prev + newImages.length);
  }, []);

  // Update a single image
  const updateImage = useCallback((imageId: string, updates: Partial<ImageData>) => {
    setImages(prev => prev.map(img => {
      if (img.id === imageId) {
        // If we're updating the status, log it for debugging
        if (updates.status || updates.segmentationStatus) {
          console.log('[useProjectImages] Updating image in state:', {
            imageId,
            oldStatus: img.status,
            newStatus: updates.status || updates.segmentationStatus,
            updates
          });
        }
        return { ...img, ...updates };
      }
      return img;
    }));
  }, []);

  // Remove an image from the list
  const removeImage = useCallback((imageId: string) => {
    setImages(prev => prev.filter(img => img.id !== imageId));
    setTotalImages(prev => Math.max(0, prev - 1));
  }, []);

  // Upload images
  const uploadImages = useCallback(async (files: File[]) => {
    try {
      setLoading(true);
      const uploadedImages = await imageService.uploadImages(projectId, files);
      
      if (!mountedRef.current) return;
      
      // Add uploaded images to the beginning of the list
      addImages(uploadedImages);
      
      addNotification({
        type: 'success',
        title: 'Upload successful',
        message: `${uploadedImages.length} image(s) uploaded successfully`,
      });
    } catch (err) {
      if (!mountedRef.current) return;
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload images';
      addNotification({
        type: 'error',
        title: 'Upload failed',
        message: errorMessage,
      });
      throw err;
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [projectId, addImages, addNotification]);

  // Trigger segmentation for a single image
  const triggerSegmentation = useCallback(async (imageId: string) => {
    try {
      await imageService.triggerSegmentation(projectId, imageId);
      
      // Update image status to queued
      updateImage(imageId, { 
        status: SEGMENTATION_STATUS.QUEUED,
        segmentationStatus: SEGMENTATION_STATUS.QUEUED,
      });
      
      addNotification({
        type: 'info',
        title: 'Segmentation started',
        message: 'Image has been queued for segmentation',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start segmentation';
      addNotification({
        type: 'error',
        title: 'Segmentation failed',
        message: errorMessage,
      });
      throw err;
    }
  }, [projectId, updateImage, addNotification]);

  // Trigger batch segmentation
  const batchTriggerSegmentation = useCallback(async (imageIds: string[]) => {
    try {
      await imageService.batchTriggerSegmentation(imageIds);
      
      // Update all images to queued status
      imageIds.forEach(id => {
        updateImage(id, { 
          status: SEGMENTATION_STATUS.QUEUED,
          segmentationStatus: SEGMENTATION_STATUS.QUEUED,
        });
      });
      
      addNotification({
        type: 'info',
        title: 'Batch segmentation started',
        message: `${imageIds.length} images queued for segmentation`,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start batch segmentation';
      addNotification({
        type: 'error',
        title: 'Batch segmentation failed',
        message: errorMessage,
      });
      throw err;
    }
  }, [updateImage, addNotification]);

  // Refresh images
  const refreshImages = useCallback(async () => {
    await fetchImages(currentPage);
  }, [fetchImages, currentPage]);

  // Setup WebSocket listeners
  useEffect(() => {
    if (!projectId) return;

    // Join project room
    unifiedWebSocketService.joinProject(projectId);

    // Listen for segmentation updates
    const handleSegmentationUpdate = (data: any) => {
      console.log('[useProjectImages] Received segmentation update:', data);
      if (data.imageId) {
        updateImage(data.imageId, {
          status: data.status,
          segmentationStatus: data.status,
        });
        console.log('[useProjectImages] Updated image status:', {
          imageId: data.imageId,
          newStatus: data.status
        });
      }
    };

    // Listen for image created events
    const handleImageCreated = (data: any) => {
      if (data.projectId === projectId && data.image) {
        addImages([data.image]);
      }
    };

    // Listen for image updated events
    const handleImageUpdated = (data: any) => {
      if (data.imageId) {
        updateImage(data.imageId, data.updates);
      }
    };

    // Listen for image deleted events
    const handleImageDeleted = (data: any) => {
      if (data.imageId) {
        removeImage(data.imageId);
      }
    };

    // Subscribe to events
    unifiedWebSocketService.on('segmentation:update', handleSegmentationUpdate);
    unifiedWebSocketService.on('image:created', handleImageCreated);
    unifiedWebSocketService.on('image:updated', handleImageUpdated);
    unifiedWebSocketService.on('image:deleted', handleImageDeleted);

    return () => {
      // Leave project room
      unifiedWebSocketService.leaveProject(projectId);
      
      // Unsubscribe from events
      unifiedWebSocketService.off('segmentation:update', handleSegmentationUpdate);
      unifiedWebSocketService.off('image:created', handleImageCreated);
      unifiedWebSocketService.off('image:updated', handleImageUpdated);
      unifiedWebSocketService.off('image:deleted', handleImageDeleted);
    };
  }, [projectId, updateImage, addImages, removeImage]);

  // Setup polling for processing images
  useEffect(() => {
    if (!autoRefresh) return;

    const startPolling = () => {
      // Clear existing interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }

      // Check if any images are processing
      const hasProcessingImages = images.some(img => 
        img.status === SEGMENTATION_STATUS.QUEUED || 
        img.status === SEGMENTATION_STATUS.PROCESSING
      );

      if (hasProcessingImages) {
        // Poll every 5 seconds
        pollingIntervalRef.current = setInterval(() => {
          refreshImages();
        }, 5000);
      }
    };

    startPolling();

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [images, autoRefresh, refreshImages]);

  // Initial fetch
  useEffect(() => {
    mountedRef.current = true;
    fetchImages();

    return () => {
      mountedRef.current = false;
    };
  }, [fetchImages]);

  return {
    images,
    loading,
    error,
    totalImages,
    currentPage,
    totalPages,
    hasMore,
    fetchImages,
    addImages,
    updateImage,
    removeImage,
    uploadImages,
    triggerSegmentation,
    batchTriggerSegmentation,
    refreshImages,
  };
}