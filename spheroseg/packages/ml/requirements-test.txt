# Testing requirements for ML service
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1
pytest-timeout>=2.1.0

# ML dependencies needed for tests
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0
opencv-python>=4.8.0
pillow>=10.0.0
scikit-image>=0.21.0

# Web framework for API tests
flask>=3.0.0
requests>=2.31.0
requests-mock>=1.11.0

# Message queue for integration tests
pika>=1.3.2

# Utilities
psutil>=5.9.5  # For memory usage tests
python-dotenv>=1.0.0  # For environment variable management