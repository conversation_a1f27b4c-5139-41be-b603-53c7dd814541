# Multi-stage Dockerfile for production build
# Stage 1: Build the application
FROM node:18-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Copy package files from frontend directory
COPY packages/frontend/package*.json ./
COPY packages/frontend/tsconfig*.json ./
COPY packages/frontend/vite.config.ts ./
COPY packages/frontend/vite.config.shared.ts ./
COPY packages/frontend/vite.config.enhanced.ts ./
COPY packages/frontend/vite-static-fix.js ./
COPY packages/frontend/postcss.config.cjs ./
COPY packages/frontend/tailwind.config.js ./
COPY packages/frontend/index.html ./
COPY packages/frontend/.env.production ./

# Copy source code from frontend directory
COPY packages/frontend/src ./src
COPY packages/frontend/public ./public

# Copy shared packages
COPY packages/shared ./shared
COPY packages/types ./types

# Install dependencies
RUN npm install --legacy-peer-deps --force

# Set up symbolic links for shared packages
RUN mkdir -p node_modules/@spheroseg && \
    ln -sf /app/shared node_modules/@spheroseg/shared && \
    ln -sf /app/types node_modules/@spheroseg/types

# Build the application for production
ENV NODE_ENV=production
ENV VITE_API_URL=/api
ENV VITE_API_BASE_URL=/api
ENV VITE_API_AUTH_PREFIX=/auth
ENV VITE_API_USERS_PREFIX=/users
ENV VITE_ASSETS_URL=/assets

RUN npm run build

# Stage 2: Serve with nginx
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy nginx configuration
COPY packages/frontend/nginx-new.conf /etc/nginx/nginx.conf

# Copy built assets from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]