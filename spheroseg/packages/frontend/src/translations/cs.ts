// Czech translations
export default {
  // Segmentation context menu
  segmentation: {
    contextMenu: {
      editPolygon: 'Upravit polygon',
      splitPolygon: 'Rozdělit polygon',
      deletePolygon: 'Smazat polygon',
      confirmDeleteTitle: 'Opravdu chcete smazat polygon?',
      confirmDeleteMessage: 'Tato akce je nevratná. Polygon bude trvale odstraněn ze segmentace.',
      duplicateVertex: 'Duplikovat vrchol',
      deleteVertex: 'Smazat vrchol',
    },
    title: 'Editor segmentace',
    resolution: '{width}x{height}',
    batch: {
      mixed: 'Segmentace: {{successCount}} obrázk<PERSON> úspěšně zařazeno do fronty, {{failCount}} selhalo',
      allSuccess: 'Segmentace: Všech {{count}} obrázků úspěšně zařazeno do fronty',
      allFailed: 'Segmentace: Všech {{count}} obrázků selhalo',
    },
    queue: {
      title: 'Fronta segmentace',
      summary: '{{total}} úko<PERSON><PERSON> celkem ({{running}} zpracovává, {{queued}} ve frontě)',
      noRunningTasks: 'Žádné běžící úkoly',
      noQueuedTasks: 'Žádné úkoly ve frontě',
      task: 'Úkol',
      statusRunning: 'Segmentace: {{count}} běží{{queued}}',
      statusQueued: ', {{count}} ve frontě',
      statusOnlyQueued: 'Segmentace: {{count}} ve frontě',
      statusOnlyQueued_one: 'Segmentace: 1 ve frontě',
      statusOnlyQueued_other: 'Segmentace: {{count}} ve frontě',
      processing: 'Zpracovává se',
      queued: 'Ve frontě',
      statusProcessing: 'Segmentace: {{count}} se zpracovává',
      statusReady: 'Připraveno',
      tasksTotal: '{{total}} úkolů celkem ({{running}} zpracovává, {{queued}} ve frontě)',
    },
    selectPolygonForEdit: 'Vyberte polygon pro úpravu',
    selectPolygonForSlice: 'Vyberte polygon pro rozdělení',
    selectPolygonForAddPoints: 'Vyberte polygon pro přidání bodů',
    clickToAddPoint: 'Klikněte pro přidání bodu',
    clickToCompletePolygon: 'Klikněte na první bod pro dokončení polygonu',
    clickToAddFirstSlicePoint: 'Klikněte pro přidání prvního bodu řezu',
    clickToAddSecondSlicePoint: 'Klikněte pro přidání druhého bodu řezu',
    polygonCreationMode: 'Režim vytváření polygonu',
    polygonEditMode: 'Režim úpravy polygonu',
    polygonSliceMode: 'Režim dělení polygonu',
    polygonAddPointsMode: 'Režim přidávání bodů',
    viewMode: 'Režim zobrazení',
    totalPolygons: 'Celkem polygonů',
    totalVertices: 'Celkem vrcholů',
    vertices: 'Vrcholy',
    zoom: 'Přiblížení',
    mode: 'Režim',
    selected: 'Vybráno',
    none: 'Žádný',
    polygons: 'Polygony',
    imageNotFound: 'Obrázek nenalezen',
    returnToProject: 'Zpět na projekt',
    backToProject: 'Zpět na projekt',
    previousImage: 'Předchozí obrázek',
    nextImage: 'Další obrázek',
    toggleShortcuts: 'Přepnout zkratky',
    modes: {
      view: 'Režim zobrazení',
      edit: 'Režim úprav',
      create: 'Režim vytváření',
      slice: 'Režim dělení',
      addPoints: 'Režim přidávání bodů',
      deletePolygon: 'Režim mazání polygonu',
      createPolygon: 'Režim vytváření polygonu',
      editVertices: 'Režim úpravy vrcholů',
      editMode: 'Režim úprav',
      slicingMode: 'Režim dělení',
      pointAddingMode: 'Režim přidávání bodů',
    },
    status: {
      processing: 'Zpracovává se',
      queued: 'Ve frontě',
      completed: 'Dokončeno',
      failed: 'Selhalo',
      pending: 'Čeká',
      withoutSegmentation: 'Bez segmentace',
    },
    autoSave: {
      enabled: 'Automatické ukládání: Zapnuto',
      disabled: 'Automatické ukládání: Vypnuto',
      idle: 'Automatické ukládání: Nečinné',
      pending: 'Čeká se...',
      saving: 'Ukládá se...',
      success: 'Uloženo',
      error: 'Chyba',
    },
    loading: 'Načítání segmentace...',
    polygon: 'Polygon',
    unsavedChanges: 'Neuložené změny',
    noData: 'Nejsou k dispozici žádná data segmentace',
    noPolygons: 'Nebyly nalezeny žádné polygony',
    regions: 'Segmentace',
    position: 'Pozice',
    polygonDeleted: 'Polygon byl úspěšně smazán',
    saveSuccess: 'Segmentace byla úspěšně uložena',
    resegmentSuccess: 'Resegmentace byla úspěšně spuštěna',
    resegmentComplete: 'Resegmentace byla úspěšně dokončena',
    resegmentError: 'Resegmentace selhala',
    resegmentButton: 'Resegmentovat',
    completedSegmentation: 'Dokončeno',
    resegmentButtonTooltip: 'Resegmentovat pomocí neuronové sítě',
    helpTips: {
      title: 'Tipy:',
      edit: {
        createPoint: 'Klikněte pro vytvoření nového bodu',
        shiftPoints: 'Držte Shift pro automatické vytvoření sekvence bodů',
        closePolygon: 'Uzavřete polygon kliknutím na první bod',
      },
      slice: {
        start: 'Klikněte pro začátek řezu',
        finish: 'Klikněte znovu pro dokončení řezu',
        cancel: 'Esc pro zrušení dělení',
      },
      addPoint: {
        hover: 'Najeďte kurzorem na čáru polygonu',
        click: 'Klikněte pro přidání bodu do vybraného polygonu',
        exit: 'Esc pro ukončení režimu přidávání',
      },
      view: {
        pan: 'Posun: Klikněte a táhněte',
        selectPolygon: 'Výběr: Klikněte na polygon',
        zoom: 'Přiblížení: Kolečko myši',
      },
    },
    processingImage: 'Zpracování obrázku...',
    imageNotFoundDescription: 'Požadovaný obrázek nebyl nalezen',
    invalidImageDimensions: 'Neplatné rozměry obrázku',
    noDataToSave: 'Žádné změny k uložení',
    polygonDuplicated: 'Polygon zduplikován',
    polygonNotFound: 'Polygon nenalezen',
    polygonSimplified: 'Polygon zjednodušen',
    polygonSimplifyFailed: 'Zjednodušení polygonu selhalo',
    polygonSliced: 'Polygon úspěšně rozdělen',
    resegment: {
      error: {
        exception: 'Chyba resegmentace: {{error}}',
        failed: 'Resegmentace selhala',
        missingData: 'Chybějící požadovaná data pro resegmentaci',
      },
      success: 'Resegmentace byla úspěšně dokončena',
    },
    resegmentMultipleError: 'Chyba při resegmentaci více obrázků',
    resegmentMultipleSuccess: 'Více obrázků bylo úspěšně resegmentováno',
    resegmenting: 'Resegmentace...',
    resegmentingMultiple: 'Resegmentace více obrázků...',
    saveError: 'Chyba při ukládání segmentace',
    segmentationLoading: 'Načítání segmentace...',
    segmentationPolygon: 'Polygon segmentace',
    selectPolygonFirst: 'Nejprve prosím vyberte polygon',
    sliceFailed: 'Rozdělení polygonu selhalo',
    undoRestored: 'Akce vrácena zpět',
    undoWhileDraggingError: 'Nelze vrátit zpět během přetahování',
    vertexDeleteFailed: 'Smazání vrcholu selhalo',
    vertexDeleted: 'Vrchol smazán',
    vertexDuplicateFailed: 'Duplikace vrcholu selhala',
    vertexDuplicated: 'Vrchol zduplikován',
  },
  // Error messages
  errors: {
    somethingWentWrong: 'Něco se pokazilo',
    componentError: 'V této komponentě došlo k chybě',
    errorDetails: 'Podrobnosti chyby',
    tryAgain: 'Zkusit znovu',
    reloadPage: 'Obnovit stránku',
    goBack: 'Vrátit se zpět',
    notFound: 'Stránka nenalezena',
    pageNotFoundMessage: 'Požadovaná stránka nebyla nalezena',
    returnToHome: 'Zpět na úvodní stránku',
    unauthorized: 'Neautorizovaný přístup',
    forbidden: 'Přístup zakázán',
    serverError: 'Chyba serveru',
    networkError: 'Chyba sítě',
    timeoutError: 'Časový limit požadavku vypršel',
    validationError: 'Chyba validace',
    unknownError: 'Neznámá chyba',
    goHome: 'Přejít na úvodní stránku',
    fetchSegmentationFailed: 'Načtení segmentace selhalo',
    fetchImageFailed: 'Načtení obrázku selhalo',
    saveSegmentationFailed: 'Uložení segmentace selhalo',
    missingPermissions: 'Nedostatečná oprávnění',
    invalidInput: 'Neplatný vstup',
    resourceNotFound: 'Zdroj nenalezen',
  },
  project: {
    detail: {
      noImagesSelected: 'Nejsou vybrány žádné obrázky',
      triggeringResegmentation: 'Spouštění resegmentace pro {{count}} obrázků...',
      deleteConfirmation: 'Opravdu chcete smazat {{count}} obrázků? Tuto akci nelze vrátit zpět.',
      deletingImages: 'Mazání {{count}} obrázků...',
      deleteSuccess: 'Úspěšně smazáno {{count}} obrázků',
      deleteFailed: 'Smazání {{count}} obrázků selhalo',
      preparingExport: 'Příprava exportu {{count}} obrázků...',
    },
    segmentation: {
      processingInBatches: 'Spouštění segmentace pro {{count}} obrázků v {{batches}} dávkách...',
      batchQueued: 'Dávka {{current}}/{{total}} úspěšně zařazena do fronty',
      batchQueuedFallback: 'Dávka {{current}}/{{total}} úspěšně zařazena do fronty (záložní koncový bod)',
      batchError: 'Chyba při zpracování dávky {{current}}/{{total}}',
      partialSuccess: 'Segmentace: {{success}} obrázků úspěšně zařazeno do fronty, {{failed}} selhalo',
      allSuccess: 'Segmentace: Všech {{count}} obrázků úspěšně zařazeno do fronty',
      allFailed: 'Segmentace: Všech {{count}} obrázků selhalo',
      startedImages: 'Segmentace spuštěna pro {{count}} obrázků',
      queuedLocallyWarning: 'Segmentace lokálně zařazena do fronty pro {{count}} obrázků. Připojení k serveru selhalo.',
    },
    loading: 'Načítání projektu...',
    notFound: 'Projekt nenalezen',
    error: 'Chyba při načítání projektu',
    empty: 'Tento projekt je prázdný',
    noImagesText: 'V tomto projektu nebyly nalezeny žádné obrázky',
    addImages: 'Přidejte obrázky pro začátek',
    noImages: {
      title: 'Zatím žádné obrázky',
      description: 'Tento projekt zatím neobsahuje žádné obrázky. Nahrajte obrázky pro začátek.',
      uploadButton: 'Nahrát obrázky',
    },
    deleteProject: 'Smazat projekt',
    deleteConfirmation: 'Opravdu chcete smazat projekt "{{projectName}}"? Tuto akci nelze vrátit zpět.',
    duplicateProject: 'Duplikovat projekt',
    duplicateDescription: 'Vytvořit kopii tohoto projektu. Nový projekt bude vytvořen s názvem, který zadáte níže.',
    newProjectName: 'Název nového projektu',
    enterProjectName: 'Zadejte název nového projektu',
    duplicate: 'Duplikovat',
    deleteImage: 'Smazat obrázek',
    resegmentImage: 'Znovu segmentovat obrázek',
  },
  projectsPage: {
    title: 'Projekty',
    description: 'Spravovat výzkumné projekty',
    createNew: 'Vytvořit nový projekt',
    createProject: 'Vytvořit projekt',
    createProjectDesc: 'Zahájit nový výzkumný projekt',
    projectName: 'Název projektu',
    projectDescription: 'Popis projektu',
    projectNamePlaceholder: 'Zadejte název projektu',
    projectDescriptionPlaceholder: 'Zadejte popis projektu',
    projectCreated: 'Projekt byl úspěšně vytvořen',
    projectCreationFailed: 'Vytvoření projektu selhalo',
    projectDeleted: 'Projekt byl úspěšně smazán',
    projectDeletionFailed: 'Smazání projektu selhalo',
    confirmDelete: 'Opravdu chcete smazat tento projekt?',
    confirmDeleteDescription:
      'Tuto akci nelze vrátit zpět. Všechna data spojená s tímto projektem budou trvale smazána.',
    deleteProject: 'Smazat projekt',
    editProject: 'Upravit projekt',
    viewProject: 'Zobrazit projekt',
    projectUpdated: 'Projekt byl úspěšně aktualizován',
    projectUpdateFailed: 'Aktualizace projektu selhala',
    noProjects: 'Nebyly nalezeny žádné projekty',
    createFirstProject: 'Vytvořte svůj první projekt pro začátek',
    searchProjects: 'Hledat projekty...',
    filterProjects: 'Filtrovat projekty',
    sortProjects: 'Seřadit projekty',
    projectNameRequired: 'Název projektu je povinný',
    loginRequired: 'Pro vytvoření projektu musíte být přihlášeni',
    createdAt: 'Vytvořeno',
    updatedAt: 'Naposledy aktualizováno',
    imageCount: 'Obrázky',
    status: 'Stav',
    actions: 'Akce',
    loading: 'Načítání projektů...',
    error: 'Chyba při načítání projektů',
    retry: 'Zkusit znovu',
    duplicating: 'Duplikování projektu...',
    duplicate: 'Duplikovat',
    duplicateSuccess: 'Projekt byl úspěšně zduplikován',
    duplicateFailed: 'Duplikace projektu selhala',
    duplicateTitle: 'Duplikovat projekt',
    duplicateProject: 'Duplikovat projekt',
    duplicateProjectDescription: 'Vytvořit kopii tohoto projektu včetně všech obrázků. Níže můžete upravit možnosti.',
    duplicateCancelled: 'Duplikace projektu byla zrušena',
    duplicatingProject: 'Duplikování projektu',
    duplicatingProjectDescription: 'Váš projekt se duplikuje. Může to chvíli trvat.',
    duplicateProgress: 'Průběh duplikace',
    duplicationComplete: 'Duplikace projektu byla dokončena',
    duplicationTaskFetchError: 'Chyba při načítání dat úkolu',
    duplicationCancelError: 'Chyba při rušení duplikace',
    duplicateProgressDescription: 'Váš projekt se duplikuje. Tento proces může u velkých projektů nějakou dobu trvat.',
    duplicationPending: 'Čeká',
    duplicationProcessing: 'Zpracovává se',
    duplicationCompleted: 'Dokončeno',
    duplicationFailed: 'Selhalo',
    duplicationCancelled: 'Zrušeno',
    duplicationCancellationFailed: 'Zrušení duplikace selhalo',
    duplicationSuccessMessage: 'Projekt byl úspěšně zduplikován! Nyní můžete přistupovat k novému projektu.',
    copySegmentations: 'Kopírovat výsledky segmentace',
    resetImageStatus: 'Resetovat stav zpracování obrázků',
    newProjectTitle: 'Nový název projektu',
    itemsProcessed: 'položek zpracováno',
    items: 'položek',
    unknownProject: 'Neznámý projekt',
    activeTasks: 'Aktivní',
    allTasks: 'Všechny',
    noActiveDuplications: 'Žádné aktivní duplikace',
    noDuplications: 'Nebyly nalezeny žádné úkoly duplikace',
    deleteProjectDescription: 'Tato akce trvale smaže projekt a všechna související data.',
    deleteWarning: 'Tuto akci nelze vrátit zpět. Všechna data spojená s tímto projektem budou trvale smazána.',
    untitledProject: 'Nepojmenovaný projekt',
    typeToConfirm: 'Napište "delete" pro potvrzení',
    deleteConfirm: 'Opravdu chcete smazat tento projekt?',
    exportProject: 'Exportovat projekt',
    archived: 'Archivováno',
    completed: 'Dokončeno',
    draft: 'Koncept',
    active: 'Aktivní',
    createDate: 'Vytvořeno',
    lastModified: 'Naposledy upraveno',
    projectDescPlaceholder: 'Zadejte popis projektu',
    creatingProject: 'Vytváření projektu...',
    noImages: {
      title: 'Zatím žádné obrázky',
      description: 'Tento projekt zatím neobsahuje žádné obrázky. Nahrajte obrázky pro začátek segmentace.',
      uploadButton: 'Nahrát obrázky',
    },
  },
  common: {
    appName: 'Segmentace sféroidů',
    appNameShort: 'SpheroSeg',
    loading: 'Načítání...',
    loadingAccount: 'Načítání vašeho účtu...',
    loadingApplication: 'Načítání aplikace...',
    selectAll: 'Vybrat vše',
    deselectAll: 'Odznačit vše',
    save: 'Uložit',
    cancel: 'Zrušit',
    delete: 'Smazat',
    edit: 'Upravit',
    create: 'Vytvořit',
    search: 'Hledat',
    error: 'Chyba',
    success: 'Úspěch',
    reset: 'Resetovat',
    clear: 'Vymazat',
    close: 'Zavřít',
    back: 'Zpět',
    signIn: 'Přihlásit se',
    signUp: 'Registrovat se',
    signOut: 'Odhlásit se',
    signingIn: 'Přihlašování...',
    settings: 'Nastavení',
    profile: 'Profil',
    dashboard: 'Nástěnka',
    project: 'Projekt',
    projects: 'Projekty',
    newProject: 'Nový projekt',
    upload: 'Nahrát',
    download: 'Stáhnout',
    removeAll: 'Odstranit vše',
    uploadImages: 'Nahrát obrázky',
    recentAnalyses: 'Nedávné analýzy',
    noProjects: 'Nebyly nalezeny žádné projekty',
    noImages: 'Nebyly nalezeny žádné obrázky',
    createYourFirst: 'Vytvořte svůj první projekt pro začátek',
    tryAgain: 'Zkusit znovu',
    email: 'E-mail',
    password: 'Heslo',
    confirmPassword: 'Potvrdit heslo',
    firstName: 'Jméno',
    lastName: 'Příjmení',
    username: 'Uživatelské jméno',
    name: 'Jméno',
    description: 'Popis',
    date: 'Datum',
    status: 'Stav',
    image: 'Obrázek',
    projectName: 'Název projektu',
    projectDescription: 'Popis projektu',
    language: 'Jazyk',
    theme: 'Motiv',
    light: 'Světlý',
    dark: 'Tmavý',
    system: 'Systémový',
    welcome: 'Vítejte na platformě pro segmentaci sféroidů',
    account: 'Účet',
    passwordConfirm: 'Potvrdit heslo',
    manageAccount: 'Spravovat účet',
    changePassword: 'Změnit heslo',
    deleteAccount: 'Smazat účet',
    requestAccess: 'Požádat o přístup',
    accessRequest: 'Žádost o přístup',
    createAccount: 'Vytvořit účet',
    signInToAccount: 'Přihlásit se k účtu',
    termsOfService: 'Podmínky služby',
    privacyPolicy: 'Zásady ochrany osobních údajů',
    termsOfServiceLink: 'Podmínky služby',
    privacyPolicyLink: 'Zásady ochrany osobních údajů',
    optional: 'Volitelné',
    saveChanges: 'Uložit změny',
    saving: 'Ukládání',
    notSpecified: 'Nespecifikováno',
    enable: 'Povolit',
    disable: 'Zakázat',
    backToHome: 'Zpět domů',
    and: 'a',
    lastChange: 'Poslední změna',
    sort: 'Seřadit',
    emailPlaceholder: 'Zadejte váš e-mail',
    passwordPlaceholder: 'Zadejte vaše heslo',
    export: 'Exportovat',
    selectImages: 'Vybrat obrázky',
    noImagesDescription: 'Nahrajte obrázky pro začátek práce s projektem',
    yes: 'Ano',
    no: 'Ne',
    images: 'Obrázky',
    files: 'Soubory',
    validationFailed: 'Validace selhala',
    cropAvatar: 'Oříznout profilový obrázek',
    profileTitle: 'Profil',
    profileDescription: 'Aktualizovat profilové informace viditelné ostatním uživatelům',
    profileUsername: 'Uživatelské jméno',
    profileUsernamePlaceholder: 'Zadejte vaše uživatelské jméno',
    profileFullName: 'Celé jméno',
    profileFullNamePlaceholder: 'Zadejte vaše celé jméno',
    profileTitlePlaceholder: 'např. Výzkumník, Profesor',
    profileOrganization: 'Organizace',
    profileOrganizationPlaceholder: 'Zadejte vaši organizaci nebo instituci',
    profileBio: 'Bio',
    profileBioPlaceholder: 'Napište krátké bio o sobě',
    profileBioDescription: 'Stručný popis vašich výzkumných zájmů a odbornosti',
    profileLocation: 'Umístění',
    profileLocationPlaceholder: 'např. Praha, Česká republika',
    profileSaveButton: 'Uložit profil',
    actions: 'Akce',
    view: 'Zobrazit',
    share: 'Sdílet',
    projectNamePlaceholder: 'Zadejte název projektu',
    projectDescPlaceholder: 'Zadejte popis projektu',
    creatingProject: 'Vytváření projektu...',
    createSuccess: 'Projekt byl úspěšně vytvořen',
    unauthorized: 'Nemáte oprávnění k provedení této akce',
    forbidden: 'Přístup zakázán',
    maxFileSize: 'Max. velikost souboru: {{size}}MB',
    accepted: 'Přijato',
    processing: 'Zpracovává se...',
    uploading: 'Nahrává se...',
    uploadComplete: 'Nahrávání dokončeno',
    uploadFailed: 'Nahrávání selhalo',
    deletePolygon: 'Smazat polygon',
    pleaseLogin: 'Pro pokračování se prosím přihlaste',
    retry: 'Zkusit znovu',
    segmentation: 'Segmentace',
    copiedToClipboard: 'Zkopírováno do schránky!',
    failedToCopy: 'Kopírování do schránky selhalo',
    confirm: 'Potvrdit',
    editor: {
      error: 'Chyba',
      success: 'Úspěch',
      edit: 'Upravit',
      create: 'Vytvořit',
    },
    closeWindow: 'Zavřít okno',
  },
  auth: {
    signIn: 'Přihlásit se',
    signUp: 'Registrovat se',
    signOut: 'Odhlásit se',
    signingIn: 'Přihlašování...',
    email: 'E-mail',
    password: 'Heslo',
    forgotPassword: 'Zapomněli jste heslo?',
    resetPassword: 'Resetovat heslo',
    dontHaveAccount: 'Nemáte účet?',
    alreadyHaveAccount: 'Již máte účet?',
    createAccount: 'Vytvořit účet',
    signInWithGoogle: 'Přihlásit se pomocí Google',
    signInWithGithub: 'Přihlásit se pomocí GitHubu',
    or: 'nebo',
    signInTitle: 'Přihlášení',
    signInDescription: 'Přihlaste se ke svému účtu',
    noAccount: 'Nemáte účet?',
    emailAddressLabel: 'E-mailová adresa',
    passwordLabel: 'Heslo',
    currentPasswordLabel: 'Současné heslo',
    newPasswordLabel: 'Nové heslo',
    confirmPasswordLabel: 'Potvrdit heslo',
    rememberMe: 'Zapamatovat si mě',
    emailRequired: 'E-mail je povinný',
    passwordRequired: 'Heslo je povinné',
    alreadyLoggedInTitle: 'Již jste přihlášeni',
    alreadyLoggedInMessage: 'Již jste přihlášeni ke svému účtu',
    goToDashboardLink: 'Přejít na nástěnku',
    invalidEmail: 'Neplatná e-mailová adresa',
    passwordTooShort: 'Heslo musí mít alespoň 6 znaků',
    passwordsDontMatch: 'Hesla se neshodují',
    invalidCredentials: 'Neplatný e-mail nebo heslo',
    accountCreated: 'Účet byl úspěšně vytvořen',
    resetLinkSent: 'Odkaz pro resetování hesla byl odeslán na váš e-mail',
    resetSuccess: 'Heslo bylo úspěšně resetováno',
    signInSuccess: 'Přihlášení bylo úspěšné',
    signOutSuccess: 'Odhlášení bylo úspěšné',
    sessionExpired: 'Vaše relace vypršela. Přihlaste se prosím znovu.',
    unauthorized: 'Nemáte oprávnění k přístupu k tomuto zdroji',
    verifyEmail: 'Ověřte prosím svou e-mailovou adresu',
    verificationLinkSent: 'Ověřovací odkaz byl odeslán na váš e-mail',
    verificationSuccess: 'E-mail byl úspěšně ověřen',
    resendVerification: 'Znovu poslat ověřovací e-mail',
    requestAccess: 'Požádat o přístup',
    termsAndPrivacy: 'Registrací souhlasíte s našimi podmínkami služby a zásadami ochrany osobních údajů.',
    forgotPasswordLink: 'Zapomněli jste heslo?',
    passwordChanged: 'Heslo bylo úspěšně změněno',
    currentPasswordIncorrect: 'Současné heslo je nesprávné',
    registerTitle: 'Vytvořit účet',
    registerDescription: 'Zaregistrujte se pro nový účet',
    registerSuccess: 'Registrace byla úspěšná! Nyní se můžete přihlásit.',
    emailPlaceholder: 'Zadejte váš e-mail',
    passwordPlaceholder: 'Zadejte vaše heslo',
    firstNamePlaceholder: 'např. Jan',
    lastNamePlaceholder: 'např. Novák',
    passwordConfirmPlaceholder: 'Potvrďte vaše heslo',
    signUpTitle: 'Vytvořit účet',
    signUpDescription: 'Zaregistrujte se pro nový účet',
    enterInfoCreateAccount: 'Zadejte své údaje pro vytvoření účtu',
    creatingAccount: 'Vytváření účtu...',
    emailAlreadyExists: 'Tento e-mail je již registrován. Použijte jiný e-mail nebo se přihlaste.',
    emailHasPendingRequest: 'Tento e-mail již má čekající žádost o přístup. Počkejte prosím na schválení.',
    signUpSuccess: 'Úspěšně registrováno!',
    signUpSuccessEmail: 'Registrace byla úspěšná! Zkontrolujte svůj e-mail nebo počkejte na schválení administrátora.',
    signUpFailed: 'Registrace selhala. Zkuste to prosím znovu.',
    alreadyHaveAccess: 'Již máte přístup?',
    forgotPasswordTitle: 'Resetovat heslo',
    checkYourEmail: 'Zkontrolujte svůj e-mail pro nové heslo',
    enterEmailForReset: 'Zadejte svou e-mailovou adresu a my vám pošleme nové heslo',
    passwordResetLinkSent: 'Pokud účet pro tento e-mail existuje, bylo odesláno nové heslo',
    passwordResetFailed: 'Odeslání nového hesla selhalo. Zkuste to prosím znovu.',
    enterEmail: 'Zadejte prosím svou e-mailovou adresu',
    sendingResetLink: 'Odesílání nového hesla...',
    sendResetLink: 'Odeslat nové heslo',
    backToSignIn: 'Zpět na přihlášení',
    fillAllFields: 'Vyplňte prosím všechna pole',
    verifyingEmail: 'Ověřování e-mailu',
    emailVerified: 'E-mail ověřen!',
    verificationError: 'Chyba ověření',
    emailVerifiedDescription: 'Váš e-mail byl úspěšně ověřen.',
    canCloseWindow: 'Nyní můžete zavřít toto okno a přihlásit se ke svému účtu.',
    verifyingEmailDescription: 'Počkejte prosím, zatímco ověřujeme vaši e-mailovou adresu...',
    invalidVerificationLink: 'Neplatný ověřovací odkaz',
    verificationFailed: 'Ověření e-mailu selhalo',
    accountLocked: 'Váš účet byl uzamčen. Kontaktujte prosím podporu.',
    serverError: 'Chyba serveru. Zkuste to prosím později.',
    signInError: 'Chyba při přihlašování',
    signInFailed: 'Přihlášení selhalo. Zkontrolujte prosím své přihlašovací údaje.',
  },
  requestAccess: {
    and: 'a',
    title: 'Požádat o přístup k platformě Segmentace sféroidů',
    description:
      'Vyplňte následující formulář pro žádost o přístup k naší platformě. Vaši žádost zkontrolujeme a brzy vás kontaktujeme.',
    emailLabel: 'Vaše e-mailová adresa',
    nameLabel: 'Vaše jméno',
    institutionLabel: 'Instituce/Společnost',
    reasonLabel: 'Důvod pro přístup',
    submitRequest: 'Odeslat žádost',
    requestReceived: 'Žádost přijata',
    thankYou: 'Děkujeme za váš zájem',
    weWillContact: 'Vaši žádost zkontrolujeme a brzy vás kontaktujeme',
    submitSuccess: 'Žádost byla úspěšně odeslána!',
    emailPlaceholder: 'Zadejte vaši e-mailovou adresu',
    namePlaceholder: 'Zadejte vaše celé jméno',
    institutionPlaceholder: 'Zadejte název vaší instituce nebo společnosti',
    reasonPlaceholder: 'Popište prosím, jak plánujete platformu používat',
    fillRequired: 'Vyplňte prosím všechna povinná pole',
    submittingRequest: 'Odesílání žádosti...',
    submitError: 'Odeslání žádosti selhalo',
    alreadyPending: 'Žádost o přístup pro tento e-mail již čeká na vyřízení',
    agreeToTerms: 'Odesláním této žádosti souhlasíte s našimi',
  },
  requestAccessForm: {
    title: 'Požádat o přístup k platformě Segmentace sféroidů',
    description:
      'Vyplňte následující formulář pro žádost o přístup k naší platformě. Vaši žádost zkontrolujeme a brzy vás kontaktujeme.',
    emailLabel: 'Vaše e-mailová adresa',
    nameLabel: 'Vaše jméno',
    institutionLabel: 'Instituce/Společnost',
    reasonLabel: 'Důvod pro přístup',
    submitButton: 'Odeslat žádost',
    signInPrompt: 'Již máte účet?',
    signInLink: 'Přihlásit se',
    thankYouTitle: 'Děkujeme za váš zájem',
    weWillContact: 'Vaši žádost zkontrolujeme a brzy vás kontaktujeme',
    agreeToTerms: 'Odesláním této žádosti souhlasíte s našimi',
    and: 'a',
  },
  documentation: {
    tag: 'Uživatelská příručka',
    title: 'Dokumentace SpheroSeg',
    subtitle: 'Naučte se efektivně používat platformu pro segmentaci sféroidů.',
    sidebar: {
      title: 'Sekce',
      introduction: 'Úvod',
      gettingStarted: 'Začínáme',
      uploadingImages: 'Nahrávání obrázků',
      segmentationProcess: 'Proces segmentace',
      apiReference: 'Reference API',
    },
    introduction: {
      title: 'Úvod',
      imageAlt: 'Ilustrace pracovního postupu analýzy sféroidů',
      whatIs: {
        title: 'Co je SpheroSeg?',
        paragraph1:
          'SpheroSeg je špičková platforma navržená pro segmentaci a analýzu buněčných sféroidů v mikroskopických obrázcích. Náš nástroj poskytuje výzkumníkům přesné detekční a analytické schopnosti.',
        paragraph2:
          'Využívá pokročilé AI algoritmy založené na hlubokém učení pro automatickou identifikaci a segmentaci sféroidů ve vašich obrázcích s vysokou přesností a konzistencí.',
        paragraph3:
          'Tato dokumentace vás provede všemi aspekty používání platformy, od začátku až po pokročilé funkce a integraci API.',
      },
    },
    gettingStarted: {
      title: 'Začínáme',
      accountCreation: {
        title: 'Vytvoření účtu',
        paragraph1:
          'Pro použití SpheroSeg potřebujete vytvořit účet. To nám umožňuje bezpečně ukládat vaše projekty a obrázky.',
        step1Prefix: 'Navštivte',
        step1Link: 'registrační stránku',
        step2: 'Zadejte svou institucionální e-mailovou adresu a vytvořte heslo',
        step3: 'Dokončete svůj profil se svým jménem a institucí',
        step4: 'Ověřte svou e-mailovou adresu pomocí odkazu zaslaného do vaší schránky',
      },
      creatingProject: {
        title: 'Vytvoření prvního projektu',
        paragraph1:
          'Projekty vám pomáhají organizovat vaši práci. Každý projekt může obsahovat více obrázků a jejich odpovídající výsledky segmentace.',
        step1: 'Na nástěnce klikněte na "Nový projekt"',
        step2: 'Zadejte název a popis projektu',
        step3: 'Vyberte typ projektu (výchozí: Analýza sféroidů)',
        step4: 'Klikněte na "Vytvořit projekt" pro pokračování',
      },
    },
    uploadingImages: {
      title: 'Nahrávání obrázků',
      paragraph1: 'SpheroSeg podporuje různé formáty obrázků běžně používané v mikroskopii, včetně TIFF, PNG a JPEG.',
      methods: {
        title: 'Metody nahrávání',
        paragraph1: 'Existuje několik způsobů, jak nahrát obrázky:',
        step1: 'Přetáhněte soubory přímo do oblasti pro nahrávání',
        step2: 'Klikněte na oblast pro nahrávání pro procházení a výběr souborů z vašeho počítače',
        step3: 'Hromadné nahrání více obrázků najednou',
      },
      note: {
        prefix: 'Poznámka:',
        text: 'Pro optimální výsledky zajistěte, aby vaše mikroskopické obrázky měly dobrý kontrast mezi sféroidem a pozadím.',
      },
    },
    segmentationProcess: {
      title: 'Proces segmentace',
      paragraph1:
        'Proces segmentace identifikuje hranice sféroidů ve vašich obrázcích, což umožňuje přesnou analýzu jejich morfologie.',
      automatic: {
        title: 'Automatická segmentace',
        paragraph1: 'Naše automatická segmentace poháněná AI dokáže detekovat hranice sféroidů s vysokou přesností:',
        step1: 'Vyberte obrázek z vašeho projektu',
        step2: 'Klikněte na "Auto-segmentovat" pro zahájení procesu',
        step3: 'Systém zpracuje obrázek a zobrazí detekované hranice',
        step4: 'Zkontrolujte výsledky v editoru segmentace',
      },
      manual: {
        title: 'Manuální úpravy',
        paragraph1: 'Někdy může automatická segmentace vyžadovat zpřesnění. Náš editor poskytuje nástroje pro:',
        step1: 'Přidávání nebo odebírání vrcholů podél hranice',
        step2: 'Úpravu pozic vrcholů pro přesnější hranice',
        step3: 'Rozdělení nebo sloučení oblastí',
        step4: 'Přidávání nebo odebírání děr uvnitř sféroidů',
      },
    },
    apiReference: {
      title: 'Reference API',
      paragraph1:
        'SpheroSeg nabízí RESTful API pro programový přístup k funkcím platformy. To je ideální pro integraci s vašimi stávajícími pracovními postupy nebo hromadné zpracování.',
      endpoint1Desc: 'Získá seznam všech vašich projektů',
      endpoint2Desc: 'Získá všechny obrázky v rámci konkrétního projektu',
      endpoint3Desc: 'Zahájí segmentaci pro konkrétní obrázek',
      contactPrefix: 'Pro úplnou dokumentaci API a podrobnosti o autentizaci nás prosím kontaktujte na',
    },
    backToHome: 'Zpět domů',
    backToTop: 'Zpět nahoru',
  },
  hero: {
    platformTag: 'Pokročilá platforma pro segmentaci sféroidů',
    title: 'Analýza buněk s umělou inteligencí pro biomedicínský výzkum',
    subtitle:
      'Posuňte analýzu mikroskopických buněčných obrázků s naší špičkovou platformou pro segmentaci sféroidů. Navrženo pro výzkumníky hledající přesnost a efektivitu.',
    getStartedButton: 'Začít',
    learnMoreButton: 'Zjistit více',
    imageAlt1: 'Mikroskopický obrázek sféroidu',
    imageAlt2: 'Mikroskopický obrázek sféroidu s analýzou',
    welcomeTitle: 'Vítejte v SpheroSeg',
    welcomeSubtitle: 'Pokročilá platforma pro segmentaci a analýzu buněčných sféroidů',
    welcomeDescription:
      'Naše platforma kombinuje špičkové algoritmy umělé inteligence s intuitivním rozhraním pro přesnou detekci a analýzu buněčných sféroidů v mikroskopických obrázcích.',
    featuresTitle: 'Výkonné funkce',
    featuresSubtitle: 'Pokročilé nástroje pro biomedicínský výzkum',
    featureAiSegmentation: 'Pokročilá segmentace',
    featureAiSegmentationDesc: 'Přesná detekce sféroidů s analýzou hranic pro přesná měření buněk.',
    featureEditing: 'Analýza s umělou inteligencí',
    featureEditingDesc: 'Využijte algoritmy hlubokého učení pro automatickou detekci a klasifikaci buněk.',
    featureAnalytics: 'Snadné nahrávání',
    featureAnalyticsDesc: 'Přetáhněte své mikroskopické obrázky pro okamžité zpracování a analýzu.',
    featureExport: 'Statistické poznatky',
    featureExportDesc: 'Komplexní metriky a vizualizace pro extrakci smysluplných datových vzorců.',
    ctaTitle: 'Jste připraveni transformovat svůj pracovní postup analýzy buněk?',
    ctaSubtitle: 'Připojte se k předním výzkumníkům, kteří již používají naši platformu k urychlení svých objevů.',
    ctaButton: 'Vytvořit účet',
  },
  navbar: {
    home: 'Domů',
    features: 'Funkce',
    documentation: 'Dokumentace',
    terms: 'Podmínky',
    privacy: 'Soukromí',
    login: 'Přihlásit se',
    requestAccess: 'Požádat o přístup',
    openMobileMenu: 'Otevřít mobilní menu',
    closeMobileMenu: 'Zavřít mobilní menu',
  },
  navigation: {
    home: 'Domů',
    projects: 'Projekty',
    settings: 'Nastavení',
    profile: 'Profil',
    dashboard: 'Nástěnka',
    back: 'Zpět',
  },
  dashboard: {
    manageProjects: 'Spravovat a organizovat své výzkumné projekty',
    viewMode: {
      grid: 'Zobrazení mřížky',
      list: 'Zobrazení seznamu',
    },
    sort: {
      name: 'Název',
      updatedAt: 'Naposledy aktualizováno',
      segmentationStatus: 'Stav',
    },
    search: 'Hledat projekty...',
    searchImagesPlaceholder: 'Hledat obrázky...',
    noProjects: 'Nebyly nalezeny žádné projekty',
    noImagesDescription: 'Žádné obrázky neodpovídají vašim kritériím vyhledávání',
    createFirst: 'Vytvořte svůj první projekt pro začátek',
    createNew: 'Vytvořit nový projekt',
    lastChange: 'Poslední změna',
    statsOverview: 'Přehled statistik',
    totalProjects: 'Celkem projektů',
    activeProjects: 'Aktivní projekty',
    totalImages: 'Celkem obrázků',
    totalAnalyses: 'Celkem analýz',
    lastUpdated: 'Naposledy aktualizováno',
    noProjectsDescription: 'Zatím jste nevytvořili žádné projekty. Vytvořte svůj první projekt pro začátek.',
    searchProjectsPlaceholder: 'Hledat projekty podle názvu...',
    sortBy: 'Seřadit podle',
    name: 'Název',
    completed: 'Dokončeno',
    processing: 'Zpracovává se',
    pending: 'Čeká',
    failed: 'Selhalo',
    selectImagesButton: 'Vybrat obrázky',
  },
  projects: {
    title: 'Projekty',
    description: 'Spravovat své výzkumné projekty',
    createNew: 'Vytvořit nový projekt',
    createProject: 'Vytvořit projekt',
    createProjectDesc: 'Vytvořit nový projekt pro začátek práce s obrázky a segmentací.',
    projectName: 'Název projektu',
    projectDescription: 'Popis projektu',
    projectNamePlaceholder: 'Zadejte název projektu',
    projectDescriptionPlaceholder: 'Zadejte popis projektu',
    projectCreated: 'Projekt byl úspěšně vytvořen',
    projectCreationFailed: 'Vytvoření projektu selhalo',
    projectDeleted: 'Projekt byl úspěšně smazán',
    deleteSuccess: 'Projekt byl úspěšně smazán',
    deleteFailed: 'Smazání projektu selhalo',
    deleting: 'Mazání projektu...',
    notFound: 'Projekt nebyl nalezen. Možná byl již smazán.',
    missingId: 'Nelze smazat projekt: chybí identifikátor projektu',
    projectDeletionFailed: 'Smazání projektu selhalo',
    confirmDelete: 'Opravdu chcete smazat tento projekt?',
    confirmDeleteDescription:
      'Tuto akci nelze vrátit zpět. Všechna data spojená s tímto projektem budou trvale smazána.',
    delete: 'Smazat',
    deleteProject: 'Smazat projekt',
    deleteProjectDescription: 'Tuto akci nelze vrátit zpět. Tím se trvale smaže projekt a všechna související data.',
    deleteWarning: 'Chystáte se smazat následující projekt:',
    typeToConfirm: 'Napište název projektu pro potvrzení',
    confirmDeleteError: 'Pro potvrzení napište přesně název projektu',
    editProject: 'Upravit projekt',
    viewProject: 'Zobrazit projekt',
    projectUpdated: 'Projekt byl úspěšně aktualizován',
    projectUpdateFailed: 'Aktualizace projektu selhala',
    noProjects: 'Nebyly nalezeny žádné projekty',
    createFirstProject: 'Vytvořte svůj první projekt pro začátek',
    searchProjects: 'Hledat projekty...',
    filterProjects: 'Filtrovat projekty',
    sortProjects: 'Seřadit projekty',
    projectNameRequired: 'Název projektu je povinný',
    loginRequired: 'Pro vytvoření projektu musíte být přihlášeni',
    createdAt: 'Vytvořeno',
    updatedAt: 'Naposledy aktualizováno',
    imageCount: 'Obrázky',
    status: 'Stav',
    actions: 'Akce',
    loading: 'Načítání projektů...',
    error: 'Chyba při načítání projektů',
    retry: 'Opakovat',
    duplicating: 'Duplikování projektu...',
    duplicate: 'Duplikovat',
    duplicateSuccess: 'Projekt byl úspěšně zduplikován',
    duplicateFailed: 'Duplikace projektu selhala',
    duplicateTitle: 'Duplikovat projekt',
    duplicateProject: 'Duplikovat projekt',
    duplicateProjectDescription: 'Vytvořit kopii tohoto projektu včetně všech obrázků. Níže můžete upravit možnosti.',
    duplicateCancelled: 'Duplikace projektu byla zrušena',
    duplicatingProject: 'Duplikování projektu',
    duplicatingProjectDescription: 'Váš projekt se duplikuje. Může to chvíli trvat.',
    duplicateProgress: 'Průběh duplikace',
    duplicationComplete: 'Duplikace projektu byla dokončena',
    duplicationTaskFetchError: 'Chyba při načítání dat úkolu',
    duplicationCancelError: 'Chyba při rušení duplikace',
    duplicateProgressDescription: 'Váš projekt se duplikuje. Tento proces může u velkých projektů nějakou dobu trvat.',
    duplicationPending: 'Čeká',
    duplicationProcessing: 'Zpracovává se',
    duplicationCompleted: 'Dokončeno',
    duplicationFailed: 'Selhalo',
    duplicationCancelled: 'Zrušeno',
    duplicationCancellationFailed: 'Zrušení duplikace selhalo',
    duplicationSuccessMessage: 'Projekt byl úspěšně zduplikován! Nyní můžete přistupovat k novému projektu.',
    copySegmentations: 'Kopírovat výsledky segmentace',
    resetImageStatus: 'Resetovat stav zpracování obrázků',
    newProjectTitle: 'Nový název projektu',
    itemsProcessed: 'položek zpracováno',
    items: 'položek',
    unknownProject: 'Neznámý projekt',
    activeTasks: 'Aktivní',
    allTasks: 'Všechny',
    noActiveDuplications: 'Žádné aktivní duplikace',
    noDuplications: 'Nebyly nalezeny žádné úkoly duplikace',
    untitledProject: 'Nepojmenovaný projekt',
    exportProject: 'Exportovat projekt',
    share: 'Sdílet',
    export: 'Exportovat',
    archived: 'Archivováno',
    completed: 'Dokončeno',
    draft: 'Koncept',
    active: 'Aktivní',
  },
  projectToolbar: {
    selectImages: 'Vybrat obrázky',
    cancelSelection: 'Zrušit výběr',
    export: 'Exportovat',
    uploadImages: 'Nahrát obrázky',
  },
  statsOverview: {
    title: 'Přehled nástěnky',
    totalProjects: 'Celkem projektů',
    totalImages: 'Celkem obrázků',
    completedSegmentations: 'Dokončené segmentace',
    storageUsed: 'Využité úložiště',
    recentActivity: 'Nedávná aktivita',
    moreStats: 'Zobrazit podrobné statistiky',
    completion: 'míra dokončení',
    vsLastMonth: 'oproti minulému měsíci',
    thisMonth: 'Tento měsíc',
    lastMonth: 'Minulý měsíc',
    projectsCreated: 'Vytvořené projekty',
    imagesUploaded: 'Nahrané obrázky',
    fetchError: 'Načtení statistik selhalo',
    storageLimit: 'Limit úložiště',
    activityTitle: 'Nedávná aktivita',
    noActivity: 'Žádná nedávná aktivita',
    hide: 'Skrýt',
    activityTypes: {
      project_created: 'Vytvořen projekt',
      image_uploaded: 'Nahrán obrázek',
      segmentation_completed: 'Dokončena segmentace',
    },
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FJFI ČVUT v Praze',
    description: 'Pokročilá platforma pro segmentaci a analýzu sféroidů',
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FJFI ČVUT v Praze',
    resourcesTitle: 'Zdroje',
    documentationLink: 'Dokumentace',
    featuresLink: 'Funkce',
    tutorialsLink: 'Návody',
    researchLink: 'Výzkum',
    legalTitle: 'Právní informace',
    termsLink: 'Podmínky služby',
    privacyLink: 'Zásady ochrany osobních údajů',
    contactUsLink: 'Kontaktujte nás',
    informationTitle: 'Informace',
    contactTitle: 'Kontakt',
    copyrightNotice: 'SpheroSeg. Všechna práva vyhrazena.',
    madeWith: 'Vytvořeno s',
    by: 'od',
    requestAccessLink: 'Požádat o přístup',
    githubRepository: 'GitHub repozitář',
    contactEmail: 'Kontaktní e-mail',
  },
  features: {
    tag: 'Funkce',
    title: 'Objevte možnosti naší platformy',
    subtitle: 'Pokročilé nástroje pro biomedicínský výzkum',
    cards: {
      segmentation: {
        title: 'Pokročilá segmentace',
        description: 'Přesná detekce sféroidů s analýzou hranic pro přesná měření buněk',
      },
      aiAnalysis: {
        title: 'Analýza s umělou inteligencí',
        description: 'Využijte algoritmy hlubokého učení pro automatickou detekci a klasifikaci buněk',
      },
      uploads: {
        title: 'Snadné nahrávání',
        description: 'Přetáhněte své mikroskopické obrázky pro okamžité zpracování a analýzu',
      },
      insights: {
        title: 'Statistické poznatky',
        description: 'Komplexní metriky a vizualizace pro extrakci smysluplných datových vzorců',
      },
      collaboration: {
        title: 'Týmová spolupráce',
        description: 'Sdílejte projekty a výsledky s kolegy pro efektivnější výzkum',
      },
      pipeline: {
        title: 'Automatizovaný pipeline',
        description: 'Zjednodušte svůj pracovní postup s našimi nástroji pro hromadné zpracování',
      },
    },
  },
  index: {
    about: {
      tag: 'O platformě',
      title: 'Co je SpheroSeg?',
      imageAlt: 'Příklad segmentace sféroidu',
      paragraph1:
        'SpheroSeg je pokročilá platforma speciálně navržená pro segmentaci a analýzu buněčných sféroidů v mikroskopických obrázcích.',
      paragraph2:
        'Náš nástroj kombinuje špičkové algoritmy umělé inteligence s intuitivním rozhraním, aby poskytl výzkumníkům přesnou detekci hranic sféroidů a analytické schopnosti.',
      paragraph3:
        'Platforma byla vyvinuta Michalem Průškem z FJFI ČVUT v Praze pod vedením Adama Novozámského z ÚTIA AV ČR, ve spolupráci s výzkumníky z Ústavu biochemie a mikrobiologie VŠCHT Praha.',
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: 'Jste připraveni transformovat svůj výzkum?',
      subtitle: 'Začněte používat SpheroSeg ještě dnes a objevte nové možnosti v analýze buněčných sféroidů',
      boxTitle: 'Vytvořte si bezplatný účet',
      boxText: 'Získejte přístup ke všem funkcím platformy a začněte analyzovat své mikroskopické obrázky',
      button: 'Vytvořit účet',
    },
  },
  tools: {
    zoomIn: 'Přiblížit',
    zoomOut: 'Oddálit',
    resetView: 'Resetovat zobrazení',
    createPolygon: 'Vytvořit nový polygon',
    exitPolygonCreation: 'Ukončit režim vytváření polygonu',
    splitPolygon: 'Rozdělit polygon na dva',
    exitSlicingMode: 'Ukončit režim dělení',
    addPoints: 'Přidat body do polygonu',
    exitPointAddingMode: 'Ukončit režim přidávání bodů',
    undo: 'Zpět',
    redo: 'Znovu',
    save: 'Uložit',
    resegment: 'Resegmentovat',
    title: 'Nástroje',
  },
  settings: {
    title: 'Nastavení',
    pageTitle: 'Nastavení',
    profile: 'Profil',
    account: 'Účet',
    appearance: 'Vzhled',
    profileSettings: 'Nastavení profilu',
    accountSettings: 'Nastavení účtu',
    securitySettings: 'Nastavení zabezpečení',
    preferenceSettings: 'Nastavení preferencí',
    selectLanguage: 'Vybrat jazyk',
    selectTheme: 'Vybrat motiv',
    updateProfile: 'Aktualizovat profil',
    changePassword: 'Změnit heslo',
    deleteAccount: 'Smazat účet',
    savedChanges: 'Změny byly úspěšně uloženy',
    saveChanges: 'Uložit změny',
    profileUpdated: 'Profil byl úspěšně aktualizován',
    languageSettings: 'Nastavení jazyka',
    themeSettings: 'Nastavení motivu',
    privacySettings: 'Nastavení soukromí',
    exportData: 'Exportovat data',
    importData: 'Importovat data',
    uploadAvatar: 'Nahrát profilový obrázek',
    removeAvatar: 'Odstranit profilový obrázek',
    twoFactorAuth: 'Dvoufaktorové ověření',
    emailNotifications: 'E-mailová oznámení',
    pushNotifications: 'Push oznámení',
    weeklyDigest: 'Týdenní přehled',
    monthlyReport: 'Měsíční zpráva',
    displaySettings: 'Nastavení zobrazení',
    accessibilitySettings: 'Nastavení přístupnosti',
    advancedSettings: 'Pokročilá nastavení',
    useBrowserLanguage: 'Použít jazyk prohlížeče',
    language: 'Jazyk',
    theme: 'Motiv',
    light: 'Světlý',
    dark: 'Tmavý',
    system: 'Systémový',
    languageUpdated: 'Jazyk byl úspěšně aktualizován',
    themeUpdated: 'Motiv byl úspěšně aktualizován',
    toggleTheme: 'Přepnout motiv',
    languageDescription: 'Vyberte preferovaný jazyk',
    themeDescription: 'Vyberte preferovaný motiv',
    profileLoadError: 'Načtení profilu selhalo',
    appearanceDescription: 'Přizpůsobte vzhled aplikace',
    personal: 'Osobní informace',
    fullName: 'Celé jméno',
    organization: 'Organizace',
    department: 'Oddělení',
    publicProfile: 'Veřejný profil',
    makeProfileVisible: 'Zviditelnit můj profil ostatním výzkumníkům',
    passwordSettings: 'Nastavení hesla',
    currentPassword: 'Současné heslo',
    newPassword: 'Nové heslo',
    confirmNewPassword: 'Potvrdit nové heslo',
    dangerZone: 'Nebezpečná zóna',
    deleteAccountWarning: 'Jakmile smažete svůj účet, není cesty zpět. Všechna vaše data budou trvale smazána.',
    savingChanges: 'Ukládání změn...',
    savePreferences: 'Uložit preference',
    usernameTaken: 'Toto uživatelské jméno je již obsazeno',
    deleteAccountDescription: 'Tato akce je nevratná. Všechna vaše data budou trvale smazána.',
    confirmUsername: 'Potvrďte svůj e-mail',
    password: 'Heslo',
    enterPassword: 'Zadejte své heslo',
    passwordChangeError: 'Chyba při změně hesla',
    passwordChangeSuccess: 'Heslo bylo úspěšně změněno',
    passwordsDoNotMatch: 'Hesla se neshodují',
    accountDeleteSuccess: 'Účet byl úspěšně smazán',
    accountDeleteError: 'Chyba při mazání účtu',
    passwordChanged: 'Heslo změněno',
    confirmPasswordLabel: 'Potvrdit heslo',
    changePasswordDescription: 'Změňte své heslo pro zabezpečení vašeho účtu',
    dangerZoneDescription: 'Tyto akce jsou nevratné a trvale odstraní vaše data',
    deletingAccount: 'Mazání účtu...',
    deleteAccountError: 'Chyba při mazání účtu',
    changingPassword: 'Změna hesla...',
  },
  accessibility: {
    skipToContent: 'Přeskočit na hlavní obsah',
  },
  profile: {
    title: 'Titul',
    about: 'O mně',
    activity: 'Aktivita',
    projects: 'Projekty',
    recentProjects: 'Nedávné projekty',
    recentAnalyses: 'Nedávné analýzy',
    accountDetails: 'Podrobnosti účtu',
    accountType: 'Typ účtu',
    joinDate: 'Datum registrace',
    lastActive: 'Naposledy aktivní',
    projectsCreated: 'Vytvořené projekty',
    imagesUploaded: 'Nahrané obrázky',
    segmentationsCompleted: 'Dokončené segmentace',
    pageTitle: 'Uživatelský profil',
    editProfile: 'Upravit profil',
    joined: 'Připojil se',
    statistics: 'Statistiky',
    images: 'Obrázky',
    analyses: 'Analýzy',
    storageUsed: 'Využité úložiště',
    recentActivity: 'Nedávná aktivita',
    noRecentActivity: 'Žádná nedávná aktivita',
    fetchError: 'Načtení dat profilu selhalo',
    aboutMe: 'O mně',
    noBio: 'Žádné bio nebylo poskytnuto',
    avatarHelp: 'Klikněte na ikonu fotoaparátu pro nahrání profilového obrázku',
    avatarImageOnly: 'Vyberte prosím soubor obrázku',
    avatarTooLarge: 'Obrázek musí být menší než 5MB',
    avatarUpdated: 'Profilový obrázek byl aktualizován',
    avatarUploadError: 'Nahrání profilového obrázku selhalo',
    avatarRemoved: 'Profilový obrázek byl odstraněn',
    avatarRemoveError: 'Odstranění profilového obrázku selhalo',
    cropAvatarDescription: 'Upravte oblast ořezu pro nastavení profilového obrázku',
    description: 'Aktualizujte své osobní informace a profilový obrázek',
    saveButton: 'Uložit profil',
    username: 'Uživatelské jméno',
    usernamePlaceholder: 'Zadejte své uživatelské jméno',
    fullName: 'Celé jméno',
    fullNamePlaceholder: 'Zadejte své celé jméno',
    titlePlaceholder: 'např. Výzkumník, Profesor',
    organization: 'Organizace',
    organizationPlaceholder: 'Zadejte svou organizaci nebo instituci',
    bio: 'Bio',
    bioPlaceholder: 'Řekněte nám o sobě',
    bioDescription: 'Stručný popis o vás, který bude viditelný na vašem profilu',
    location: 'Umístění',
    locationPlaceholder: 'např. Praha, Česká republika',
    uploadAvatar: 'Nahrát profilový obrázek',
    removeAvatar: 'Odstranit profilový obrázek',
    cropAvatar: 'Oříznout profilový obrázek',
    activityDescription: 'Systémová aktivita',
    email: 'E-mail',
    notProvided: 'Neposkytnuto',
  },
  termsPage: {
    title: 'Podmínky služby',
    acceptance: {
      title: '1. Přijetí podmínek',
      paragraph1:
        'Přístupem nebo používáním SpheroSeg souhlasíte s tím, že budete vázáni těmito podmínkami služby a všemi platnými zákony a předpisy. Pokud nesouhlasíte s některými z těchto podmínek, je vám zakázáno tuto službu používat.',
    },
    useLicense: {
      title: '2. Licence k používání',
      paragraph1:
        'Je uděleno povolení k dočasnému používání SpheroSeg pouze pro osobní, nekomerční nebo akademické výzkumné účely. Jedná se o udělení licence, nikoli o převod vlastnictví.',
    },
    dataUsage: {
      title: '3. Použití dat',
      paragraph1:
        'Všechna data nahraná do SpheroSeg zůstávají vaším vlastnictvím. Nenárokujeme si vlastnictví vašeho obsahu, ale vyžadujeme určitá oprávnění k poskytování služby.',
    },
    limitations: {
      title: '4. Omezení',
      paragraph1:
        'V žádném případě nebude SpheroSeg odpovědný za jakékoli škody vzniklé z používání nebo nemožnosti používat platformu, i když jsme byli upozorněni na možnost takových škod.',
    },
    revisions: {
      title: '5. Revize a chyby',
      paragraph1:
        'Materiály objevující se na SpheroSeg mohou obsahovat technické, typografické nebo fotografické chyby. Nezaručujeme, že jakékoli materiály jsou přesné, úplné nebo aktuální.',
    },
    governingLaw: {
      title: '6. Rozhodné právo',
      paragraph1:
        'Tyto podmínky se řídí a vykládají v souladu se zákony země, ve které je služba hostována, a vy se neodvolatelně podřizujete výlučné jurisdikci soudů v této lokalitě.',
    },
    lastUpdated: 'Naposledy aktualizováno: 7. ledna 2025',
  },
  privacyPage: {
    title: 'Zásady ochrany osobních údajů',
    introduction: {
      title: '1. Úvod',
      paragraph1:
        'Tyto zásady ochrany osobních údajů vysvětlují, jak SpheroSeg ("my", "nás", "naše") shromažďuje, používá a sdílí vaše informace, když používáte naši platformu pro segmentaci a analýzu sféroidů.',
    },
    informationWeCollect: {
      title: '2. Informace, které shromažďujeme',
      paragraph1:
        'Shromažďujeme informace, které nám přímo poskytujete, když si vytváříte účet, nahráváte obrázky, vytváříte projekty a jinak komunikujete s našimi službami.',
    },
    personalInformation: {
      title: '2.1 Osobní informace',
      paragraph1:
        'To zahrnuje vaše jméno, e-mailovou adresu, instituci/organizaci a další informace, které poskytnete při vytváření účtu nebo žádosti o přístup k našim službám.',
    },
    researchData: {
      title: '2.2 Výzkumná data',
      paragraph1:
        'To zahrnuje obrázky, které nahráváte, podrobnosti o projektech, výsledky analýz a další data související s výzkumem, která vytvoříte nebo nahrajete na naši platformu.',
    },
    usageInformation: {
      title: '2.3 Informace o používání',
      paragraph1:
        'Shromažďujeme informace o tom, jak používáte naši platformu, včetně dat protokolů, informací o zařízení a vzorců používání.',
    },
    howWeUse: {
      title: '3. Jak používáme vaše informace',
      paragraph1:
        'Používáme informace, které shromažďujeme, k poskytování, údržbě a zlepšování našich služeb, ke komunikaci s vámi a k plnění našich právních povinností.',
    },
    dataSecurity: {
      title: '4. Zabezpečení dat',
      paragraph1:
        'Implementujeme vhodná bezpečnostní opatření k ochraně vašich osobních informací a výzkumných dat před neoprávněným přístupem, změnou, zveřejněním nebo zničením.',
    },
    dataSharing: {
      title: '5. Sdílení dat',
      paragraph1:
        'Neprodáváme vaše osobní informace ani výzkumná data. Můžeme sdílet vaše informace za omezených okolností, například s vaším souhlasem, k plnění právních povinností nebo s poskytovateli služeb, kteří nám pomáhají provozovat naši platformu.',
    },
    yourChoices: {
      title: '6. Vaše možnosti',
      paragraph1:
        'Můžete přistupovat, aktualizovat nebo smazat informace o svém účtu a výzkumná data prostřednictvím nastavení vašeho účtu. Můžete nás také kontaktovat s žádostí o přístup, opravu nebo smazání jakýchkoli osobních informací, které o vás máme.',
    },
    changes: {
      title: '7. Změny těchto zásad',
      paragraph1:
        'Tyto zásady ochrany osobních údajů můžeme čas od času aktualizovat. O jakýchkoli změnách vás budeme informovat zveřejněním nových zásad ochrany osobních údajů na této stránce a aktualizací data "Naposledy aktualizováno".',
    },
    contactUs: {
      title: '8. Kontaktujte nás',
      paragraph1:
        'Pokud máte jakékoli dotazy k těmto zásadám ochrany osobních údajů, kontaktujte nás prosí<NAME_EMAIL>.',
    },
    contact: {
      email: '<EMAIL>',
      paragraph1: 'Pokud máte jakékoli dotazy k těmto zásadám ochrany osobních údajů, kontaktujte nás prosím na:',
      title: '9. Kontaktujte nás',
    },
    cookies: {
      paragraph1:
        'Používáme cookies a podobné technologie k vylepšení vašeho zážitku, analýze používání webu a personalizaci obsahu.',
      title: '7. Cookies a sledovací technologie',
    },
    dataCollection: {
      list: [
        'Informace o účtu (e-mail, jméno, instituce)',
        'Nahrané obrázky a data pro segmentaci',
        'Metadata projektů a výsledky analýz',
        'Údaje o používání a protokoly aktivit',
      ],
      paragraph1: 'Shromažďujeme informace, které nám přímo poskytujete, včetně:',
      title: '2. Informace, které shromažďujeme',
    },
    dataStorage: {
      paragraph1:
        'Implementujeme vhodná technická a organizační opatření k ochraně vašich osobních informací před neoprávněným přístupem, změnou, zveřejněním nebo zničením.',
      paragraph2: 'Vaše data jsou uložena na zabezpečených serverech a jsou mazána podle našich zásad uchovávání dat.',
      title: '4. Ukládání dat a zabezpečení',
    },
    dataUsage: {
      list: [
        'Poskytovat a udržovat naše služby',
        'Zpracovávat vaše požadavky na segmentaci obrázků',
        'Zlepšovat naše algoritmy a služby',
        'Komunikovat s vámi o vašem účtu',
        'Zajistit bezpečnost a zabránit zneužití',
      ],
      paragraph1: 'Shromážděné informace používáme k:',
      title: '3. Jak používáme vaše informace',
    },
    userRights: {
      list: [
        'Přístup k vašim osobním informacím',
        'Opravu nepřesných informací',
        'Žádost o smazání vašich dat',
        'Export vašich dat',
        'Námitku proti zpracování vašich dat',
      ],
      paragraph1: 'Máte právo na:',
      title: '6. Vaše práva',
    },
    lastUpdated: 'Naposledy aktualizováno: 7. ledna 2025',
  },
  shortcuts: {
    button: 'Zkratky',
    editMode: 'Přepnout do režimu úprav',
    sliceMode: 'Přepnout do režimu dělení',
    addPointMode: 'Přepnout do režimu přidávání bodů',
    holdShift: 'Držet Shift pro automatické přidávání bodů (v režimu úprav)',
    undo: 'Zpět',
    redo: 'Znovu',
    deletePolygon: 'Smazat vybraný polygon',
    cancel: 'Zrušit aktuální operaci',
    zoomIn: 'Přiblížit',
    zoomOut: 'Oddálit',
    resetView: 'Resetovat zobrazení',
    title: 'Klávesové zkratky',
    viewMode: 'Režim zobrazení',
    editVerticesMode: 'Režim úpravy vrcholů',
    addPointsMode: 'Režim přidávání bodů',
    createPolygonMode: 'Režim vytváření polygonu',
    save: 'Uložit',
    description: 'Tyto zkratky fungují v editoru segmentace pro rychlejší a pohodlnější práci.',
  },
  imageProcessor: {
    segmentationStarted: 'Proces segmentace byl zahájen...',
    startSegmentationTooltip: 'Spustit segmentaci',
    processingTooltip: 'Zpracovává se...',
    savingTooltip: 'Ukládá se...',
    completedTooltip: 'Segmentace dokončena',
    retryTooltip: 'Opakovat segmentaci',
  },
  uploader: {
    dragDrop: 'Přetáhněte sem obrázky nebo klikněte pro výběr souborů',
    dropFiles: 'Pusťte soubory zde...',
    segmentAfterUploadLabel: 'Segmentovat obrázky ihned po nahrání',
    filesToUpload: 'Soubory k nahrání',
    uploadBtn: 'Nahrát',
    uploadError: 'Při nahrávání došlo k chybě. Zkuste to prosím znovu.',
    clickToUpload: 'Klikněte pro procházení souborů',
    selectProjectLabel: 'Vybrat projekt',
    selectProjectPlaceholder: 'Vyberte projekt...',
    noProjectsFound: 'Nebyly nalezeny žádné projekty. Nejprve vytvořte nový.',
    imageOnly: '(Pouze obrázkové soubory)',
    uploadingImages: 'Nahrávání obrázků...',
    uploadComplete: 'Nahrávání dokončeno',
    uploadFailed: 'Nahrávání selhalo',
    processingImages: 'Zpracování obrázků...',
    dragAndDropFiles: 'Přetáhněte sem soubory',
    or: 'nebo',
    clickToSelect: 'Klikněte pro výběr souborů',
  },
  images: {
    uploadImages: 'Nahrát obrázky',
    dragDrop: 'Přetáhněte sem obrázky',
    clickToSelect: 'nebo klikněte pro výběr souborů',
    acceptedFormats: 'Podporované formáty: JPEG, PNG, TIFF, BMP (max 10MB)',
    uploadProgress: 'Průběh nahrávání',
    uploadingTo: 'Nahrávání do',
    currentProject: 'Aktuální projekt',
    autoSegment: 'Automaticky segmentovat obrázky po nahrání',
    uploadCompleted: 'Nahrávání dokončeno',
    uploadFailed: 'Nahrávání selhalo',
    imagesUploaded: 'Obrázky byly úspěšně nahrány',
    imagesFailed: 'Nahrání obrázků selhalo',
    viewAnalyses: 'Zobrazit analýzy',
    noAnalysesYet: 'Zatím žádné analýzy',
    runAnalysis: 'Spustit analýzu',
    viewResults: 'Zobrazit výsledky',
    dropImagesHere: 'Pusťte obrázky zde...',
    selectProjectFirst: 'Nejprve prosím vyberte projekt',
    projectRequired: 'Před nahráním obrázků musíte vybrat projekt',
    imageOnly: '(Pouze obrázkové soubory)',
    dropFiles: 'Pusťte soubory zde...',
    filesToUpload: 'Soubory k nahrání ({{count}})',
    uploadBtn: 'Nahrát {{count}} obrázků',
    uploadError: 'Při nahrávání došlo k chybě. Zkuste to prosím znovu.',
    noProjectsToUpload: 'Žádné dostupné projekty. Nejprve vytvořte projekt.',
    notFound: 'Projekt "{{projectName}}" nebyl nalezen. Možná byl smazán.',
    errors: {
      imageOrProjectNotFound: 'Obrázek nebo projekt nebyl nalezen.',
      failedToDeleteImage: 'Nepodařilo se smazat obrázek',
      imageOrProjectNotFoundForNavigation: 'Obrázek nebo projekt nebyl nalezen pro navigaci, nebo chybí UUID.',
      imageNotFoundForClearingSegmentation: 'Obrázek pro vymazání segmentace nebyl nalezen nebo chybí UUID.',
      failedToClearSegmentation: 'Nepodařilo se odstranit segmentaci',
    },
    success: {
      localImageDeleted: 'Lokální obrázek byl úspěšně odstraněn',
      imageDeleted: 'Obrázek byl úspěšně smazán',
      segmentationCleared: 'Segmentace byla úspěšně odstraněna.',
    },
    info: {
      clearingSegmentation: 'Mažu segmentaci pro obrázek {{imageName}}...',
      selectAtLeastOneImage: 'Vyberte prosím alespoň jeden obrázek.',
    },
  },
  export: {
    formatDescriptions: {
      COCO: 'Formát JSON Common Objects in Context (COCO) pro detekci objektů',
      YOLO: 'Textový formát You Only Look Once (YOLO) pro detekci objektů',
      MASK: 'Binární maskové obrázky pro každý segmentovaný objekt',
      POLYGONS: 'Souřadnice polygonů ve formátu JSON',
      DATUMARO: 'Formát Datumaro - unifikovaná reprezentace datové sady',
      CVAT_MASKS: 'CVAT XML formát s polygonálními anotacemi',
      CVAT_YAML: 'CVAT YAML formát pro výměnu anotací',
    },
    exportCompleted: 'Export dokončen',
    exportFailed: 'Export selhal',
    title: 'Exportovat data segmentace',
    spheroidMetrics: 'Metriky sféroidů',
    visualization: 'Vizualizace',
    cocoFormat: 'Formát COCO',
    close: 'Zavřít',
    metricsExported: 'Metriky byly úspěšně exportovány',
    options: {
      includeMetadata: 'Zahrnout metadata',
      includeSegmentation: 'Zahrnout segmentaci',
      selectExportFormat: 'Vybrat formát exportu',
      includeObjectMetrics: 'Zahrnout metriky objektů',
      selectMetricsFormat: 'Vybrat formát metrik',
      metricsFormatDescription: {
        EXCEL: 'Soubor Excel (.xlsx)',
        CSV: 'Soubor CSV (.csv)',
      },
      includeImages: 'Zahrnout původní obrázky',
      exportMetricsOnly: 'Exportovat pouze metriky',
      metricsRequireSegmentation: 'Export metrik vyžaduje dokončenou segmentaci',
    },
    formats: {
      COCO: 'COCO JSON',
      YOLO: 'YOLO TXT',
      MASK: 'Maska (TIFF)',
      POLYGONS: 'Polygony (JSON)',
      DATUMARO: 'Datumaro',
      CVAT_MASKS: 'CVAT Masky (XML)',
      CVAT_YAML: 'CVAT YAML',
    },
    metricsFormats: {
      EXCEL: 'Excel (.xlsx)',
      CSV: 'CSV (.csv)',
    },
    selectImagesForExport: 'Vyberte obrázky pro export',
    selectImagesToExport: 'Vyberte obrázky k exportu',
    noImagesAvailable: 'Žádné obrázky nejsou k dispozici',
    backToProject: 'Zpět na projekt',
    exportImages: 'Exportovat obrázky',
    maskExportError: 'Chyba při exportu masky',
    maskExportStarted: 'Export masky zahájen',
    metricsRequireSegmentation: 'Metriky vyžadují dokončenou segmentaci',
    noImageSelectedError: 'Pro export nebyl vybrán žádný obrázek',
  },
  metrics: {
    area: 'Plocha',
    perimeter: 'Obvod',
    circularity: 'Kruhovost',
    sphericity: 'Sféričnost',
    solidity: 'Solidita',
    compactness: 'Kompaktnost',
    convexity: 'Konvexita',
    visualization: 'Vizualizace metrik',
    visualizationHelp: 'Vizuální reprezentace metrik pro všechny sferoidy v tomto obrázku',
    barChart: 'Sloupcový graf',
    pieChart: 'Koláčový graf',
    comparisonChart: 'Srovnávací graf',
    keyMetricsComparison: 'Porovnání klíčových metrik',
    areaDistribution: 'Distribuce plochy',
    shapeMetricsComparison: 'Porovnání tvarových metrik',
    noPolygonsFound: 'Pro analýzu nebyly nalezeny žádné polygony',
  },
  imageStatus: {
    completed: 'Zpracováno',
    processing: 'Zpracovává se',
    pending: 'Čeká',
    failed: 'Selhalo',
    noImage: 'Žádný obrázek',
    untitledImage: 'Nepojmenovaný obrázek',
  },
  projectActions: {
    duplicateTooltip: 'Duplikovat projekt',
    deleteTooltip: 'Smazat projekt',
    deleteConfirmTitle: 'Jste si jisti?',
    deleteConfirmDesc: 'Opravdu chcete smazat projekt "{{projectName}}"? Tuto akci nelze vrátit zpět.',
    deleteSuccess: 'Projekt "{{projectName}}" byl úspěšně smazán.',
    deleteError: 'Smazání projektu selhalo.',
    duplicateSuccess: 'Projekt "{{projectName}}" byl úspěšně zduplikován.',
    duplicateError: 'Duplikace projektu selhala.',
    makePrivateTooltip: 'Označit jako soukromý',
    makePublicTooltip: 'Označit jako veřejný',
    shareTooltip: 'Sdílet projekt',
    downloadTooltip: 'Stáhnout projekt',
    notFound: 'Projekt "{{projectName}}" nebyl nalezen. Možná byl již smazán.',
  },
  editor: {
    backButtonTooltip: 'Zpět na přehled projektu',
    exportButtonTooltip: 'Exportovat aktuální data segmentace',
    saveTooltip: 'Uložit změny',
    image: 'Obrázek',
    previousImage: 'Předchozí obrázek',
    nextImage: 'Další obrázek',
    resegmentButton: 'Resegmentovat',
    resegmentButtonTooltip: 'Znovu spustit segmentaci na tomto obrázku',
    exportMaskButton: 'Exportovat masku',
    exportMaskButtonTooltip: 'Exportovat segmentační masku pro tento obrázek',
    backButton: 'Zpět',
    exportButton: 'Exportovat',
    saveButton: 'Uložit',
    loadingProject: 'Načítání projektu...',
    loadingImage: 'Načítání obrázku...',
    sliceErrorInvalidPolygon: 'Nelze rozdělit: Vybraný polygon je neplatný.',
    sliceWarningInvalidResult: 'Dělení vytvořilo polygony, které jsou příliš malé a neplatné.',
    sliceWarningInvalidIntersections: 'Neplatný řez: Čára řezu musí protínat polygon přesně ve dvou bodech.',
    sliceSuccess: 'Polygon byl úspěšně rozdělen.',
    noPolygonToSlice: 'Nejsou k dispozici žádné polygony k rozdělení.',
    savingTooltip: 'Ukládá se...',
  },
  segmentationPage: {
    noImageSelected: 'Pro resegmentaci nebyl vybrán žádný obrázek.',
    resegmentationStarted: 'Spouští se resegmentace pomocí neuronové sítě ResUNet...',
    resegmentationQueued: 'Resegmentace byla zařazena do fronty.',
    resegmentationCompleted: 'Resegmentace byla úspěšně dokončena.',
    resegmentationFailed: 'Resegmentace selhala.',
    resegmentationTimeout: 'Vypršel časový limit resegmentace. Zkontrolujte stav fronty.',
    resegmentationError: 'Spuštění resegmentace selhalo.',
    resegmentTooltip: 'Resegmentovat',
  },
  share: {
    accepted: 'Přijato',
    alreadyShared: 'Již sdíleno s tímto uživatelem',
    canEdit: 'Může upravovat',
    copyToClipboard: 'Kopírovat do schránky',
    edit: 'Upravit',
    email: 'E-mail',
    failedToCopy: 'Kopírování odkazu selhalo',
    failedToGenerateLink: 'Generování odkazu pro sdílení selhalo',
    failedToLoadShares: 'Načtení sdílených uživatelů selhalo',
    failedToRemove: 'Odstranění sdílení selhalo',
    failedToShare: 'Sdílení projektu selhalo',
    generateLink: 'Vygenerovat odkaz',
    generateNewLink: 'Vygenerovat nový odkaz',
    generating: 'Generuje se...',
    invalidEmail: 'Neplatná e-mailová adresa',
    invalidEmailOrPermission: 'Neplatný e-mail nebo oprávnění',
    invite: 'Pozvat',
    inviteByEmail: 'Pozvat e-mailem',
    inviteByLink: 'Pozvat odkazem',
    linkCopied: 'Odkaz zkopírován do schránky',
    linkGenerated: 'Odkaz pro sdílení byl vygenerován',
    linkPermissions: 'Oprávnění odkazu',
    noPermission: 'Žádné oprávnění',
    noShares: 'Žádní sdílení uživatelé',
    pendingAcceptance: 'Čeká na přijetí',
    permissions: 'Oprávnění',
    projectNotFound: 'Projekt nenalezen',
    removeShare: 'Odstranit sdílení',
    selectAccessLevel: 'Vybrat úroveň přístupu',
    selectPermission: 'Vyberte prosím typ oprávnění',
    shareDescription: 'Sdílet tento projekt s ostatními uživateli',
    sharedWith: 'Sdíleno s',
    shareLinkDescription: 'Kdokoli s tímto odkazem může přistupovat k projektu',
    shareProject: 'Sdílet projekt',
    shareProjectTitle: 'Sdílet projekt "{{projectName}}"',
    sharing: 'Sdílí se...',
    sharedSuccess: 'Projekt "{{projectName}}" byl sdílen s {{email}}',
    removedSuccess: 'Sdílení s {{email}} bylo odstraněno',
    status: 'Stav',
    userEmail: 'E-mail uživatele',
    view: 'Zobrazit',
    viewOnly: 'Pouze zobrazit',
  },
  invitation: {
    acceptedSuccess: 'Pozvánka byla úspěšně přijata',
    createAccount: 'Vytvořit účet',
    errorTitle: 'Nelze přijmout pozvánku',
    expired: 'Tento odkaz pozvánky vypršel nebo je neplatný',
    genericError: 'Přijetí pozvánky selhalo. Zkuste to prosím znovu.',
    goToDashboard: 'Přejít na nástěnku',
    invalidLink: 'Neplatný odkaz pozvánky',
    loginMessage: 'Pro přijetí této pozvánky k projektu se prosím přihlaste.',
    loginRequired: 'Vyžadováno přihlášení',
    notForYou: 'Tato pozvánka není určena pro váš účet',
    processing: 'Zpracování pozvánky...',
    redirecting: 'Přesměrování na projekt...',
    signIn: 'Přihlásit se',
    successMessage: 'Nyní máte přístup k projektu "{{projectName}}" sdílenému uživatelem {{ownerName}}.',
    successTitle: 'Pozvánka přijata!',
    title: 'Pozvánka k projektu',
  },
  about: {
    contact: {
      description: 'Máte otázky nebo potřebujete podporu? Jsme tu, abychom vám pomohli!',
      email: 'Kontaktovat e-mailem',
      github: 'Zobrazit na GitHubu',
      title: 'Kontaktujte nás',
      twitter: 'Sledovat na Twitteru',
    },
    mission: {
      description:
        'SpheroSeg je pokročilá platforma speciálně navržená pro segmentaci a analýzu buněčných sféroidů v mikroskopických obrázcích. Kombinujeme špičkové algoritmy umělé inteligence s intuitivním rozhraním, abychom výzkumníkům poskytli přesnou detekci hranic sféroidů a analytické schopnosti.',
      title: 'Naše mise',
      vision:
        'Naší vizí je urychlit vědecké objevy tím, že zpřístupníme pokročilou analýzu obrazů výzkumníkům po celém světě a umožníme jim soustředit se na jejich výzkum místo technických výzev.',
    },
    team: {
      description:
        'SpheroSeg byl vyvinut oddaným týmem výzkumníků a inženýrů nadšených pro pokrok v biomedicínském výzkumu',
      member1: {
        name: 'Michal Průšek',
        role: 'Hlavní vývojář, FJFI ČVUT v Praze',
      },
      member2: {
        name: 'Adam Novozámský',
        role: 'Vedoucí, ÚTIA AV ČR',
      },
      member3: {
        name: 'Výzkumný tým',
        role: 'Ústav biochemie a mikrobiologie, VŠCHT Praha',
      },
      title: 'Náš tým',
    },
    technology: {
      description:
        'Postaveno na nejmodernějších modelech hlubokého učení a technikách počítačového vidění, SpheroSeg nabízí bezkonkurenční přesnost v segmentaci sféroidů.',
      feature1: {
        description:
          'Pokročilé modely hlubokého učení trénované na různorodých obrázcích sféroidů zajišťují přesné a spolehlivé výsledky segmentace.',
        title: 'Segmentace poháněná AI',
      },
      feature2: {
        description:
          'Optimalizované algoritmy poskytují rychlé časy zpracování, což vám umožňuje efektivně analyzovat velké datové sady.',
        title: 'Zpracování v reálném čase',
      },
      feature3: {
        description:
          'Extrahujte podrobné metriky včetně plochy, obvodu, kruhovosti a dalších pro každý segmentovaný sféroid.',
        title: 'Komplexní analýza',
      },
      title: 'Naše technologie',
    },
    title: 'O SpheroSeg',
  },
};
