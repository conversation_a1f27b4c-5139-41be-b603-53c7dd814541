// Spanish translations
export default {
  // Segmentation context menu
  segmentation: {
    batch: {
      mixed: 'Segmentación: {{successCount}} imágenes encoladas exitosamente, {{failCount}} fallidas',
      allSuccess: 'Segmentación: Todas las {{count}} imágenes encoladas exitosamente',
      allFailed: 'Segmentación: Todas las {{count}} imágenes fallaron',
    },
    contextMenu: {
      editPolygon: 'Editar polígono',
      splitPolygon: 'Dividir polígono',
      deletePolygon: 'Eliminar polígono',
      confirmDeleteTitle: '¿Está seguro de que desea eliminar el polígono?',
      confirmDeleteMessage: 'Esta acción es irreversible. El polígono se eliminará permanentemente de la segmentación.',
      duplicateVertex: 'Duplicar vértice',
      deleteVertex: 'Eliminar vértice',
    },
    title: 'Editor de Segmentación',
    resolution: '{width}x{height}',
    queue: {
      title: 'Cola de Segmentación',
      summary: '{{total}} tareas en total ({{running}} procesando, {{queued}} en cola)',
      noRunningTasks: 'No hay tareas en ejecución',
      noQueuedTasks: 'No hay tareas en cola',
      task: 'Tarea',
      statusRunning: 'Segmentación: {{count}} en ejecución{{queued}}',
      statusQueued: ', {{count}} en cola',
      statusOnlyQueued: 'Segmentación: {{count}} en cola',
      statusOnlyQueued_one: 'Segmentación: 1 en cola',
      statusOnlyQueued_other: 'Segmentación: {{count}} en cola',
      processing: 'Procesando',
      queued: 'En cola',
      statusProcessing: 'Segmentación: {{count}} procesando',
      statusReady: 'Listo',
      tasksTotal: '{{total}} tareas en total ({{running}} procesando, {{queued}} en cola)',
    },
    selectPolygonForEdit: 'Seleccione un polígono para editar',
    selectPolygonForSlice: 'Seleccione un polígono para dividir',
    selectPolygonForAddPoints: 'Seleccione un polígono para añadir puntos',
    clickToAddPoint: 'Haga clic para añadir un punto',
    clickToCompletePolygon: 'Haga clic en el primer punto para completar el polígono',
    clickToAddFirstSlicePoint: 'Haga clic para añadir el primer punto de corte',
    clickToAddSecondSlicePoint: 'Haga clic para añadir el segundo punto de corte',
    polygonCreationMode: 'Modo de Creación de Polígono',
    polygonEditMode: 'Modo de Edición de Polígono',
    polygonSliceMode: 'Modo de División de Polígono',
    polygonAddPointsMode: 'Modo de Añadir Puntos',
    viewMode: 'Modo de Visualización',
    totalPolygons: 'Total de Polígonos',
    totalVertices: 'Total de Vértices',
    vertices: 'Vértices',
    zoom: 'Zoom',
    mode: 'Modo',
    selected: 'Seleccionado',
    none: 'Ninguno',
    polygons: 'Polígonos',
    imageNotFound: 'Imagen no encontrada',
    returnToProject: 'Volver al proyecto',
    backToProject: 'Volver al proyecto',
    previousImage: 'Imagen anterior',
    nextImage: 'Imagen siguiente',
    toggleShortcuts: 'Mostrar atajos',
    modes: {
      view: 'Modo de Visualización',
      edit: 'Modo de Edición',
      create: 'Modo de Creación',
      slice: 'Modo de División',
      addPoints: 'Modo de Añadir Puntos',
      deletePolygon: 'Modo de Eliminar Polígono',
      createPolygon: 'Modo de Crear Polígono',
      editVertices: 'Modo de Editar Vértices',
      editMode: 'Modo de Edición',
      slicingMode: 'Modo de División',
      pointAddingMode: 'Modo de Añadir Puntos',
    },
    status: {
      processing: 'Procesando',
      queued: 'En cola',
      completed: 'Completado',
      failed: 'Fallido',
      pending: 'Pendiente',
      withoutSegmentation: 'Sin Segmentación',
    },
    autoSave: {
      enabled: 'Guardado automático: Activado',
      disabled: 'Guardado automático: Desactivado',
      idle: 'Guardado automático: Inactivo',
      pending: 'Pendiente...',
      saving: 'Guardando...',
      success: 'Guardado',
      error: 'Error',
    },
    loading: 'Cargando segmentación...',
    polygon: 'Polígono',
    unsavedChanges: 'Cambios sin guardar',
    noData: 'No hay datos de segmentación disponibles',
    noPolygons: 'No se encontraron polígonos',
    regions: 'Segmentación',
    position: 'Posición',
    polygonDeleted: 'Polígono eliminado exitosamente',
    saveSuccess: 'Segmentación guardada exitosamente',
    resegmentSuccess: 'Resegmentación iniciada exitosamente',
    resegmentComplete: 'Resegmentación completada exitosamente',
    resegmentError: 'Fallo al resegmentar la imagen',
    resegmentButton: 'Resegmentar',
    completedSegmentation: 'Completado',
    resegmentButtonTooltip: 'Resegmentar con Red Neuronal',
    processingImage: 'Procesando imagen...',
    helpTips: {
      title: 'Consejos:',
      edit: {
        createPoint: 'Haga clic para crear un nuevo punto',
        shiftPoints: 'Mantenga presionado Shift para crear automáticamente una secuencia de puntos',
        closePolygon: 'Cierre el polígono haciendo clic en el primer punto',
      },
      slice: {
        start: 'Haga clic para iniciar el corte',
        finish: 'Haga clic de nuevo para finalizar el corte',
        cancel: 'Esc para cancelar el corte',
      },
      addPoint: {
        hover: 'Pase el cursor sobre la línea del polígono',
        click: 'Haga clic para añadir punto al polígono seleccionado',
        exit: 'Esc para salir del modo añadir',
      },
      view: {
        pan: 'Desplazar: Haga clic y arrastre',
        selectPolygon: 'Seleccionar: Haga clic en el polígono',
        zoom: 'Zoom: Rueda del ratón',
      },
    },
    imageNotFoundDescription: 'La imagen solicitada no pudo ser encontrada',
    invalidImageDimensions: 'Dimensiones de imagen inválidas',
    noDataToSave: 'No hay cambios para guardar',
    polygonDuplicated: 'Polígono duplicado',
    polygonNotFound: 'Polígono no encontrado',
    polygonSimplified: 'Polígono simplificado',
    polygonSimplifyFailed: 'Fallo al simplificar el polígono',
    polygonSliced: 'Polígono dividido exitosamente',
    resegment: {
      error: {
        exception: 'Error de resegmentación: {{error}}',
        failed: 'Resegmentación fallida',
        missingData: 'Faltan datos requeridos para la resegmentación',
      },
      success: 'Resegmentación completada exitosamente',
    },
    resegmentMultipleError: 'Error al resegmentar múltiples imágenes',
    resegmentMultipleSuccess: 'Múltiples imágenes resegmentadas exitosamente',
    resegmenting: 'Resegmentando...',
    resegmentingMultiple: 'Resegmentando múltiples imágenes...',
    saveError: 'Error al guardar la segmentación',
    segmentationLoading: 'Cargando segmentación...',
    segmentationPolygon: 'Polígono de segmentación',
    selectPolygonFirst: 'Por favor seleccione un polígono primero',
    sliceFailed: 'Fallo al dividir el polígono',
    undoRestored: 'Acción deshecha',
    undoWhileDraggingError: 'No se puede deshacer mientras se arrastra',
    vertexDeleteFailed: 'Fallo al eliminar vértice',
    vertexDeleted: 'Vértice eliminado',
    vertexDuplicateFailed: 'Fallo al duplicar vértice',
    vertexDuplicated: 'Vértice duplicado',
  },
  // Error messages
  errors: {
    somethingWentWrong: 'Algo salió mal',
    componentError: 'Ocurrió un error en este componente',
    errorDetails: 'Detalles del Error',
    tryAgain: 'Intentar de nuevo',
    reloadPage: 'Recargar Página',
    goBack: 'Volver atrás',
    notFound: 'Página no encontrada',
    pageNotFoundMessage: 'La página solicitada no pudo ser encontrada',
    returnToHome: 'Volver al Inicio',
    unauthorized: 'Acceso no autorizado',
    forbidden: 'Acceso prohibido',
    serverError: 'Error del servidor',
    networkError: 'Error de red',
    timeoutError: 'Tiempo de espera agotado',
    validationError: 'Error de validación',
    unknownError: 'Error desconocido',
    goHome: 'Ir a la página de inicio',
    fetchSegmentationFailed: 'Fallo al obtener la segmentación',
    fetchImageFailed: 'Fallo al obtener la imagen',
    saveSegmentationFailed: 'Fallo al guardar la segmentación',
    missingPermissions: 'Permisos insuficientes',
    invalidInput: 'Entrada inválida',
    resourceNotFound: 'Recurso no encontrado',
  },
  project: {
    detail: {
      noImagesSelected: 'No hay imágenes seleccionadas',
      triggeringResegmentation: 'Iniciando resegmentación para {{count}} imágenes...',
      deleteConfirmation: '¿Está seguro de que desea eliminar {{count}} imágenes? Esta acción no se puede deshacer.',
      deletingImages: 'Eliminando {{count}} imágenes...',
      deleteSuccess: '{{count}} imágenes eliminadas exitosamente',
      deleteFailed: 'Fallo al eliminar {{count}} imágenes',
      preparingExport: 'Preparando exportación de {{count}} imágenes...',
    },
    segmentation: {
      processingInBatches: 'Iniciando segmentación para {{count}} imágenes en {{batches}} lotes...',
      batchQueued: 'Lote {{current}}/{{total}} encolado exitosamente',
      batchQueuedFallback: 'Lote {{current}}/{{total}} encolado exitosamente (punto de acceso alternativo)',
      batchError: 'Error procesando lote {{current}}/{{total}}',
      partialSuccess: 'Segmentación: {{success}} imágenes encoladas exitosamente, {{failed}} fallidas',
      allSuccess: 'Segmentación: Todas las {{count}} imágenes encoladas exitosamente',
      allFailed: 'Segmentación: Todas las {{count}} imágenes fallaron',
      startedImages: 'Segmentación iniciada para {{count}} imágenes',
      queuedLocallyWarning: 'Segmentación encolada localmente para {{count}} imágenes. Conexión al servidor falló.',
    },
    loading: 'Cargando proyecto...',
    notFound: 'Proyecto no encontrado',
    error: 'Error al cargar el proyecto',
    empty: 'Este proyecto está vacío',
    noImagesText: 'No se encontraron imágenes en este proyecto',
    addImages: 'Añadir imágenes para comenzar',
    noImages: {
      title: 'Sin Imágenes Aún',
      description: 'Este proyecto aún no tiene imágenes. Sube imágenes para comenzar.',
      uploadButton: 'Subir Imágenes',
    },
    deleteProject: 'Eliminar Proyecto',
    deleteConfirmation:
      '¿Está seguro de que desea eliminar el proyecto "{{projectName}}"? Esta acción no se puede deshacer.',
    resegmentImage: 'Resegmentar imagen',
    deleteImage: 'Eliminar imagen',
  },
  projectsPage: {
    title: 'Proyectos',
    description: 'Gestionar proyectos de investigación',
    createNew: 'Crear nuevo proyecto',
    createProject: 'Crear proyecto',
    createProjectDesc: 'Iniciar un nuevo proyecto de investigación',
    projectName: 'Nombre del proyecto',
    projectDescription: 'Descripción del proyecto',
    projectNamePlaceholder: 'Ingrese el nombre del proyecto',
    projectDescriptionPlaceholder: 'Ingrese la descripción del proyecto',
    projectCreated: 'Proyecto creado exitosamente',
    projectCreationFailed: 'Fallo al crear el proyecto',
    projectDeleted: 'Proyecto eliminado exitosamente',
    projectDeletionFailed: 'Fallo al eliminar el proyecto',
    confirmDelete: '¿Está seguro de que desea eliminar este proyecto?',
    confirmDeleteDescription:
      'Esta acción no se puede deshacer. Todos los datos asociados con este proyecto se eliminarán permanentemente.',
    deleteProject: 'Eliminar proyecto',
    editProject: 'Editar proyecto',
    viewProject: 'Ver proyecto',
    projectUpdated: 'Proyecto actualizado exitosamente',
    projectUpdateFailed: 'Fallo al actualizar el proyecto',
    noProjects: 'No se encontraron proyectos',
    createFirstProject: 'Cree su primer proyecto para comenzar',
    searchProjects: 'Buscar proyectos...',
    filterProjects: 'Filtrar proyectos',
    sortProjects: 'Ordenar proyectos',
    projectNameRequired: 'El nombre del proyecto es requerido',
    loginRequired: 'Debe iniciar sesión para crear un proyecto',
    createdAt: 'Creado',
    updatedAt: 'Última actualización',
    imageCount: 'Imágenes',
    status: 'Estado',
    actions: 'Acciones',
    loading: 'Cargando proyectos...',
    error: 'Error al cargar proyectos',
    retry: 'Intentar de nuevo',
    copySegmentations: 'Copiar resultados de segmentación',
    resetImageStatus: 'Restablecer estado de procesamiento de imagen',
    newProjectTitle: 'Título del nuevo proyecto',
    itemsProcessed: 'elementos procesados',
    items: 'elementos',
    unknownProject: 'Proyecto desconocido',
    activeTasks: 'Activo',
    allTasks: 'Todo',
    deleteProjectDescription: 'Esta acción eliminará permanentemente el proyecto y todos los datos asociados.',
    deleteWarning:
      'Esta acción no se puede deshacer. Todos los datos asociados con este proyecto se eliminarán permanentemente.',
    untitledProject: 'Proyecto sin título',
    typeToConfirm: 'Escriba "eliminar" para confirmar',
    deleteConfirm: '¿Está seguro de que desea eliminar este proyecto?',
    exportProject: 'Exportar proyecto',
    archived: 'Archivado',
    completed: 'Completado',
    draft: 'Borrador',
    active: 'Activo',
    createDate: 'Creado',
    lastModified: 'Última modificación',
    projectDescPlaceholder: 'Ingrese la descripción del proyecto',
    creatingProject: 'Creando proyecto...',
    noImages: {
      title: 'Sin imágenes aún',
      description: 'Este proyecto aún no tiene imágenes. Sube imágenes para iniciar la segmentación.',
      uploadButton: 'Subir Imágenes',
    },
  },
  common: {
    appName: 'Segmentación de Esferoides',
    appNameShort: 'SpheroSeg',
    loading: 'Cargando...',
    loadingAccount: 'Cargando su cuenta...',
    loadingApplication: 'Cargando aplicación...',
    selectAll: 'Seleccionar todo',
    deselectAll: 'Deseleccionar todo',
    save: 'Guardar',
    cancel: 'Cancelar',
    delete: 'Eliminar',
    edit: 'Editar',
    create: 'Crear',
    search: 'Buscar',
    error: 'Error',
    success: 'Éxito',
    reset: 'Restablecer',
    clear: 'Limpiar',
    close: 'Cerrar',
    back: 'Atrás',
    signIn: 'Iniciar Sesión',
    signUp: 'Registrarse',
    signOut: 'Cerrar Sesión',
    signingIn: 'Iniciando Sesión...',
    settings: 'Configuración',
    profile: 'Perfil',
    dashboard: 'Panel de Control',
    project: 'Proyecto',
    projects: 'Proyectos',
    newProject: 'Nuevo Proyecto',
    upload: 'Subir',
    download: 'Descargar',
    removeAll: 'Eliminar Todo',
    uploadImages: 'Subir Imágenes',
    recentAnalyses: 'Análisis Recientes',
    noProjects: 'No se encontraron proyectos',
    noImages: 'No se encontraron imágenes',
    createYourFirst: 'Cree su primer proyecto para comenzar',
    tryAgain: 'Intentar de Nuevo',
    email: 'Correo Electrónico',
    password: 'Contraseña',
    confirmPassword: 'Confirmar Contraseña',
    firstName: 'Nombre',
    lastName: 'Apellido',
    username: 'Nombre de Usuario',
    name: 'Nombre',
    description: 'Descripción',
    date: 'Fecha',
    status: 'Estado',
    image: 'Imagen',
    projectName: 'Nombre del Proyecto',
    projectDescription: 'Descripción del Proyecto',
    language: 'Idioma',
    theme: 'Tema',
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
    welcome: 'Bienvenido a la Plataforma de Segmentación de Esferoides',
    account: 'Cuenta',
    passwordConfirm: 'Confirmar Contraseña',
    manageAccount: 'Gestionar Cuenta',
    changePassword: 'Cambiar Contraseña',
    deleteAccount: 'Eliminar Cuenta',
    requestAccess: 'Solicitar Acceso',
    accessRequest: 'Solicitud de Acceso',
    createAccount: 'Crear Cuenta',
    signInToAccount: 'Iniciar Sesión en la Cuenta',
    termsOfService: 'Términos de Servicio',
    privacyPolicy: 'Política de Privacidad',
    termsOfServiceLink: 'Términos de Servicio',
    privacyPolicyLink: 'Política de Privacidad',
    optional: 'Opcional',
    saveChanges: 'Guardar Cambios',
    saving: 'Guardando',
    notSpecified: 'No Especificado',
    enable: 'Habilitar',
    disable: 'Deshabilitar',
    backToHome: 'Volver al Inicio',
    and: 'y',
    lastChange: 'Último Cambio',
    sort: 'Ordenar',
    emailPlaceholder: 'Ingrese su correo electrónico',
    passwordPlaceholder: 'Ingrese su contraseña',
    export: 'Exportar',
    selectImages: 'Seleccionar Imágenes',
    noImagesDescription: 'Sube imágenes para comenzar con tu proyecto',
    yes: 'Sí',
    no: 'No',
    images: 'Imágenes',
    files: 'Archivos',
    validationFailed: 'Validación fallida',
    cropAvatar: 'Recortar Imagen de Perfil',
    profileTitle: 'Perfil',
    profileDescription: 'Actualice su información de perfil visible para otros usuarios',
    profileUsername: 'Nombre de usuario',
    profileUsernamePlaceholder: 'Ingrese su nombre de usuario',
    profileFullName: 'Nombre completo',
    profileFullNamePlaceholder: 'Ingrese su nombre completo',
    profileTitlePlaceholder: 'ej. Investigador, Profesor',
    profileOrganization: 'Organización',
    profileOrganizationPlaceholder: 'Ingrese su organización o institución',
    profileBio: 'Biografía',
    profileBioPlaceholder: 'Escriba una breve biografía sobre usted',
    profileBioDescription: 'Breve descripción de sus intereses de investigación y experiencia',
    profileLocation: 'Ubicación',
    profileLocationPlaceholder: 'ej. Praga, República Checa',
    profileSaveButton: 'Guardar Perfil',
    actions: 'Acciones',
    view: 'Ver',
    share: 'Compartir',
    projectNamePlaceholder: 'Ingrese el nombre del proyecto',
    projectDescPlaceholder: 'Ingrese la descripción del proyecto',
    creatingProject: 'Creando proyecto...',
    createSuccess: 'Proyecto creado exitosamente',
    unauthorized: 'No está autorizado para realizar esta acción',
    forbidden: 'Acceso prohibido',
    maxFileSize: 'Tamaño máximo de archivo: {{size}}MB',
    accepted: 'Aceptado',
    processing: 'Procesando...',
    uploading: 'Subiendo...',
    uploadComplete: 'Carga completada',
    uploadFailed: 'Carga fallida',
    deletePolygon: 'Eliminar polígono',
    pleaseLogin: 'Por favor inicie sesión para continuar',
    retry: 'Reintentar',
    segmentation: 'Segmentación',
    copiedToClipboard: '¡Copiado al portapapeles!',
    failedToCopy: 'Fallo al copiar al portapapeles',
    confirm: 'Confirmar',
    editor: {
      error: 'Error',
      success: 'Éxito',
      edit: 'Editar',
      create: 'Crear',
    },
  },
  auth: {
    signIn: 'Iniciar Sesión',
    signUp: 'Registrarse',
    signOut: 'Cerrar Sesión',
    signingIn: 'Iniciando Sesión...',
    email: 'Correo electrónico',
    password: 'Contraseña',
    forgotPassword: '¿Olvidó su Contraseña?',
    resetPassword: 'Restablecer Contraseña',
    dontHaveAccount: '¿No tiene una cuenta?',
    alreadyHaveAccount: '¿Ya tiene una cuenta?',
    createAccount: 'Crear Cuenta',
    signInWithGoogle: 'Iniciar Sesión con Google',
    signInWithGithub: 'Iniciar Sesión con GitHub',
    or: 'o',
    signInTitle: 'Iniciar Sesión',
    signInDescription: 'Inicie sesión en su cuenta',
    noAccount: '¿No tiene una cuenta?',
    emailAddressLabel: 'Dirección de correo electrónico',
    passwordLabel: 'Contraseña',
    currentPasswordLabel: 'Contraseña actual',
    newPasswordLabel: 'Nueva contraseña',
    confirmPasswordLabel: 'Confirmar contraseña',
    rememberMe: 'Recordarme',
    emailRequired: 'El correo electrónico es requerido',
    passwordRequired: 'La contraseña es requerida',
    alreadyLoggedInTitle: 'Ya ha iniciado sesión',
    alreadyLoggedInMessage: 'Ya ha iniciado sesión en su cuenta',
    goToDashboardLink: 'Ir al Panel de Control',
    invalidEmail: 'Dirección de correo electrónico inválida',
    passwordTooShort: 'La contraseña debe tener al menos 6 caracteres',
    passwordsDontMatch: 'Las contraseñas no coinciden',
    invalidCredentials: 'Correo electrónico o contraseña inválidos',
    accountCreated: 'Cuenta creada exitosamente',
    resetLinkSent: 'Enlace de restablecimiento de contraseña enviado a su correo',
    resetSuccess: 'Contraseña restablecida exitosamente',
    signInSuccess: 'Sesión iniciada exitosamente',
    signOutSuccess: 'Sesión cerrada exitosamente',
    sessionExpired: 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
    unauthorized: 'No está autorizado para acceder a este recurso',
    verifyEmail: 'Por favor verifique su dirección de correo electrónico',
    verificationLinkSent: 'Enlace de verificación enviado a su correo',
    verificationSuccess: 'Correo electrónico verificado exitosamente',
    resendVerification: 'Reenviar correo de verificación',
    requestAccess: 'Solicitar Acceso',
    termsAndPrivacy: 'Al registrarse, acepta nuestros Términos de Servicio y Política de Privacidad.',
    forgotPasswordLink: '¿Olvidó su contraseña?',
    passwordChanged: 'Contraseña cambiada exitosamente',
    currentPasswordIncorrect: 'La contraseña actual es incorrecta',
    registerTitle: 'Crear Cuenta',
    registerDescription: 'Regístrese para una nueva cuenta',
    registerSuccess: '¡Registro exitoso! Ahora puede iniciar sesión.',
    emailPlaceholder: 'Ingrese su correo electrónico',
    passwordPlaceholder: 'Ingrese su contraseña',
    firstNamePlaceholder: 'ej. Juan',
    lastNamePlaceholder: 'ej. García',
    passwordConfirmPlaceholder: 'Confirme su contraseña',
    signUpTitle: 'Crear Cuenta',
    signUpDescription: 'Regístrese para una nueva cuenta',
    enterInfoCreateAccount: 'Ingrese su información para crear una cuenta',
    creatingAccount: 'Creando Cuenta...',
    emailAlreadyExists: 'Este correo ya está registrado. Por favor use un correo diferente o inicie sesión.',
    emailHasPendingRequest: 'Este correo ya tiene una solicitud de acceso pendiente. Por favor espere la aprobación.',
    signUpSuccessEmail: '¡Registro exitoso! Por favor revise su correo o espere la aprobación del administrador.',
    signUpFailed: 'Registro fallido. Por favor intente de nuevo.',
    alreadyHaveAccess: '¿Ya tiene acceso?',
    forgotPasswordTitle: 'Restablecer Su Contraseña',
    checkYourEmail: 'Revise su correo para una nueva contraseña',
    enterEmailForReset: 'Ingrese su dirección de correo y le enviaremos una nueva contraseña',
    passwordResetLinkSent: 'Si existe una cuenta para este correo, se ha enviado una nueva contraseña',
    passwordResetFailed: 'Fallo al enviar nueva contraseña. Por favor intente de nuevo.',
    enterEmail: 'Por favor ingrese su dirección de correo electrónico',
    sendingResetLink: 'Enviando nueva contraseña...',
    sendResetLink: 'Enviar Nueva Contraseña',
    backToSignIn: 'Volver a Iniciar Sesión',
    signUpSuccess: '¡Registro exitoso!',
    accountLocked: 'Su cuenta ha sido bloqueada. Por favor contacte al soporte.',
    fillAllFields: 'Por favor complete todos los campos requeridos',
    serverError: 'Error del servidor. Por favor intente más tarde.',
    signInError: 'Error al iniciar sesión',
    signInFailed: 'Inicio de sesión fallido. Por favor verifique sus credenciales.',
  },
  requestAccess: {
    and: 'y',
    title: 'Solicitar Acceso a la Plataforma de Segmentación de Esferoides',
    description:
      'Complete el siguiente formulario para solicitar acceso a nuestra plataforma. Revisaremos su solicitud y nos pondremos en contacto pronto.',
    emailLabel: 'Su Dirección de Correo Electrónico',
    nameLabel: 'Su Nombre',
    institutionLabel: 'Institución/Empresa',
    reasonLabel: 'Razón para el Acceso',
    submitRequest: 'Enviar Solicitud',
    requestReceived: 'Solicitud Recibida',
    thankYou: 'Gracias por su interés',
    weWillContact: 'Revisaremos su solicitud y nos pondremos en contacto pronto',
    submitSuccess: '¡Solicitud enviada exitosamente!',
    emailPlaceholder: 'Ingrese su dirección de correo electrónico',
    namePlaceholder: 'Ingrese su nombre completo',
    institutionPlaceholder: 'Ingrese el nombre de su institución o empresa',
    reasonPlaceholder: 'Por favor describa cómo planea usar la plataforma',
    fillRequired: 'Por favor complete todos los campos requeridos',
    submittingRequest: 'Enviando Solicitud...',
    submitError: 'Fallo al enviar la solicitud',
    alreadyPending: 'Ya existe una solicitud de acceso pendiente para este correo',
    agreeToTerms: 'Al enviar esta solicitud, acepta nuestros',
  },
  requestAccessForm: {
    title: 'Solicitar Acceso a la Plataforma de Segmentación de Esferoides',
    description:
      'Complete el siguiente formulario para solicitar acceso a nuestra plataforma. Revisaremos su solicitud y nos pondremos en contacto pronto.',
    emailLabel: 'Su Dirección de Correo Electrónico',
    nameLabel: 'Su Nombre',
    institutionLabel: 'Institución/Empresa',
    reasonLabel: 'Razón para el Acceso',
    submitButton: 'Enviar Solicitud',
    signInPrompt: '¿Ya tiene una cuenta?',
    signInLink: 'Iniciar Sesión',
    thankYouTitle: 'Gracias por su interés',
    weWillContact: 'Revisaremos su solicitud y nos pondremos en contacto pronto',
    agreeToTerms: 'Al enviar esta solicitud, acepta nuestros',
    and: 'y',
  },
  documentation: {
    tag: 'Guía del Usuario',
    title: 'Documentación de SpheroSeg',
    subtitle: 'Aprenda a usar la Plataforma de Segmentación de Esferoides efectivamente.',
    sidebar: {
      title: 'Secciones',
      introduction: 'Introducción',
      gettingStarted: 'Comenzando',
      uploadingImages: 'Subiendo Imágenes',
      segmentationProcess: 'Proceso de Segmentación',
      apiReference: 'Referencia API',
    },
    introduction: {
      title: 'Introducción',
      imageAlt: 'Ilustración del flujo de trabajo de análisis de esferoides',
      whatIs: {
        title: '¿Qué es SpheroSeg?',
        paragraph1:
          'SpheroSeg es una plataforma de vanguardia diseñada para la segmentación y análisis de esferoides celulares en imágenes microscópicas. Nuestra herramienta proporciona a los investigadores capacidades precisas de detección y análisis.',
        paragraph2:
          'Utiliza algoritmos avanzados de IA basados en aprendizaje profundo para identificar y segmentar automáticamente esferoides en sus imágenes con alta precisión y consistencia.',
        paragraph3:
          'Esta documentación le guiará a través de todos los aspectos del uso de la plataforma, desde comenzar hasta características avanzadas e integración API.',
      },
    },
    gettingStarted: {
      title: 'Comenzando',
      accountCreation: {
        title: 'Creación de Cuenta',
        paragraph1:
          'Para usar SpheroSeg, necesita crear una cuenta. Esto nos permite almacenar de forma segura sus proyectos e imágenes.',
        step1Prefix: 'Visite la',
        step1Link: 'página de registro',
        step2: 'Ingrese su dirección de correo institucional y cree una contraseña',
        step3: 'Complete su perfil con su nombre e institución',
        step4: 'Verifique su dirección de correo a través del enlace enviado a su bandeja de entrada',
      },
      creatingProject: {
        title: 'Creando Su Primer Proyecto',
        paragraph1:
          'Los proyectos le ayudan a organizar su trabajo. Cada proyecto puede contener múltiples imágenes y sus correspondientes resultados de segmentación.',
        step1: 'En su panel de control, haga clic en "Nuevo Proyecto"',
        step2: 'Ingrese un nombre y descripción del proyecto',
        step3: 'Seleccione el tipo de proyecto (predeterminado: Análisis de Esferoides)',
        step4: 'Haga clic en "Crear Proyecto" para continuar',
      },
    },
    uploadingImages: {
      title: 'Subiendo Imágenes',
      paragraph1:
        'SpheroSeg soporta varios formatos de imagen comúnmente usados en microscopía, incluyendo TIFF, PNG y JPEG.',
      methods: {
        title: 'Métodos de Carga',
        paragraph1: 'Hay varias formas de subir imágenes:',
        step1: 'Arrastre y suelte archivos directamente en el área de carga',
        step2: 'Haga clic en el área de carga para navegar y seleccionar archivos de su computadora',
        step3: 'Carga por lotes de múltiples imágenes a la vez',
      },
      note: {
        prefix: 'Nota:',
        text: 'Para resultados óptimos, asegúrese de que sus imágenes microscópicas tengan buen contraste entre el esferoide y el fondo.',
      },
    },
    segmentationProcess: {
      title: 'Proceso de Segmentación',
      paragraph1:
        'El proceso de segmentación identifica los límites de los esferoides en sus imágenes, permitiendo un análisis preciso de su morfología.',
      automatic: {
        title: 'Segmentación Automática',
        paragraph1:
          'Nuestra segmentación automática potenciada por IA puede detectar límites de esferoides con alta precisión:',
        step1: 'Seleccione una imagen de su proyecto',
        step2: 'Haga clic en "Auto-Segmentar" para iniciar el proceso',
        step3: 'El sistema procesará la imagen y mostrará los límites detectados',
        step4: 'Revise los resultados en el editor de segmentación',
      },
      manual: {
        title: 'Ajustes Manuales',
        paragraph1:
          'A veces la segmentación automática puede requerir refinamiento. Nuestro editor proporciona herramientas para:',
        step1: 'Añadir o eliminar vértices a lo largo del límite',
        step2: 'Ajustar posiciones de vértices para límites más precisos',
        step3: 'Dividir o fusionar regiones',
        step4: 'Añadir o eliminar huecos dentro de esferoides',
      },
    },
    apiReference: {
      title: 'Referencia API',
      paragraph1:
        'SpheroSeg ofrece una API RESTful para acceso programático a las características de la plataforma. Esto es ideal para integración con sus flujos de trabajo existentes o procesamiento por lotes.',
      endpoint1Desc: 'Recupera una lista de todos sus proyectos',
      endpoint2Desc: 'Recupera todas las imágenes dentro de un proyecto específico',
      endpoint3Desc: 'Inicia la segmentación para una imagen específica',
      contactPrefix: 'Para documentación completa de API y detalles de autenticación, por favor contáctenos en',
    },
    backToHome: 'Volver al Inicio',
    backToTop: 'Volver Arriba',
  },
  hero: {
    platformTag: 'Plataforma Avanzada de Segmentación de Esferoides',
    title: 'Análisis Celular Potenciado por IA para Investigación Biomédica',
    subtitle:
      'Eleve su análisis de imágenes celulares microscópicas con nuestra plataforma de vanguardia para segmentación de esferoides. Diseñada para investigadores que buscan precisión y eficiencia.',
    getStartedButton: 'Comenzar',
    learnMoreButton: 'Más Información',
    imageAlt1: 'Imagen de microscopía de esferoide',
    imageAlt2: 'Imagen de microscopía de esferoide con análisis',
    welcomeTitle: 'Bienvenido a SpheroSeg',
    welcomeSubtitle: 'Plataforma avanzada para segmentación y análisis de esferoides celulares',
    welcomeDescription:
      'Nuestra plataforma combina algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para la detección precisa y análisis de esferoides celulares en imágenes microscópicas.',
    featuresTitle: 'Características Poderosas',
    featuresSubtitle: 'Herramientas avanzadas para investigación biomédica',
    featureAiSegmentation: 'Segmentación Avanzada',
    featureAiSegmentationDesc:
      'Detección precisa de esferoides con análisis de límites para mediciones celulares exactas.',
    featureEditing: 'Análisis Potenciado por IA',
    featureEditingDesc:
      'Aproveche algoritmos de aprendizaje profundo para detección automatizada y clasificación celular.',
    featureAnalytics: 'Carga Fácil',
    featureAnalyticsDesc: 'Arrastre y suelte sus imágenes de microscopía para procesamiento y análisis inmediato.',
    featureExport: 'Perspectivas Estadísticas',
    featureExportDesc: 'Métricas integrales y visualizaciones para extraer patrones de datos significativos.',
    ctaTitle: '¿Listo para transformar su flujo de trabajo de análisis celular?',
    ctaSubtitle: 'Únase a investigadores líderes que ya usan nuestra plataforma para acelerar sus descubrimientos.',
    ctaButton: 'Crear Cuenta',
  },
  navbar: {
    home: 'Inicio',
    features: 'Características',
    documentation: 'Documentación',
    terms: 'Términos',
    privacy: 'Privacidad',
    login: 'Iniciar Sesión',
    requestAccess: 'Solicitar Acceso',
    openMobileMenu: 'Abrir menú móvil',
    closeMobileMenu: 'Cerrar menú móvil',
  },
  navigation: {
    home: 'Inicio',
    projects: 'Proyectos',
    settings: 'Configuración',
    profile: 'Perfil',
    dashboard: 'Panel de Control',
    back: 'Atrás',
  },
  dashboard: {
    manageProjects: 'Gestione y organice sus proyectos de investigación',
    viewMode: {
      grid: 'Vista de Cuadrícula',
      list: 'Vista de Lista',
    },
    sort: {
      name: 'Nombre',
      updatedAt: 'Última Actualización',
      segmentationStatus: 'Estado',
    },
    search: 'Buscar proyectos...',
    searchImagesPlaceholder: 'Buscar imágenes...',
    noProjects: 'No se encontraron proyectos',
    noImagesDescription: 'No hay imágenes que coincidan con sus criterios de búsqueda',
    createFirst: 'Cree su primer proyecto para comenzar',
    createNew: 'Crear Nuevo Proyecto',
    lastChange: 'Último Cambio',
    statsOverview: 'Resumen de Estadísticas',
    totalProjects: 'Total de Proyectos',
    activeProjects: 'Proyectos Activos',
    totalImages: 'Total de Imágenes',
    totalAnalyses: 'Total de Análisis',
    lastUpdated: 'Última Actualización',
    noProjectsDescription: 'Aún no ha creado ningún proyecto. Cree su primer proyecto para comenzar.',
    searchProjectsPlaceholder: 'Buscar proyectos por nombre...',
    sortBy: 'Ordenar por',
    name: 'Nombre',
    completed: 'Completado',
    processing: 'Procesando',
    pending: 'Pendiente',
    failed: 'Fallido',
    selectImagesButton: 'Seleccionar Imágenes',
  },
  projects: {
    title: 'Proyectos',
    description: 'Gestione sus proyectos de investigación',
    createNew: 'Crear Nuevo Proyecto',
    createProject: 'Crear Proyecto',
    createProjectDesc: 'Cree un nuevo proyecto para comenzar a trabajar con imágenes y segmentación.',
    projectName: 'Nombre del Proyecto',
    projectDescription: 'Descripción del Proyecto',
    projectNamePlaceholder: 'Ingrese el nombre del proyecto',
    projectDescriptionPlaceholder: 'Ingrese la descripción del proyecto',
    projectCreated: 'Proyecto creado exitosamente',
    projectCreationFailed: 'Fallo al crear el proyecto',
    projectDeleted: 'Proyecto eliminado exitosamente',
    deleteSuccess: 'Proyecto eliminado exitosamente',
    deleteFailed: 'Fallo al eliminar el proyecto',
    deleting: 'Eliminando proyecto...',
    notFound: 'Proyecto no encontrado. Puede haber sido eliminado.',
    missingId: 'No se puede eliminar el proyecto: falta el identificador del proyecto',
    projectDeletionFailed: 'Fallo al eliminar el proyecto',
    confirmDelete: '¿Está seguro de que desea eliminar este proyecto?',
    confirmDeleteDescription:
      'Esta acción no se puede deshacer. Todos los datos asociados con este proyecto se eliminarán permanentemente.',
    delete: 'Eliminar',
    deleteProject: 'Eliminar Proyecto',
    deleteProjectDescription:
      'Esta acción no se puede deshacer. Esto eliminará permanentemente el proyecto y todos los datos asociados.',
    deleteWarning: 'Está a punto de eliminar el siguiente proyecto:',
    typeToConfirm: 'Escriba el nombre del proyecto para confirmar',
    confirmDeleteError: 'Por favor escriba el nombre del proyecto exactamente para confirmar',
    editProject: 'Editar Proyecto',
    viewProject: 'Ver Proyecto',
    projectUpdated: 'Proyecto actualizado exitosamente',
    projectUpdateFailed: 'Fallo al actualizar el proyecto',
    noProjects: 'No se encontraron proyectos',
    createFirstProject: 'Cree su primer proyecto para comenzar',
    searchProjects: 'Buscar proyectos...',
    filterProjects: 'Filtrar proyectos',
    sortProjects: 'Ordenar proyectos',
    projectNameRequired: 'El nombre del proyecto es requerido',
    loginRequired: 'Debe iniciar sesión para crear un proyecto',
    createdAt: 'Creado',
    updatedAt: 'Última actualización',
    imageCount: 'Imágenes',
    status: 'Estado',
    actions: 'Acciones',
    loading: 'Cargando proyectos...',
    error: 'Error al cargar proyectos',
    retry: 'Reintentar',
    copySegmentations: 'Copiar resultados de segmentación',
    resetImageStatus: 'Restablecer estado de procesamiento de imagen',
    newProjectTitle: 'Título del Nuevo Proyecto',
    itemsProcessed: 'elementos procesados',
    items: 'elementos',
    unknownProject: 'Proyecto Desconocido',
    activeTasks: 'Activo',
    allTasks: 'Todo',
    untitledProject: 'Proyecto Sin Título',
    exportProject: 'Exportar Proyecto',
    share: 'Compartir',
    export: 'Exportar',
    archived: 'Archivado',
    completed: 'Completado',
    draft: 'Borrador',
    active: 'Activo',
  },
  projectToolbar: {
    selectImages: 'Seleccionar Imágenes',
    cancelSelection: 'Cancelar Selección',
    export: 'Exportar',
    uploadImages: 'Subir Imágenes',
  },
  statsOverview: {
    title: 'Resumen del Panel',
    totalProjects: 'Total de Proyectos',
    totalImages: 'Total de Imágenes',
    completedSegmentations: 'Segmentaciones Completadas',
    storageUsed: 'Almacenamiento Usado',
    recentActivity: 'Actividad Reciente',
    moreStats: 'Ver Estadísticas Detalladas',
    completion: 'tasa de completado',
    vsLastMonth: 'vs. mes pasado',
    thisMonth: 'Este Mes',
    lastMonth: 'Mes Pasado',
    projectsCreated: 'Proyectos Creados',
    imagesUploaded: 'Imágenes Subidas',
    fetchError: 'Fallo al cargar estadísticas',
    storageLimit: 'Límite de Almacenamiento',
    activityTitle: 'Actividad Reciente',
    noActivity: 'No hay actividad reciente',
    hide: 'Ocultar',
    activityTypes: {
      project_created: 'Proyecto creado',
      image_uploaded: 'Imagen subida',
      segmentation_completed: 'Segmentación completada',
    },
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FNSPE CTU en Praga',
    description: 'Plataforma avanzada para segmentación y análisis de esferoides',
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FNSPE CTU en Praga',
    resourcesTitle: 'Recursos',
    documentationLink: 'Documentación',
    featuresLink: 'Características',
    tutorialsLink: 'Tutoriales',
    researchLink: 'Investigación',
    legalTitle: 'Información Legal',
    termsLink: 'Términos de Servicio',
    privacyLink: 'Política de Privacidad',
    contactUsLink: 'Contáctenos',
    informationTitle: 'Información',
    contactTitle: 'Contacto',
    copyrightNotice: 'SpheroSeg. Todos los derechos reservados.',
    madeWith: 'Hecho con',
    by: 'por',
    requestAccessLink: 'Solicitar Acceso',
    githubRepository: 'Repositorio GitHub',
    contactEmail: 'Correo de contacto',
  },
  features: {
    tag: 'Características',
    title: 'Descubra las Capacidades de Nuestra Plataforma',
    subtitle: 'Herramientas avanzadas para investigación biomédica',
    cards: {
      segmentation: {
        title: 'Segmentación Avanzada',
        description: 'Detección precisa de esferoides con análisis de límites para mediciones celulares exactas',
      },
      aiAnalysis: {
        title: 'Análisis Potenciado por IA',
        description: 'Aproveche algoritmos de aprendizaje profundo para detección y clasificación celular automatizada',
      },
      uploads: {
        title: 'Carga Fácil',
        description: 'Arrastre y suelte sus imágenes de microscopía para procesamiento y análisis inmediato',
      },
      insights: {
        title: 'Perspectivas Estadísticas',
        description: 'Métricas integrales y visualizaciones para extraer patrones de datos significativos',
      },
      collaboration: {
        title: 'Colaboración en Equipo',
        description: 'Comparta proyectos y resultados con colegas para una investigación más eficiente',
      },
      pipeline: {
        title: 'Pipeline Automatizado',
        description: 'Optimice su flujo de trabajo con nuestras herramientas de procesamiento por lotes',
      },
    },
  },
  index: {
    about: {
      tag: 'Acerca de la Plataforma',
      title: '¿Qué es SpheroSeg?',
      imageAlt: 'Ejemplo de segmentación de esferoide',
      paragraph1:
        'SpheroSeg es una plataforma avanzada específicamente diseñada para la segmentación y análisis de esferoides celulares en imágenes microscópicas.',
      paragraph2:
        'Nuestra herramienta combina algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para proporcionar a los investigadores detección precisa de límites de esferoides y capacidades analíticas.',
      paragraph3:
        'La plataforma fue desarrollada por Michal Průšek de FNSPE CTU en Praga bajo la supervisión de Adam Novozámský de UTIA CAS, en colaboración con investigadores del Departamento de Bioquímica y Microbiología de UCT Praga.',
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: '¿Listo para transformar su investigación?',
      subtitle: 'Comience a usar SpheroSeg hoy y descubra nuevas posibilidades en el análisis de esferoides celulares',
      boxTitle: 'Cree una cuenta gratuita',
      boxText:
        'Obtenga acceso a todas las características de la plataforma y comience a analizar sus imágenes microscópicas',
      button: 'Crear Cuenta',
    },
  },
  tools: {
    zoomIn: 'Acercar',
    zoomOut: 'Alejar',
    resetView: 'Restablecer Vista',
    createPolygon: 'Crear Nuevo Polígono',
    exitPolygonCreation: 'Salir del Modo de Creación de Polígono',
    splitPolygon: 'Dividir Polígono en Dos',
    exitSlicingMode: 'Salir del Modo de División',
    addPoints: 'Añadir Puntos al Polígono',
    exitPointAddingMode: 'Salir del Modo de Añadir Puntos',
    undo: 'Deshacer',
    redo: 'Rehacer',
    save: 'Guardar',
    resegment: 'Resegmentar',
    title: 'Herramientas',
  },
  settings: {
    title: 'Configuración',
    pageTitle: 'Configuración',
    profile: 'Perfil',
    account: 'Cuenta',
    appearance: 'Apariencia',
    profileSettings: 'Configuración de Perfil',
    accountSettings: 'Configuración de Cuenta',
    securitySettings: 'Configuración de Seguridad',
    preferenceSettings: 'Configuración de Preferencias',
    selectLanguage: 'Seleccionar Idioma',
    selectTheme: 'Seleccionar Tema',
    updateProfile: 'Actualizar Perfil',
    changePassword: 'Cambiar Contraseña',
    deleteAccount: 'Eliminar Cuenta',
    savedChanges: 'Cambios guardados exitosamente',
    saveChanges: 'Guardar Cambios',
    profileUpdated: 'Perfil actualizado exitosamente',
    languageSettings: 'Configuración de Idioma',
    themeSettings: 'Configuración de Tema',
    privacySettings: 'Configuración de Privacidad',
    exportData: 'Exportar Datos',
    importData: 'Importar Datos',
    uploadAvatar: 'Subir Imagen de Perfil',
    removeAvatar: 'Eliminar Imagen de Perfil',
    twoFactorAuth: 'Autenticación de Dos Factores',
    emailNotifications: 'Notificaciones por Correo',
    pushNotifications: 'Notificaciones Push',
    weeklyDigest: 'Resumen Semanal',
    monthlyReport: 'Informe Mensual',
    displaySettings: 'Configuración de Pantalla',
    accessibilitySettings: 'Configuración de Accesibilidad',
    advancedSettings: 'Configuración Avanzada',
    useBrowserLanguage: 'Usar idioma del navegador',
    language: 'Idioma',
    theme: 'Tema',
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
    languageUpdated: 'Idioma actualizado exitosamente',
    themeUpdated: 'Tema actualizado exitosamente',
    toggleTheme: 'Cambiar tema',
    languageDescription: 'Elija su idioma preferido',
    themeDescription: 'Elija su tema preferido',
    profileLoadError: 'Fallo al cargar el perfil',
    appearanceDescription: 'Personalice la apariencia de la aplicación',
    personal: 'Información Personal',
    fullName: 'Nombre Completo',
    organization: 'Organización',
    department: 'Departamento',
    publicProfile: 'Perfil Público',
    makeProfileVisible: 'Hacer mi perfil visible para otros investigadores',
    passwordSettings: 'Configuración de Contraseña',
    currentPassword: 'Contraseña Actual',
    newPassword: 'Nueva Contraseña',
    confirmNewPassword: 'Confirmar Nueva Contraseña',
    dangerZone: 'Zona de Peligro',
    deleteAccountWarning:
      'Una vez que elimine su cuenta, no hay vuelta atrás. Todos sus datos se eliminarán permanentemente.',
    savingChanges: 'Guardando cambios...',
    savePreferences: 'Guardar Preferencias',
    usernameTaken: 'Este nombre de usuario ya está en uso',
    deleteAccountDescription: 'Esta acción es irreversible. Todos sus datos se eliminarán permanentemente.',
    confirmUsername: 'Confirme su correo electrónico',
    password: 'Contraseña',
    enterPassword: 'Ingrese su contraseña',
    passwordChangeError: 'Error al cambiar la contraseña',
    passwordChangeSuccess: 'Contraseña cambiada exitosamente',
    passwordsDoNotMatch: 'Las contraseñas no coinciden',
    accountDeleteSuccess: 'Cuenta eliminada exitosamente',
    accountDeleteError: 'Error al eliminar la cuenta',
    passwordChanged: 'Contraseña cambiada',
    changingPassword: 'Cambiando Contraseña...',
    confirmPasswordLabel: 'Confirmar Contraseña',
    changePasswordDescription: 'Cambie su contraseña para mantener su cuenta segura',
    dangerZoneDescription: 'Estas acciones son irreversibles y eliminarán permanentemente sus datos',
    deletingAccount: 'Eliminando cuenta...',
    deleteAccountError: 'Error al eliminar la cuenta',
  },
  accessibility: {
    skipToContent: 'Saltar al contenido principal',
  },
  profile: {
    title: 'Título',
    about: 'Acerca de',
    activity: 'Actividad',
    projects: 'Proyectos',
    recentProjects: 'Proyectos Recientes',
    recentAnalyses: 'Análisis Recientes',
    accountDetails: 'Detalles de la Cuenta',
    accountType: 'Tipo de Cuenta',
    joinDate: 'Fecha de Registro',
    lastActive: 'Última Actividad',
    projectsCreated: 'Proyectos Creados',
    imagesUploaded: 'Imágenes Subidas',
    segmentationsCompleted: 'Segmentaciones Completadas',
    pageTitle: 'Perfil de Usuario',
    editProfile: 'Editar Perfil',
    joined: 'Se unió',
    statistics: 'Estadísticas',
    images: 'Imágenes',
    analyses: 'Análisis',
    storageUsed: 'Almacenamiento Usado',
    recentActivity: 'Actividad Reciente',
    noRecentActivity: 'No hay actividad reciente',
    fetchError: 'Fallo al cargar datos del perfil',
    aboutMe: 'Acerca de Mí',
    noBio: 'No se proporcionó biografía',
    avatarHelp: 'Haga clic en el ícono de cámara para subir una imagen de perfil',
    avatarImageOnly: 'Por favor seleccione un archivo de imagen',
    avatarTooLarge: 'La imagen debe ser menor de 5MB',
    avatarUpdated: 'Imagen de perfil actualizada',
    avatarUploadError: 'Fallo al subir la imagen de perfil',
    avatarRemoved: 'Imagen de perfil eliminada',
    avatarRemoveError: 'Fallo al eliminar la imagen de perfil',
    cropAvatarDescription: 'Ajuste el área de recorte para establecer su imagen de perfil',
    description: 'Actualice su información personal e imagen de perfil',
    saveButton: 'Guardar Perfil',
    username: 'Nombre de Usuario',
    usernamePlaceholder: 'Ingrese su nombre de usuario',
    fullName: 'Nombre Completo',
    fullNamePlaceholder: 'Ingrese su nombre completo',
    titlePlaceholder: 'ej. Investigador, Profesor',
    organization: 'Organización',
    organizationPlaceholder: 'Ingrese su organización o institución',
    bio: 'Biografía',
    bioPlaceholder: 'Cuéntenos sobre usted',
    bioDescription: 'Una breve descripción sobre usted que será visible en su perfil',
    location: 'Ubicación',
    locationPlaceholder: 'ej. Praga, República Checa',
    uploadAvatar: 'Subir Imagen de Perfil',
    removeAvatar: 'Eliminar Imagen de Perfil',
    cropAvatar: 'Recortar Imagen de Perfil',
    activityDescription: 'Actividad del sistema',
    email: 'Correo Electrónico',
    notProvided: 'No proporcionado',
  },
  termsPage: {
    title: 'Términos de Servicio',
    acceptance: {
      title: '1. Aceptación de Términos',
      paragraph1:
        'Al acceder o usar SpheroSeg, usted acepta estar sujeto a estos Términos de Servicio y todas las leyes y regulaciones aplicables. Si no está de acuerdo con alguno de estos términos, tiene prohibido usar este servicio.',
    },
    useLicense: {
      title: '2. Licencia de Uso',
      paragraph1:
        'Se otorga permiso para usar temporalmente SpheroSeg solo para fines personales, no comerciales o de investigación académica. Esto es el otorgamiento de una licencia, no una transferencia de título.',
    },
    dataUsage: {
      title: '3. Uso de Datos',
      paragraph1:
        'Todos los datos cargados en SpheroSeg siguen siendo de su propiedad. No reclamamos la propiedad de su contenido, pero requerimos ciertos permisos para proporcionar el servicio.',
    },
    limitations: {
      title: '4. Limitaciones',
      paragraph1:
        'En ningún caso SpheroSeg será responsable de cualquier daño que surja del uso o la incapacidad de usar la plataforma, incluso si hemos sido informados de la posibilidad de dicho daño.',
    },
    revisions: {
      title: '5. Revisiones y Errores',
      paragraph1:
        'Los materiales que aparecen en SpheroSeg podrían incluir errores técnicos, tipográficos o fotográficos. No garantizamos que los materiales sean precisos, completos o actuales.',
    },
    governingLaw: {
      title: '6. Ley Aplicable',
      paragraph1:
        'Estos términos se regirán e interpretarán de acuerdo con las leyes del país en el que se aloja el servicio, y usted se somete irrevocablemente a la jurisdicción exclusiva de los tribunales en esa ubicación.',
    },
    lastUpdated: 'Última actualización: 7 de enero de 2025',
  },
  invitation: {
    title: 'Invitación al Proyecto',
    processing: 'Procesando invitación...',
    successTitle: '¡Invitación Aceptada!',
    successMessage: 'Ahora tiene acceso a "{{projectName}}" compartido por {{ownerName}}.',
    redirecting: 'Redirigiendo al proyecto...',
    errorTitle: 'No se pudo aceptar la invitación',
    loginRequired: 'Inicio de sesión requerido',
    loginMessage: 'Por favor inicie sesión para aceptar esta invitación al proyecto.',
    signIn: 'Iniciar Sesión',
    createAccount: 'Crear Cuenta',
    goToDashboard: 'Ir al Panel de Control',
    invalidLink: 'Enlace de invitación inválido',
    expired: 'Este enlace de invitación ha expirado o es inválido',
    notForYou: 'Esta invitación no está destinada para su cuenta',
    genericError: 'Fallo al aceptar la invitación. Por favor intente de nuevo.',
    acceptedSuccess: 'Invitación aceptada exitosamente',
  },
  privacyPage: {
    title: 'Política de Privacidad',
    introduction: {
      title: '1. Introducción',
      paragraph1:
        'En SpheroSeg, estamos comprometidos a proteger su privacidad. Esta Política de Privacidad explica qué información recopilamos, cómo la usamos y qué derechos tiene con respecto a su información.',
    },
    dataCollection: {
      title: '2. Información que Recopilamos',
      paragraph1: 'Recopilamos información que usted nos proporciona directamente, incluyendo:',
      list: [
        'Información de cuenta (correo, nombre, institución)',
        'Imágenes y datos cargados para segmentación',
        'Metadatos del proyecto y resultados de análisis',
        'Datos de uso y registros de actividad',
      ],
    },
    dataUsage: {
      title: '3. Cómo Usamos Su Información',
      paragraph1: 'Usamos la información recopilada para:',
      list: [
        'Proporcionar y mantener nuestros servicios',
        'Procesar sus solicitudes de segmentación de imágenes',
        'Mejorar nuestros algoritmos y servicios',
        'Comunicarnos con usted sobre su cuenta',
        'Garantizar la seguridad y prevenir abusos',
      ],
    },
    dataStorage: {
      title: '4. Almacenamiento y Seguridad de Datos',
      paragraph1:
        'Implementamos medidas técnicas y organizativas apropiadas para proteger su información personal contra el acceso no autorizado, alteración, divulgación o destrucción.',
      paragraph2:
        'Sus datos se almacenan en servidores seguros y se eliminan según nuestra política de retención de datos.',
    },
    dataSharing: {
      title: '5. Compartir Datos',
      paragraph1:
        'No vendemos, intercambiamos ni transferimos de otra manera su información personal a terceros sin su consentimiento, excepto como se describe en esta política o según lo exija la ley.',
    },
    userRights: {
      title: '6. Sus Derechos',
      paragraph1: 'Usted tiene derecho a:',
      list: [
        'Acceder a su información personal',
        'Corregir información inexacta',
        'Solicitar la eliminación de sus datos',
        'Exportar sus datos',
        'Oponerse al procesamiento de sus datos',
      ],
    },
    cookies: {
      title: '7. Cookies y Tecnologías de Seguimiento',
      paragraph1:
        'Usamos cookies y tecnologías similares para mejorar su experiencia, analizar el uso del sitio y personalizar el contenido.',
    },
    changes: {
      title: '8. Cambios a Esta Política',
      paragraph1:
        'Podemos actualizar nuestra Política de Privacidad de vez en cuando. Le notificaremos cualquier cambio publicando la nueva Política de Privacidad en esta página.',
    },
    contact: {
      title: '9. Contáctenos',
      paragraph1: 'Si tiene alguna pregunta sobre esta Política de Privacidad, contáctenos en:',
      email: '<EMAIL>',
    },
    lastUpdated: 'Última actualización: 7 de enero de 2025',
  },
  about: {
    title: 'Acerca de SpheroSeg',
    mission: {
      title: 'Nuestra Misión',
      description:
        'SpheroSeg es una plataforma avanzada específicamente diseñada para la segmentación y análisis de esferoides celulares en imágenes microscópicas. Combinamos algoritmos de inteligencia artificial de vanguardia con una interfaz intuitiva para proporcionar a los investigadores capacidades precisas de detección de límites de esferoides y análisis.',
      vision:
        'Nuestra visión es acelerar el descubrimiento científico haciendo que el análisis avanzado de imágenes sea accesible para investigadores en todo el mundo, permitiéndoles enfocarse en su investigación en lugar de desafíos técnicos.',
    },
    technology: {
      title: 'Nuestra Tecnología',
      description:
        'Construido con modelos de aprendizaje profundo de última generación y técnicas de visión por computadora, SpheroSeg ofrece una precisión incomparable en la segmentación de esferoides.',
      feature1: {
        title: 'Segmentación Potenciada por IA',
        description:
          'Modelos avanzados de aprendizaje profundo entrenados en diversas imágenes de esferoides garantizan resultados de segmentación precisos y confiables.',
      },
      feature2: {
        title: 'Procesamiento en Tiempo Real',
        description:
          'Algoritmos optimizados proporcionan tiempos de procesamiento rápidos, permitiéndole analizar grandes conjuntos de datos de manera eficiente.',
      },
      feature3: {
        title: 'Análisis Integral',
        description:
          'Extraiga métricas detalladas incluyendo área, perímetro, circularidad y más para cada esferoide segmentado.',
      },
    },
    team: {
      title: 'Nuestro Equipo',
      description:
        'SpheroSeg fue desarrollado por un equipo dedicado de investigadores e ingenieros apasionados por avanzar en la investigación biomédica',
      member1: {
        name: 'Michal Průšek',
        role: 'Desarrollador Principal, FNSPE CTU Praga',
      },
      member2: {
        name: 'Adam Novozámský',
        role: 'Supervisor, UTIA CAS',
      },
      member3: {
        name: 'Equipo de Investigación',
        role: 'Departamento de Bioquímica y Microbiología, UCT Praga',
      },
    },
    contact: {
      title: 'Contáctenos',
      description: '¿Tiene preguntas o necesita soporte? ¡Estamos aquí para ayudar!',
      email: 'Contactar por Correo',
      github: 'Ver en GitHub',
      twitter: 'Seguir en Twitter',
    },
  },
  shortcuts: {
    button: 'Atajos',
    editMode: 'Cambiar a Modo Edición',
    sliceMode: 'Cambiar a Modo División',
    addPointMode: 'Cambiar a Modo Añadir Punto',
    holdShift: 'Mantener Shift para auto-añadir puntos (en Modo Edición)',
    undo: 'Deshacer',
    redo: 'Rehacer',
    deletePolygon: 'Eliminar polígono seleccionado',
    cancel: 'Cancelar operación actual',
    zoomIn: 'Acercar',
    zoomOut: 'Alejar',
    resetView: 'Restablecer vista',
    title: 'Atajos de Teclado',
    viewMode: 'Modo Vista',
    editVerticesMode: 'Modo Editar Vértices',
    addPointsMode: 'Modo Añadir Puntos',
    createPolygonMode: 'Modo Crear Polígono',
    save: 'Guardar',
    description: 'Estos atajos funcionan dentro del editor de segmentación para un trabajo más rápido y cómodo.',
  },
  imageProcessor: {
    segmentationStarted: 'El proceso de segmentación ha comenzado...',
    startSegmentationTooltip: 'Iniciar Segmentación',
    processingTooltip: 'Procesando...',
    savingTooltip: 'Guardando...',
    completedTooltip: 'Segmentación completada',
    retryTooltip: 'Reintentar segmentación',
  },
  uploader: {
    dragDrop: 'Arrastre y suelte imágenes aquí o haga clic para seleccionar archivos',
    dropFiles: 'Suelte archivos aquí...',
    segmentAfterUploadLabel: 'Segmentar imágenes inmediatamente después de cargar',
    filesToUpload: 'Archivos para cargar',
    uploadBtn: 'Cargar',
    uploadError: 'Ocurrió un error durante la carga. Por favor intente de nuevo.',
    clickToUpload: 'Haga clic para buscar archivos',
    selectProjectLabel: 'Seleccionar proyecto',
    selectProjectPlaceholder: 'Seleccionar proyecto...',
    noProjectsFound: 'No se encontraron proyectos. Cree uno primero.',
    imageOnly: '(Solo archivos de imagen)',
    uploadingImages: 'Cargando imágenes...',
    uploadComplete: 'Carga completada',
    uploadFailed: 'Carga fallida',
    processingImages: 'Procesando imágenes...',
    dragAndDropFiles: 'Arrastre y suelte archivos aquí',
    or: 'o',
    clickToSelect: 'Haga clic para seleccionar archivos',
  },
  images: {
    uploadImages: 'Cargar Imágenes',
    dragDrop: 'Arrastre y suelte imágenes aquí',
    clickToSelect: 'o haga clic para seleccionar archivos',
    acceptedFormats: 'Formatos soportados: JPEG, PNG, TIFF, BMP (máx 10MB)',
    uploadProgress: 'Progreso de Carga',
    uploadingTo: 'Cargando a',
    currentProject: 'Proyecto Actual',
    autoSegment: 'Segmentar automáticamente las imágenes después de cargar',
    uploadCompleted: 'Carga Completada',
    uploadFailed: 'Carga Fallida',
    imagesUploaded: 'Imágenes cargadas exitosamente',
    imagesFailed: 'Carga de imagen fallida',
    viewAnalyses: 'Ver Análisis',
    noAnalysesYet: 'Sin análisis aún',
    runAnalysis: 'Ejecutar Análisis',
    viewResults: 'Ver Resultados',
    dropImagesHere: 'Suelte imágenes aquí...',
    selectProjectFirst: 'Por favor seleccione un proyecto primero',
    projectRequired: 'Debe seleccionar un proyecto antes de cargar imágenes',
    imageOnly: '(Solo archivos de imagen)',
    dropFiles: 'Suelte archivos aquí...',
    filesToUpload: 'Archivos para cargar ({{count}})',
    uploadBtn: 'Cargar {{count}} imágenes',
    uploadError: 'Ocurrió un error durante la carga. Por favor intente de nuevo.',
    noProjectsToUpload: 'No hay proyectos disponibles. Cree un proyecto primero.',
    notFound: 'Proyecto "{{projectName}}" no encontrado. Puede haber sido eliminado.',
    errors: {
      imageOrProjectNotFound: 'Imagen o proyecto no encontrado.',
      failedToDeleteImage: 'Fallo al eliminar imagen',
      imageOrProjectNotFoundForNavigation: 'Imagen o proyecto no encontrado para navegación, o falta UUID.',
      imageNotFoundForClearingSegmentation: 'Imagen no encontrada para limpiar segmentación o falta UUID.',
      failedToClearSegmentation: 'Fallo al limpiar segmentación',
    },
    success: {
      localImageDeleted: 'Imagen local eliminada exitosamente',
      imageDeleted: 'Imagen eliminada exitosamente',
      segmentationCleared: 'Segmentación limpiada exitosamente.',
    },
    info: {
      clearingSegmentation: 'Limpiando segmentación para imagen {{imageName}}...',
      selectAtLeastOneImage: 'Por favor seleccione al menos una imagen.',
    },
  },
  export: {
    formatDescriptions: {
      COCO: 'Formato JSON de Common Objects in Context (COCO) para detección de objetos',
      YOLO: 'Formato de texto You Only Look Once (YOLO) para detección de objetos',
      MASK: 'Imágenes de máscara binaria para cada objeto segmentado',
      POLYGONS: 'Coordenadas de polígono en formato JSON',
      DATUMARO: 'Formato Datumaro - representación unificada de conjunto de datos',
      CVAT_MASKS: 'Formato XML CVAT con anotaciones de polígono',
      CVAT_YAML: 'Formato YAML CVAT para intercambio de anotaciones',
    },
    exportCompleted: 'Exportación completada',
    exportFailed: 'Exportación fallida',
    title: 'Exportar Datos de Segmentación',
    spheroidMetrics: 'Métricas de Esferoides',
    visualization: 'Visualización',
    cocoFormat: 'Formato COCO',
    close: 'Cerrar',
    metricsExported: 'Métricas exportadas exitosamente',
    options: {
      includeMetadata: 'Incluir metadatos',
      includeSegmentation: 'Incluir segmentación',
      selectExportFormat: 'Seleccionar formato de exportación',
      includeObjectMetrics: 'Incluir métricas de objetos',
      selectMetricsFormat: 'Seleccionar formato de métricas',
      metricsFormatDescription: {
        EXCEL: 'Archivo Excel (.xlsx)',
        CSV: 'Archivo CSV (.csv)',
      },
      includeImages: 'Incluir imágenes originales',
      exportMetricsOnly: 'Exportar solo métricas',
      metricsRequireSegmentation: 'Exportar métricas requiere segmentación completada',
    },
    formats: {
      COCO: 'COCO JSON',
      YOLO: 'YOLO TXT',
      MASK: 'Máscara (TIFF)',
      POLYGONS: 'Polígonos (JSON)',
      DATUMARO: 'Datumaro',
      CVAT_MASKS: 'CVAT Máscaras (XML)',
      CVAT_YAML: 'CVAT YAML',
    },
    metricsFormats: {
      EXCEL: 'Excel (.xlsx)',
      CSV: 'CSV (.csv)',
    },
    selectImagesForExport: 'Seleccionar imágenes para exportar',
    selectImagesToExport: 'Seleccionar imágenes para exportar',
    noImagesAvailable: 'No hay imágenes disponibles',
    backToProject: 'Volver al Proyecto',
    exportImages: 'Exportar Imágenes',
    maskExportError: 'Error al exportar máscara',
    maskExportStarted: 'Exportación de máscara iniciada',
    metricsRequireSegmentation: 'Las métricas requieren que la segmentación esté completada',
    noImageSelectedError: 'No hay imagen seleccionada para exportar',
  },
  metrics: {
    area: 'Área',
    perimeter: 'Perímetro',
    circularity: 'Circularidad',
    sphericity: 'Esfericidad',
    solidity: 'Solidez',
    compactness: 'Compacidad',
    convexity: 'Convexidad',
    visualization: 'Visualización de Métricas',
    visualizationHelp: 'Representación visual de métricas para todos los esferoides en esta imagen',
    barChart: 'Gráfico de Barras',
    pieChart: 'Gráfico Circular',
    comparisonChart: 'Gráfico de Comparación',
    keyMetricsComparison: 'Comparación de Métricas Clave',
    areaDistribution: 'Distribución de Área',
    shapeMetricsComparison: 'Comparación de Métricas de Forma',
    noPolygonsFound: 'No se encontraron polígonos para análisis',
  },
  imageStatus: {
    completed: 'Procesado',
    processing: 'Procesando',
    pending: 'Pendiente',
    failed: 'Fallido',
    noImage: 'Sin imagen',
    untitledImage: 'Imagen sin título',
  },
  projectActions: {
    deleteTooltip: 'Eliminar proyecto',
    deleteConfirmTitle: '¿Está seguro?',
    deleteConfirmDesc:
      '¿Está seguro de que desea eliminar el proyecto "{{projectName}}"? Esta acción no se puede deshacer.',
    deleteSuccess: 'El proyecto "{{projectName}}" ha sido eliminado exitosamente.',
    deleteError: 'Eliminación del proyecto fallida.',
    duplicateSuccess: 'El proyecto "{{projectName}}" ha sido duplicado exitosamente.',
    duplicateError: 'Duplicación del proyecto fallida.',
    makePrivateTooltip: 'Marcar como privado',
    makePublicTooltip: 'Marcar como público',
    shareTooltip: 'Compartir proyecto',
    downloadTooltip: 'Descargar proyecto',
    notFound: 'Proyecto "{{projectName}}" no encontrado. Puede haber sido eliminado.',
  },
  editor: {
    backButtonTooltip: 'Volver a la vista general del proyecto',
    exportButtonTooltip: 'Exportar datos de segmentación actuales',
    saveTooltip: 'Guardar cambios',
    image: 'Imagen',
    previousImage: 'Imagen anterior',
    nextImage: 'Imagen siguiente',
    resegmentButton: 'Resegmentar',
    resegmentButtonTooltip: 'Ejecutar segmentación nuevamente en esta imagen',
    exportMaskButton: 'Exportar máscara',
    exportMaskButtonTooltip: 'Exportar máscara de segmentación para esta imagen',
    backButton: 'Atrás',
    exportButton: 'Exportar',
    saveButton: 'Guardar',
    loadingProject: 'Cargando proyecto...',
    loadingImage: 'Cargando imagen...',
    sliceErrorInvalidPolygon: 'No se puede dividir: Polígono seleccionado inválido.',
    sliceWarningInvalidResult: 'La división creó polígonos demasiado pequeños e inválidos.',
    sliceWarningInvalidIntersections:
      'División inválida: La línea de corte debe intersectar el polígono en exactamente dos puntos.',
    sliceSuccess: 'Polígono dividido exitosamente.',
    noPolygonToSlice: 'No hay polígonos disponibles para dividir.',
    savingTooltip: 'Guardando...',
  },
  segmentationPage: {
    noImageSelected: 'No hay imagen seleccionada para resegmentación.',
    resegmentationStarted: 'Iniciando resegmentación usando red neuronal ResUNet...',
    resegmentationQueued: 'La resegmentación ha sido encolada.',
    resegmentationCompleted: 'Resegmentación completada exitosamente.',
    resegmentationFailed: 'Resegmentación fallida.',
    resegmentationTimeout: 'Tiempo de espera de resegmentación agotado. Verifique el estado de la cola.',
    resegmentationError: 'Fallo al iniciar la resegmentación.',
    resegmentTooltip: 'Resegmentar',
  },
  share: {
    accepted: 'Aceptado',
    alreadyShared: 'Ya compartido con este usuario',
    canEdit: 'Puede editar',
    copyToClipboard: 'Copiar al portapapeles',
    edit: 'Editar',
    email: 'Correo electrónico',
    failedToCopy: 'Fallo al copiar enlace',
    failedToGenerateLink: 'Fallo al generar enlace para compartir',
    failedToLoadShares: 'Fallo al cargar usuarios compartidos',
    failedToRemove: 'Fallo al eliminar compartido',
    failedToShare: 'Fallo al compartir proyecto',
    generateLink: 'Generar enlace',
    generateNewLink: 'Generar nuevo enlace',
    generating: 'Generando...',
    invalidEmail: 'Dirección de correo inválida',
    invalidEmailOrPermission: 'Correo o permiso inválido',
    invite: 'Invitar',
    inviteByEmail: 'Invitar por correo',
    inviteByLink: 'Invitar por enlace',
    linkCopied: 'Enlace copiado al portapapeles',
    linkGenerated: 'Enlace para compartir generado',
    linkPermissions: 'Permisos del enlace',
    noPermission: 'Sin permiso',
    noShares: 'No hay usuarios compartidos',
    pendingAcceptance: 'Aceptación pendiente',
    permissions: 'Permisos',
    projectNotFound: 'Proyecto no encontrado',
    removeShare: 'Eliminar compartido',
    selectAccessLevel: 'Seleccionar nivel de acceso',
    selectPermission: 'Por favor seleccione un tipo de permiso',
    shareDescription: 'Compartir este proyecto con otros usuarios',
    sharedWith: 'Compartido con',
    shareLinkDescription: 'Cualquiera con este enlace puede acceder al proyecto',
    shareProject: 'Compartir Proyecto',
    shareProjectTitle: 'Compartir proyecto "{{projectName}}"',
    sharing: 'Compartiendo...',
    sharedSuccess: 'El proyecto "{{projectName}}" ha sido compartido con {{email}}',
    removedSuccess: 'Se ha eliminado el acceso compartido con {{email}}',
    status: 'Estado',
    userEmail: 'Correo del usuario',
    view: 'Ver',
    viewOnly: 'Solo vista',
  },
};
