// German translations
export default {
  // Segmentation context menu
  segmentation: {
    contextMenu: {
      editPolygon: 'Polygon bearbeiten',
      splitPolygon: 'Polygon teilen',
      deletePolygon: 'Polygon löschen',
      confirmDeleteTitle: 'Sind Si<PERSON> sicher, dass Sie das Polygon löschen möchten?',
      confirmDeleteMessage:
        'Diese Aktion ist unwiderruflich. Das Polygon wird dauerhaft aus der Segmentierung entfernt.',
      duplicateVertex: 'Eckpunkt duplizieren',
      deleteVertex: 'Eckpunkt löschen',
    },
    title: 'Segmentierungs-Editor',
    resolution: '{width}x{height}',
    batch: {
      mixed: 'Segmentierung: {{successCount}} Bilder erfolgreich in Warteschlange, {{failCount}} fehlgeschlagen',
      allSuccess: 'Segmentierung: Alle {{count}} Bilder erfolgreich in Warteschlange',
      allFailed: 'Segmentierung: Alle {{count}} Bilder fehlgeschlagen',
    },
    queue: {
      title: 'Segmentierungs-Warteschlange',
      summary: '{{total}} Aufgaben insgesamt ({{running}} in Bearbeitung, {{queued}} in Warteschlange)',
      noRunningTasks: 'Keine laufenden Aufgaben',
      noQueuedTasks: 'Keine Aufgaben in Warteschlange',
      task: 'Aufgabe',
      statusRunning: 'Segmentierung: {{count}} laufend{{queued}}',
      statusQueued: ', {{count}} in Warteschlange',
      statusOnlyQueued: 'Segmentierung: {{count}} in Warteschlange',
      statusOnlyQueued_one: 'Segmentierung: 1 in Warteschlange',
      statusOnlyQueued_other: 'Segmentierung: {{count}} in Warteschlange',
      processing: 'In Bearbeitung',
      queued: 'In Warteschlange',
      statusProcessing: 'Segmentierung: {{count}} in Bearbeitung',
      statusReady: 'Bereit',
      tasksTotal: '{{total}} Aufgaben insgesamt ({{running}} in Bearbeitung, {{queued}} in Warteschlange)',
    },
    selectPolygonForEdit: 'Wählen Sie ein Polygon zum Bearbeiten aus',
    selectPolygonForSlice: 'Wählen Sie ein Polygon zum Teilen aus',
    selectPolygonForAddPoints: 'Wählen Sie ein Polygon aus, um Punkte hinzuzufügen',
    clickToAddPoint: 'Klicken, um einen Punkt hinzuzufügen',
    clickToCompletePolygon: 'Auf den ersten Punkt klicken, um das Polygon zu schließen',
    clickToAddFirstSlicePoint: 'Klicken, um den ersten Schnittpunkt hinzuzufügen',
    clickToAddSecondSlicePoint: 'Klicken, um den zweiten Schnittpunkt hinzuzufügen',
    polygonCreationMode: 'Polygon-Erstellungsmodus',
    polygonEditMode: 'Polygon-Bearbeitungsmodus',
    polygonSliceMode: 'Polygon-Teilungsmodus',
    polygonAddPointsMode: 'Punkte hinzufügen-Modus',
    viewMode: 'Ansichtsmodus',
    totalPolygons: 'Polygone gesamt',
    totalVertices: 'Eckpunkte gesamt',
    vertices: 'Eckpunkte',
    zoom: 'Zoom',
    mode: 'Modus',
    selected: 'Ausgewählt',
    none: 'Keine',
    polygons: 'Polygone',
    imageNotFound: 'Bild nicht gefunden',
    returnToProject: 'Zurück zum Projekt',
    backToProject: 'Zurück zum Projekt',
    previousImage: 'Vorheriges Bild',
    nextImage: 'Nächstes Bild',
    toggleShortcuts: 'Tastenkürzel anzeigen',
    modes: {
      view: 'Ansichtsmodus',
      edit: 'Bearbeitungsmodus',
      create: 'Erstellungsmodus',
      slice: 'Teilungsmodus',
      addPoints: 'Punkte hinzufügen-Modus',
      deletePolygon: 'Polygon löschen-Modus',
      createPolygon: 'Polygon erstellen-Modus',
      editVertices: 'Eckpunkte bearbeiten-Modus',
      editMode: 'Bearbeitungsmodus',
      slicingMode: 'Teilungsmodus',
      pointAddingMode: 'Punkt hinzufügen-Modus',
    },
    status: {
      processing: 'In Bearbeitung',
      queued: 'In Warteschlange',
      completed: 'Abgeschlossen',
      failed: 'Fehlgeschlagen',
      pending: 'Ausstehend',
      withoutSegmentation: 'Keine Segmentierung',
    },
    autoSave: {
      enabled: 'Auto-Speichern: Aktiviert',
      disabled: 'Auto-Speichern: Deaktiviert',
      idle: 'Auto-Speichern: Inaktiv',
      pending: 'Ausstehend...',
      saving: 'Speichert...',
      success: 'Gespeichert',
      error: 'Fehler',
    },
    loading: 'Segmentierung wird geladen...',
    polygon: 'Polygon',
    unsavedChanges: 'Nicht gespeicherte Änderungen',
    noData: 'Keine Segmentierungsdaten verfügbar',
    noPolygons: 'Keine Polygone gefunden',
    regions: 'Segmentierung',
    position: 'Position',
    polygonDeleted: 'Polygon erfolgreich gelöscht',
    saveSuccess: 'Segmentierung erfolgreich gespeichert',
    resegmentSuccess: 'Neusegmentierung erfolgreich gestartet',
    resegmentComplete: 'Neusegmentierung erfolgreich abgeschlossen',
    resegmentError: 'Neusegmentierung des Bildes fehlgeschlagen',
    resegmentButton: 'Neu segmentieren',
    completedSegmentation: 'Abgeschlossen',
    resegmentButtonTooltip: 'Mit neuronalem Netzwerk neu segmentieren',
    processingImage: 'Bild wird verarbeitet...',
    helpTips: {
      title: 'Tipps:',
      edit: {
        createPoint: 'Klicken, um einen neuen Punkt zu erstellen',
        shiftPoints: 'Shift gedrückt halten, um automatisch eine Punktsequenz zu erstellen',
        closePolygon: 'Polygon schließen, indem Sie auf den ersten Punkt klicken',
      },
      slice: {
        start: 'Klicken, um Schnitt zu starten',
        finish: 'Erneut klicken, um Schnitt zu beenden',
        cancel: 'Esc zum Abbrechen des Schneidens',
      },
      addPoint: {
        hover: 'Über Polygonlinie fahren',
        click: 'Klicken, um Punkt zum ausgewählten Polygon hinzuzufügen',
        exit: 'Esc zum Verlassen des Hinzufügen-Modus',
      },
      view: {
        pan: 'Verschieben: Klicken und ziehen',
        selectPolygon: 'Auswählen: Auf Polygon klicken',
        zoom: 'Zoom: Mausrad',
      },
    },
    imageNotFoundDescription: 'Das angeforderte Bild konnte nicht gefunden werden',
    invalidImageDimensions: 'Ungültige Bildabmessungen',
    noDataToSave: 'Keine Änderungen zum Speichern',
    polygonDuplicated: 'Polygon dupliziert',
    polygonNotFound: 'Polygon nicht gefunden',
    polygonSimplified: 'Polygon vereinfacht',
    polygonSimplifyFailed: 'Vereinfachung des Polygons fehlgeschlagen',
    polygonSliced: 'Polygon erfolgreich geteilt',
    resegment: {
      error: {
        exception: 'Neusegmentierungsfehler: {{error}}',
        failed: 'Neusegmentierung fehlgeschlagen',
        missingData: 'Fehlende erforderliche Daten für die Neusegmentierung',
      },
      success: 'Neusegmentierung erfolgreich abgeschlossen',
    },
    resegmentMultipleError: 'Fehler bei der Neusegmentierung mehrerer Bilder',
    resegmentMultipleSuccess: 'Mehrere Bilder erfolgreich neu segmentiert',
    resegmenting: 'Neusegmentierung läuft...',
    resegmentingMultiple: 'Mehrere Bilder werden neu segmentiert...',
    saveError: 'Fehler beim Speichern der Segmentierung',
    segmentationLoading: 'Segmentierung wird geladen...',
    segmentationPolygon: 'Segmentierungspolygon',
    selectPolygonFirst: 'Bitte wählen Sie zuerst ein Polygon aus',
    sliceFailed: 'Teilen des Polygons fehlgeschlagen',
    undoRestored: 'Aktion rückgängig gemacht',
    undoWhileDraggingError: 'Rückgängig während des Ziehens nicht möglich',
    vertexDeleteFailed: 'Löschen des Eckpunkts fehlgeschlagen',
    vertexDeleted: 'Eckpunkt gelöscht',
    vertexDuplicateFailed: 'Duplizieren des Eckpunkts fehlgeschlagen',
    vertexDuplicated: 'Eckpunkt dupliziert',
  },
  // Error messages
  errors: {
    somethingWentWrong: 'Etwas ist schiefgelaufen',
    componentError: 'In dieser Komponente ist ein Fehler aufgetreten',
    errorDetails: 'Fehlerdetails',
    tryAgain: 'Erneut versuchen',
    reloadPage: 'Seite neu laden',
    goBack: 'Zurück',
    notFound: 'Seite nicht gefunden',
    pageNotFoundMessage: 'Die angeforderte Seite konnte nicht gefunden werden',
    returnToHome: 'Zur Startseite zurückkehren',
    unauthorized: 'Nicht autorisierter Zugriff',
    forbidden: 'Zugriff verweigert',
    serverError: 'Serverfehler',
    networkError: 'Netzwerkfehler',
    timeoutError: 'Zeitüberschreitung der Anfrage',
    validationError: 'Validierungsfehler',
    unknownError: 'Unbekannter Fehler',
    goHome: 'Zur Startseite',
    fetchSegmentationFailed: 'Segmentierung konnte nicht abgerufen werden',
    fetchImageFailed: 'Bild konnte nicht abgerufen werden',
    saveSegmentationFailed: 'Segmentierung konnte nicht gespeichert werden',
    missingPermissions: 'Unzureichende Berechtigungen',
    invalidInput: 'Ungültige Eingabe',
    resourceNotFound: 'Ressource nicht gefunden',
  },
  project: {
    detail: {
      noImagesSelected: 'Keine Bilder ausgewählt',
      triggeringResegmentation: 'Starte Neusegmentierung für {{count}} Bilder...',
      deleteConfirmation:
        'Sind Sie sicher, dass Sie {{count}} Bilder löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
      deletingImages: 'Lösche {{count}} Bilder...',
      deleteSuccess: '{{count}} Bilder erfolgreich gelöscht',
      deleteFailed: 'Löschen von {{count}} Bildern fehlgeschlagen',
      preparingExport: 'Export von {{count}} Bildern wird vorbereitet...',
    },
    segmentation: {
      processingInBatches: 'Starte Segmentierung für {{count}} Bilder in {{batches}} Stapeln...',
      batchQueued: 'Stapel {{current}}/{{total}} erfolgreich in Warteschlange',
      batchQueuedFallback: 'Stapel {{current}}/{{total}} erfolgreich in Warteschlange (Fallback-Endpunkt)',
      batchError: 'Fehler bei Verarbeitung von Stapel {{current}}/{{total}}',
      partialSuccess: 'Segmentierung: {{success}} Bilder erfolgreich in Warteschlange, {{failed}} fehlgeschlagen',
      allSuccess: 'Segmentierung: Alle {{count}} Bilder erfolgreich in Warteschlange',
      allFailed: 'Segmentierung: Alle {{count}} Bilder fehlgeschlagen',
      startedImages: 'Segmentierung für {{count}} Bilder gestartet',
      queuedLocallyWarning:
        'Segmentierung für {{count}} Bilder lokal in Warteschlange. Serververbindung fehlgeschlagen.',
    },
    loading: 'Projekt wird geladen...',
    notFound: 'Projekt nicht gefunden',
    error: 'Fehler beim Laden des Projekts',
    empty: 'Dieses Projekt ist leer',
    noImagesText: 'Keine Bilder in diesem Projekt gefunden',
    addImages: 'Bilder hinzufügen, um zu beginnen',
    noImages: {
      title: 'Noch keine Bilder',
      description: 'Dieses Projekt enthält noch keine Bilder. Laden Sie Bilder hoch, um zu beginnen.',
      uploadButton: 'Bilder hochladen',
    },
    deleteProject: 'Projekt löschen',
    deleteConfirmation:
      'Sind Sie sicher, dass Sie das Projekt "{{projectName}}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    resegmentImage: 'Bild neu segmentieren',
    deleteImage: 'Bild löschen',
    duplicateProject: 'Projekt duplizieren',
    duplicateDescription:
      'Erstellen Sie eine Kopie dieses Projekts. Das neue Projekt wird mit dem von Ihnen angegebenen Namen erstellt.',
    newProjectName: 'Neuer Projektname',
    enterProjectName: 'Geben Sie den neuen Projektnamen ein',
    duplicate: 'Duplizieren',
  },
  projectsPage: {
    title: 'Projekte',
    description: 'Forschungsprojekte verwalten',
    createNew: 'Neues Projekt erstellen',
    createProject: 'Projekt erstellen',
    createProjectDesc: 'Neues Forschungsprojekt starten',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    projectNamePlaceholder: 'Projektnamen eingeben',
    projectDescriptionPlaceholder: 'Projektbeschreibung eingeben',
    projectCreated: 'Projekt erfolgreich erstellt',
    projectCreationFailed: 'Projekterstellung fehlgeschlagen',
    projectDeleted: 'Projekt erfolgreich gelöscht',
    projectDeletionFailed: 'Projektlöschung fehlgeschlagen',
    confirmDelete: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    confirmDeleteDescription:
      'Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.',
    deleteProject: 'Projekt löschen',
    editProject: 'Projekt bearbeiten',
    viewProject: 'Projekt anzeigen',
    projectUpdated: 'Projekt erfolgreich aktualisiert',
    projectUpdateFailed: 'Projektaktualisierung fehlgeschlagen',
    noProjects: 'Keine Projekte gefunden',
    createFirstProject: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    searchProjects: 'Projekte suchen...',
    filterProjects: 'Projekte filtern',
    sortProjects: 'Projekte sortieren',
    projectNameRequired: 'Projektname ist erforderlich',
    loginRequired: 'Sie müssen angemeldet sein, um ein Projekt zu erstellen',
    createdAt: 'Erstellt',
    updatedAt: 'Zuletzt aktualisiert',
    imageCount: 'Bilder',
    status: 'Status',
    actions: 'Aktionen',
    loading: 'Projekte werden geladen...',
    error: 'Fehler beim Laden der Projekte',
    retry: 'Erneut versuchen',
    duplicating: 'Projekt wird dupliziert...',
    duplicate: 'Duplizieren',
    duplicateSuccess: 'Projekt erfolgreich dupliziert',
    duplicateFailed: 'Projektduplizierung fehlgeschlagen',
    duplicateTitle: 'Projekt duplizieren',
    duplicateProject: 'Projekt duplizieren',
    duplicateProjectDescription:
      'Erstellen Sie eine Kopie dieses Projekts einschließlich aller Bilder. Sie können die Optionen unten anpassen.',
    duplicateCancelled: 'Projektduplizierung abgebrochen',
    duplicatingProject: 'Projekt wird dupliziert',
    duplicatingProjectDescription: 'Ihr Projekt wird dupliziert. Dies kann einen Moment dauern.',
    duplicateProgress: 'Duplizierungsfortschritt',
    duplicationComplete: 'Projektduplizierung abgeschlossen',
    duplicationTaskFetchError: 'Fehler beim Abrufen der Aufgabendaten',
    duplicationCancelError: 'Fehler beim Abbrechen der Duplizierung',
    duplicateProgressDescription:
      'Ihr Projekt wird dupliziert. Dieser Vorgang kann bei großen Projekten einige Zeit dauern.',
    duplicationPending: 'Ausstehend',
    duplicationProcessing: 'In Bearbeitung',
    duplicationCompleted: 'Abgeschlossen',
    duplicationFailed: 'Fehlgeschlagen',
    duplicationCancelled: 'Abgebrochen',
    duplicationCancellationFailed: 'Abbruch der Duplizierung fehlgeschlagen',
    duplicationSuccessMessage: 'Projekt erfolgreich dupliziert! Sie können jetzt auf das neue Projekt zugreifen.',
    copySegmentations: 'Segmentierungsergebnisse kopieren',
    resetImageStatus: 'Bildverarbeitungsstatus zurücksetzen',
    newProjectTitle: 'Neuer Projekttitel',
    itemsProcessed: 'Elemente verarbeitet',
    items: 'Elemente',
    unknownProject: 'Unbekanntes Projekt',
    activeTasks: 'Aktiv',
    allTasks: 'Alle',
    noActiveDuplications: 'Keine aktiven Duplizierungen',
    noDuplications: 'Keine Duplizierungsaufgaben gefunden',
    deleteProjectDescription: 'Diese Aktion wird das Projekt und alle zugehörigen Daten dauerhaft löschen.',
    deleteWarning:
      'Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.',
    untitledProject: 'Unbenanntes Projekt',
    typeToConfirm: 'Geben Sie "delete" ein, um zu bestätigen',
    deleteConfirm: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    exportProject: 'Projekt exportieren',
    archived: 'Archiviert',
    completed: 'Abgeschlossen',
    draft: 'Entwurf',
    active: 'Aktiv',
    createDate: 'Erstellt',
    lastModified: 'Zuletzt geändert',
    projectDescPlaceholder: 'Projektbeschreibung eingeben',
    creatingProject: 'Projekt wird erstellt...',
    noImages: {
      title: 'Noch keine Bilder',
      description:
        'Dieses Projekt enthält noch keine Bilder. Laden Sie Bilder hoch, um mit der Segmentierung zu beginnen.',
      uploadButton: 'Bilder hochladen',
    },
  },
  common: {
    appName: 'Sphäroid-Segmentierung',
    appNameShort: 'SpheroSeg',
    loading: 'Lädt...',
    loadingAccount: 'Ihr Konto wird geladen...',
    loadingApplication: 'Anwendung wird geladen...',
    selectAll: 'Alle auswählen',
    deselectAll: 'Alle abwählen',
    save: 'Speichern',
    cancel: 'Abbrechen',
    delete: 'Löschen',
    edit: 'Bearbeiten',
    create: 'Erstellen',
    search: 'Suchen',
    error: 'Fehler',
    success: 'Erfolg',
    reset: 'Zurücksetzen',
    clear: 'Löschen',
    close: 'Schließen',
    back: 'Zurück',
    signIn: 'Anmelden',
    signUp: 'Registrieren',
    signOut: 'Abmelden',
    signingIn: 'Anmeldung läuft...',
    settings: 'Einstellungen',
    profile: 'Profil',
    dashboard: 'Dashboard',
    project: 'Projekt',
    projects: 'Projekte',
    newProject: 'Neues Projekt',
    upload: 'Hochladen',
    download: 'Herunterladen',
    removeAll: 'Alle entfernen',
    uploadImages: 'Bilder hochladen',
    recentAnalyses: 'Aktuelle Analysen',
    noProjects: 'Keine Projekte gefunden',
    noImages: 'Keine Bilder gefunden',
    createYourFirst: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    tryAgain: 'Erneut versuchen',
    email: 'E-Mail',
    password: 'Passwort',
    confirmPassword: 'Passwort bestätigen',
    firstName: 'Vorname',
    lastName: 'Nachname',
    username: 'Benutzername',
    name: 'Name',
    description: 'Beschreibung',
    date: 'Datum',
    status: 'Status',
    image: 'Bild',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    language: 'Sprache',
    theme: 'Design',
    light: 'Hell',
    dark: 'Dunkel',
    system: 'System',
    welcome: 'Willkommen auf der Sphäroid-Segmentierungsplattform',
    account: 'Konto',
    passwordConfirm: 'Passwort bestätigen',
    manageAccount: 'Konto verwalten',
    changePassword: 'Passwort ändern',
    deleteAccount: 'Konto löschen',
    requestAccess: 'Zugang anfordern',
    accessRequest: 'Zugangsanfrage',
    createAccount: 'Konto erstellen',
    signInToAccount: 'Zum Konto anmelden',
    termsOfService: 'Nutzungsbedingungen',
    privacyPolicy: 'Datenschutzrichtlinie',
    termsOfServiceLink: 'Nutzungsbedingungen',
    privacyPolicyLink: 'Datenschutzrichtlinie',
    optional: 'Optional',
    saveChanges: 'Änderungen speichern',
    saving: 'Speichert',
    notSpecified: 'Nicht angegeben',
    enable: 'Aktivieren',
    disable: 'Deaktivieren',
    backToHome: 'Zurück zur Startseite',
    and: 'und',
    lastChange: 'Letzte Änderung',
    sort: 'Sortieren',
    emailPlaceholder: 'Geben Sie Ihre E-Mail-Adresse ein',
    passwordPlaceholder: 'Geben Sie Ihr Passwort ein',
    export: 'Exportieren',
    selectImages: 'Bilder auswählen',
    noImagesDescription: 'Laden Sie Bilder hoch, um mit Ihrem Projekt zu beginnen',
    yes: 'Ja',
    no: 'Nein',
    images: 'Bilder',
    files: 'Dateien',
    validationFailed: 'Validierung fehlgeschlagen',
    cropAvatar: 'Profilbild zuschneiden',
    profileTitle: 'Profil',
    profileDescription: 'Aktualisieren Sie Ihre für andere Benutzer sichtbaren Profilinformationen',
    profileUsername: 'Benutzername',
    profileUsernamePlaceholder: 'Geben Sie Ihren Benutzernamen ein',
    profileFullName: 'Vollständiger Name',
    profileFullNamePlaceholder: 'Geben Sie Ihren vollständigen Namen ein',
    profileTitlePlaceholder: 'z.B. Forscher, Professor',
    profileOrganization: 'Organisation',
    profileOrganizationPlaceholder: 'Geben Sie Ihre Organisation oder Institution ein',
    profileBio: 'Biografie',
    profileBioPlaceholder: 'Schreiben Sie eine kurze Biografie über sich',
    profileBioDescription: 'Kurze Beschreibung Ihrer Forschungsinteressen und Expertise',
    profileLocation: 'Standort',
    profileLocationPlaceholder: 'z.B. Prag, Tschechische Republik',
    profileSaveButton: 'Profil speichern',
    actions: 'Aktionen',
    view: 'Anzeigen',
    share: 'Teilen',
    projectNamePlaceholder: 'Projektnamen eingeben',
    projectDescPlaceholder: 'Projektbeschreibung eingeben',
    creatingProject: 'Projekt wird erstellt...',
    createSuccess: 'Projekt erfolgreich erstellt',
    unauthorized: 'Sie sind nicht berechtigt, diese Aktion auszuführen',
    forbidden: 'Zugriff verweigert',
    maxFileSize: 'Max. Dateigröße: {{size}}MB',
    accepted: 'Akzeptiert',
    processing: 'Verarbeitung läuft...',
    uploading: 'Hochladen...',
    uploadComplete: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    deletePolygon: 'Polygon löschen',
    pleaseLogin: 'Bitte melden Sie sich an, um fortzufahren',
    retry: 'Erneut versuchen',
    segmentation: 'Segmentierung',
    copiedToClipboard: 'In die Zwischenablage kopiert!',
    failedToCopy: 'Kopieren in die Zwischenablage fehlgeschlagen',
    confirm: 'Bestätigen',
    editor: {
      error: 'Fehler',
      success: 'Erfolg',
      edit: 'Bearbeiten',
      create: 'Erstellen',
    },
  },
  auth: {
    signIn: 'Anmelden',
    signUp: 'Registrieren',
    signOut: 'Abmelden',
    signingIn: 'Anmeldung läuft...',
    email: 'E-Mail',
    password: 'Passwort',
    forgotPassword: 'Passwort vergessen?',
    resetPassword: 'Passwort zurücksetzen',
    dontHaveAccount: 'Noch kein Konto?',
    alreadyHaveAccount: 'Bereits ein Konto?',
    createAccount: 'Konto erstellen',
    signInWithGoogle: 'Mit Google anmelden',
    signInWithGithub: 'Mit GitHub anmelden',
    or: 'oder',
    signInTitle: 'Anmelden',
    signInDescription: 'Melden Sie sich bei Ihrem Konto an',
    noAccount: 'Noch kein Konto?',
    emailAddressLabel: 'E-Mail-Adresse',
    passwordLabel: 'Passwort',
    currentPasswordLabel: 'Aktuelles Passwort',
    newPasswordLabel: 'Neues Passwort',
    confirmPasswordLabel: 'Passwort bestätigen',
    rememberMe: 'Angemeldet bleiben',
    emailRequired: 'E-Mail ist erforderlich',
    passwordRequired: 'Passwort ist erforderlich',
    alreadyLoggedInTitle: 'Sie sind bereits angemeldet',
    alreadyLoggedInMessage: 'Sie sind bereits bei Ihrem Konto angemeldet',
    goToDashboardLink: 'Zum Dashboard',
    invalidEmail: 'Ungültige E-Mail-Adresse',
    passwordTooShort: 'Passwort muss mindestens 6 Zeichen lang sein',
    passwordsDontMatch: 'Passwörter stimmen nicht überein',
    invalidCredentials: 'Ungültige E-Mail oder Passwort',
    accountCreated: 'Konto erfolgreich erstellt',
    resetLinkSent: 'Link zum Zurücksetzen des Passworts an Ihre E-Mail gesendet',
    resetSuccess: 'Passwort erfolgreich zurückgesetzt',
    signInSuccess: 'Erfolgreich angemeldet',
    signOutSuccess: 'Erfolgreich abgemeldet',
    sessionExpired: 'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.',
    unauthorized: 'Sie sind nicht berechtigt, auf diese Ressource zuzugreifen',
    verifyEmail: 'Bitte verifizieren Sie Ihre E-Mail-Adresse',
    verificationLinkSent: 'Verifizierungslink an Ihre E-Mail gesendet',
    verificationSuccess: 'E-Mail erfolgreich verifiziert',
    resendVerification: 'Verifizierungs-E-Mail erneut senden',
    requestAccess: 'Zugang anfordern',
    termsAndPrivacy: 'Mit der Registrierung stimmen Sie unseren Nutzungsbedingungen und Datenschutzrichtlinien zu.',
    forgotPasswordLink: 'Passwort vergessen?',
    passwordChanged: 'Passwort erfolgreich geändert',
    currentPasswordIncorrect: 'Aktuelles Passwort ist falsch',
    registerTitle: 'Konto erstellen',
    registerDescription: 'Registrieren Sie sich für ein neues Konto',
    registerSuccess: 'Registrierung erfolgreich! Sie können sich jetzt anmelden.',
    emailPlaceholder: 'Geben Sie Ihre E-Mail ein',
    passwordPlaceholder: 'Geben Sie Ihr Passwort ein',
    firstNamePlaceholder: 'z.B. Max',
    lastNamePlaceholder: 'z.B. Mustermann',
    passwordConfirmPlaceholder: 'Bestätigen Sie Ihr Passwort',
    signUpTitle: 'Konto erstellen',
    signUpDescription: 'Registrieren Sie sich für ein neues Konto',
    enterInfoCreateAccount: 'Geben Sie Ihre Informationen ein, um ein Konto zu erstellen',
    creatingAccount: 'Konto wird erstellt...',
    emailAlreadyExists:
      'Diese E-Mail ist bereits registriert. Bitte verwenden Sie eine andere E-Mail oder melden Sie sich an.',
    emailHasPendingRequest:
      'Diese E-Mail hat bereits eine ausstehende Zugangsanfrage. Bitte warten Sie auf die Genehmigung.',
    signUpSuccess: 'Erfolgreich registriert!',
    signUpSuccessEmail:
      'Registrierung erfolgreich! Bitte überprüfen Sie Ihre E-Mails oder warten Sie auf die Genehmigung durch den Administrator.',
    signUpFailed: 'Registrierung fehlgeschlagen. Bitte versuchen Sie es erneut.',
    alreadyHaveAccess: 'Bereits Zugang?',
    forgotPasswordTitle: 'Passwort zurücksetzen',
    checkYourEmail: 'Überprüfen Sie Ihre E-Mail für ein neues Passwort',
    enterEmailForReset: 'Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen ein neues Passwort',
    passwordResetLinkSent: 'Falls ein Konto für diese E-Mail existiert, wurde ein neues Passwort gesendet',
    passwordResetFailed: 'Senden des neuen Passworts fehlgeschlagen. Bitte versuchen Sie es erneut.',
    enterEmail: 'Bitte geben Sie Ihre E-Mail-Adresse ein',
    sendingResetLink: 'Neues Passwort wird gesendet...',
    sendResetLink: 'Neues Passwort senden',
    backToSignIn: 'Zurück zur Anmeldung',
    accountLocked: 'Ihr Konto wurde gesperrt. Bitte kontaktieren Sie den Support.',
    fillAllFields: 'Bitte füllen Sie alle erforderlichen Felder aus',
    serverError: 'Serverfehler. Bitte versuchen Sie es später erneut.',
    signInError: 'Fehler bei der Anmeldung',
    signInFailed: 'Anmeldung fehlgeschlagen. Bitte überprüfen Sie Ihre Anmeldedaten.',
  },
  requestAccess: {
    and: 'und',
    title: 'Zugang zur Sphäroid-Segmentierungsplattform anfordern',
    description:
      'Füllen Sie das folgende Formular aus, um Zugang zu unserer Plattform anzufordern. Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren.',
    emailLabel: 'Ihre E-Mail-Adresse',
    nameLabel: 'Ihr Name',
    institutionLabel: 'Institution/Unternehmen',
    reasonLabel: 'Grund für den Zugang',
    submitRequest: 'Anfrage senden',
    requestReceived: 'Anfrage erhalten',
    thankYou: 'Vielen Dank für Ihr Interesse',
    weWillContact: 'Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren',
    submitSuccess: 'Anfrage erfolgreich gesendet!',
    emailPlaceholder: 'Geben Sie Ihre E-Mail-Adresse ein',
    namePlaceholder: 'Geben Sie Ihren vollständigen Namen ein',
    institutionPlaceholder: 'Geben Sie den Namen Ihrer Institution oder Ihres Unternehmens ein',
    reasonPlaceholder: 'Bitte beschreiben Sie, wie Sie die Plattform nutzen möchten',
    fillRequired: 'Bitte füllen Sie alle erforderlichen Felder aus',
    submittingRequest: 'Anfrage wird gesendet...',
    submitError: 'Senden der Anfrage fehlgeschlagen',
    alreadyPending: 'Eine Zugangsanfrage für diese E-Mail ist bereits ausstehend',
    agreeToTerms: 'Mit dem Absenden dieser Anfrage stimmen Sie unseren',
  },
  requestAccessForm: {
    title: 'Zugang zur Sphäroid-Segmentierungsplattform anfordern',
    description:
      'Füllen Sie das folgende Formular aus, um Zugang zu unserer Plattform anzufordern. Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren.',
    emailLabel: 'Ihre E-Mail-Adresse',
    nameLabel: 'Ihr Name',
    institutionLabel: 'Institution/Unternehmen',
    reasonLabel: 'Grund für den Zugang',
    submitButton: 'Anfrage senden',
    signInPrompt: 'Bereits ein Konto?',
    signInLink: 'Anmelden',
    thankYouTitle: 'Vielen Dank für Ihr Interesse',
    weWillContact: 'Wir werden Ihre Anfrage prüfen und Sie bald kontaktieren',
    agreeToTerms: 'Mit dem Absenden dieser Anfrage stimmen Sie unseren',
    and: 'und',
  },
  documentation: {
    tag: 'Benutzerhandbuch',
    title: 'SpheroSeg Dokumentation',
    subtitle: 'Erfahren Sie, wie Sie die Sphäroid-Segmentierungsplattform effektiv nutzen.',
    sidebar: {
      title: 'Abschnitte',
      introduction: 'Einführung',
      gettingStarted: 'Erste Schritte',
      uploadingImages: 'Bilder hochladen',
      segmentationProcess: 'Segmentierungsprozess',
      apiReference: 'API-Referenz',
    },
    introduction: {
      title: 'Einführung',
      imageAlt: 'Illustration des Sphäroid-Analyse-Workflows',
      whatIs: {
        title: 'Was ist SpheroSeg?',
        paragraph1:
          'SpheroSeg ist eine moderne Plattform für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern. Unser Tool bietet Forschern präzise Erkennungs- und Analysefähigkeiten.',
        paragraph2:
          'Es nutzt fortschrittliche KI-Algorithmen basierend auf Deep Learning, um Sphäroide in Ihren Bildern automatisch mit hoher Genauigkeit und Konsistenz zu identifizieren und zu segmentieren.',
        paragraph3:
          'Diese Dokumentation führt Sie durch alle Aspekte der Plattformnutzung, von den ersten Schritten bis zu erweiterten Funktionen und API-Integration.',
      },
    },
    gettingStarted: {
      title: 'Erste Schritte',
      accountCreation: {
        title: 'Kontoerstellung',
        paragraph1:
          'Um SpheroSeg zu nutzen, müssen Sie ein Konto erstellen. Dies ermöglicht es uns, Ihre Projekte und Bilder sicher zu speichern.',
        step1Prefix: 'Besuchen Sie die',
        step1Link: 'Registrierungsseite',
        step2: 'Geben Sie Ihre institutionelle E-Mail-Adresse ein und erstellen Sie ein Passwort',
        step3: 'Vervollständigen Sie Ihr Profil mit Ihrem Namen und Ihrer Institution',
        step4: 'Verifizieren Sie Ihre E-Mail-Adresse über den Link in Ihrem Posteingang',
      },
      creatingProject: {
        title: 'Ihr erstes Projekt erstellen',
        paragraph1:
          'Projekte helfen Ihnen, Ihre Arbeit zu organisieren. Jedes Projekt kann mehrere Bilder und deren entsprechende Segmentierungsergebnisse enthalten.',
        step1: 'Klicken Sie auf Ihrem Dashboard auf "Neues Projekt"',
        step2: 'Geben Sie einen Projektnamen und eine Beschreibung ein',
        step3: 'Wählen Sie den Projekttyp (Standard: Sphäroid-Analyse)',
        step4: 'Klicken Sie auf "Projekt erstellen", um fortzufahren',
      },
    },
    uploadingImages: {
      title: 'Bilder hochladen',
      paragraph1:
        'SpheroSeg unterstützt verschiedene Bildformate, die häufig in der Mikroskopie verwendet werden, einschließlich TIFF, PNG und JPEG.',
      methods: {
        title: 'Upload-Methoden',
        paragraph1: 'Es gibt mehrere Möglichkeiten, Bilder hochzuladen:',
        step1: 'Ziehen Sie Dateien direkt in den Upload-Bereich',
        step2: 'Klicken Sie auf den Upload-Bereich, um Dateien von Ihrem Computer auszuwählen',
        step3: 'Laden Sie mehrere Bilder gleichzeitig hoch',
      },
      note: {
        prefix: 'Hinweis:',
        text: 'Für optimale Ergebnisse stellen Sie sicher, dass Ihre Mikroskopiebilder einen guten Kontrast zwischen Sphäroid und Hintergrund aufweisen.',
      },
    },
    segmentationProcess: {
      title: 'Segmentierungsprozess',
      paragraph1:
        'Der Segmentierungsprozess identifiziert die Grenzen von Sphäroiden in Ihren Bildern und ermöglicht eine präzise Analyse ihrer Morphologie.',
      automatic: {
        title: 'Automatische Segmentierung',
        paragraph1:
          'Unsere KI-gestützte automatische Segmentierung kann Sphäroidgrenzen mit hoher Genauigkeit erkennen:',
        step1: 'Wählen Sie ein Bild aus Ihrem Projekt aus',
        step2: 'Klicken Sie auf "Auto-Segmentieren", um den Prozess zu starten',
        step3: 'Das System verarbeitet das Bild und zeigt die erkannten Grenzen an',
        step4: 'Überprüfen Sie die Ergebnisse im Segmentierungs-Editor',
      },
      manual: {
        title: 'Manuelle Anpassungen',
        paragraph1:
          'Manchmal erfordert die automatische Segmentierung eine Verfeinerung. Unser Editor bietet Werkzeuge für:',
        step1: 'Hinzufügen oder Entfernen von Eckpunkten entlang der Grenze',
        step2: 'Anpassen von Eckpunktpositionen für genauere Grenzen',
        step3: 'Teilen oder Zusammenführen von Regionen',
        step4: 'Hinzufügen oder Entfernen von Löchern innerhalb von Sphäroiden',
      },
    },
    apiReference: {
      title: 'API-Referenz',
      paragraph1:
        'SpheroSeg bietet eine RESTful-API für den programmatischen Zugriff auf die Funktionen der Plattform. Dies ist ideal für die Integration in Ihre bestehenden Workflows oder die Stapelverarbeitung.',
      endpoint1Desc: 'Ruft eine Liste aller Ihrer Projekte ab',
      endpoint2Desc: 'Ruft alle Bilder innerhalb eines bestimmten Projekts ab',
      endpoint3Desc: 'Initiiert die Segmentierung für ein bestimmtes Bild',
      contactPrefix:
        'Für die vollständige API-Dokumentation und Authentifizierungsdetails kontaktieren Sie uns bitte unter',
    },
    backToHome: 'Zurück zur Startseite',
    backToTop: 'Zurück nach oben',
  },
  hero: {
    platformTag: 'Fortschrittliche Sphäroid-Segmentierungsplattform',
    title: 'KI-gestützte Zellanalyse für die biomedizinische Forschung',
    subtitle:
      'Verbessern Sie Ihre mikroskopische Zellbildanalyse mit unserer hochmodernen Sphäroid-Segmentierungsplattform. Entwickelt für Forscher, die Präzision und Effizienz suchen.',
    getStartedButton: 'Jetzt starten',
    learnMoreButton: 'Mehr erfahren',
    imageAlt1: 'Sphäroid-Mikroskopiebild',
    imageAlt2: 'Sphäroid-Mikroskopiebild mit Analyse',
    welcomeTitle: 'Willkommen bei SpheroSeg',
    welcomeSubtitle: 'Fortschrittliche Plattform für Zellsphäroid-Segmentierung und -Analyse',
    welcomeDescription:
      'Unsere Plattform kombiniert modernste künstliche Intelligenz-Algorithmen mit einer intuitiven Benutzeroberfläche für präzise Erkennung und Analyse von Zellsphäroiden in mikroskopischen Bildern.',
    featuresTitle: 'Leistungsstarke Funktionen',
    featuresSubtitle: 'Fortschrittliche Werkzeuge für die biomedizinische Forschung',
    featureAiSegmentation: 'Erweiterte Segmentierung',
    featureAiSegmentationDesc: 'Präzise Sphäroiderkennung mit Grenzanalyse für genaue Zellmessungen.',
    featureEditing: 'KI-gestützte Analyse',
    featureEditingDesc: 'Nutzen Sie Deep-Learning-Algorithmen für automatisierte Erkennung und Zellklassifizierung.',
    featureAnalytics: 'Einfaches Hochladen',
    featureAnalyticsDesc: 'Ziehen Sie Ihre Mikroskopiebilder per Drag & Drop für sofortige Verarbeitung und Analyse.',
    featureExport: 'Statistische Einblicke',
    featureExportDesc: 'Umfassende Metriken und Visualisierungen zur Extraktion aussagekräftiger Datenmuster.',
    ctaTitle: 'Bereit, Ihren Zellanalyse-Workflow zu transformieren?',
    ctaSubtitle:
      'Schließen Sie sich führenden Forschern an, die unsere Plattform bereits nutzen, um ihre Entdeckungen zu beschleunigen.',
    ctaButton: 'Konto erstellen',
  },
  navbar: {
    home: 'Startseite',
    features: 'Funktionen',
    documentation: 'Dokumentation',
    terms: 'Bedingungen',
    privacy: 'Datenschutz',
    login: 'Anmelden',
    requestAccess: 'Zugang anfordern',
    openMobileMenu: 'Mobiles Menü öffnen',
    closeMobileMenu: 'Mobiles Menü schließen',
  },
  navigation: {
    home: 'Startseite',
    projects: 'Projekte',
    settings: 'Einstellungen',
    profile: 'Profil',
    dashboard: 'Dashboard',
    back: 'Zurück',
  },
  dashboard: {
    manageProjects: 'Verwalten und organisieren Sie Ihre Forschungsprojekte',
    viewMode: {
      grid: 'Rasteransicht',
      list: 'Listenansicht',
    },
    sort: {
      name: 'Name',
      updatedAt: 'Zuletzt aktualisiert',
      segmentationStatus: 'Status',
    },
    search: 'Projekte suchen...',
    searchImagesPlaceholder: 'Bilder suchen...',
    noProjects: 'Keine Projekte gefunden',
    noImagesDescription: 'Keine Bilder entsprechen Ihren Suchkriterien',
    createFirst: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    createNew: 'Neues Projekt erstellen',
    lastChange: 'Letzte Änderung',
    statsOverview: 'Statistikübersicht',
    totalProjects: 'Projekte gesamt',
    activeProjects: 'Aktive Projekte',
    totalImages: 'Bilder gesamt',
    totalAnalyses: 'Analysen gesamt',
    lastUpdated: 'Zuletzt aktualisiert',
    noProjectsDescription: 'Sie haben noch keine Projekte erstellt. Erstellen Sie Ihr erstes Projekt, um zu beginnen.',
    searchProjectsPlaceholder: 'Projekte nach Namen suchen...',
    sortBy: 'Sortieren nach',
    name: 'Name',
    completed: 'Abgeschlossen',
    processing: 'In Bearbeitung',
    pending: 'Ausstehend',
    failed: 'Fehlgeschlagen',
    selectImagesButton: 'Bilder auswählen',
  },
  projects: {
    title: 'Projekte',
    description: 'Verwalten Sie Ihre Forschungsprojekte',
    createNew: 'Neues Projekt erstellen',
    createProject: 'Projekt erstellen',
    createProjectDesc: 'Erstellen Sie ein neues Projekt, um mit Bildern und Segmentierung zu arbeiten.',
    projectName: 'Projektname',
    projectDescription: 'Projektbeschreibung',
    projectNamePlaceholder: 'Projektnamen eingeben',
    projectDescriptionPlaceholder: 'Projektbeschreibung eingeben',
    projectCreated: 'Projekt erfolgreich erstellt',
    projectCreationFailed: 'Projekterstellung fehlgeschlagen',
    projectDeleted: 'Projekt erfolgreich gelöscht',
    deleteSuccess: 'Projekt erfolgreich gelöscht',
    deleteFailed: 'Projektlöschung fehlgeschlagen',
    deleting: 'Projekt wird gelöscht...',
    notFound: 'Projekt nicht gefunden. Es wurde möglicherweise bereits gelöscht.',
    missingId: 'Projekt kann nicht gelöscht werden: fehlende Projektkennung',
    projectDeletionFailed: 'Projektlöschung fehlgeschlagen',
    confirmDelete: 'Sind Sie sicher, dass Sie dieses Projekt löschen möchten?',
    confirmDeleteDescription:
      'Diese Aktion kann nicht rückgängig gemacht werden. Alle mit diesem Projekt verbundenen Daten werden dauerhaft gelöscht.',
    delete: 'Löschen',
    deleteProject: 'Projekt löschen',
    deleteProjectDescription:
      'Diese Aktion kann nicht rückgängig gemacht werden. Das Projekt und alle zugehörigen Daten werden dauerhaft gelöscht.',
    deleteWarning: 'Sie sind dabei, das folgende Projekt zu löschen:',
    typeToConfirm: 'Geben Sie den Projektnamen ein, um zu bestätigen',
    confirmDeleteError: 'Bitte geben Sie den Projektnamen genau ein, um zu bestätigen',
    editProject: 'Projekt bearbeiten',
    viewProject: 'Projekt anzeigen',
    projectUpdated: 'Projekt erfolgreich aktualisiert',
    projectUpdateFailed: 'Projektaktualisierung fehlgeschlagen',
    noProjects: 'Keine Projekte gefunden',
    createFirstProject: 'Erstellen Sie Ihr erstes Projekt, um zu beginnen',
    searchProjects: 'Projekte suchen...',
    filterProjects: 'Projekte filtern',
    sortProjects: 'Projekte sortieren',
    projectNameRequired: 'Projektname ist erforderlich',
    loginRequired: 'Sie müssen angemeldet sein, um ein Projekt zu erstellen',
    createdAt: 'Erstellt',
    updatedAt: 'Zuletzt aktualisiert',
    imageCount: 'Bilder',
    status: 'Status',
    actions: 'Aktionen',
    loading: 'Projekte werden geladen...',
    error: 'Fehler beim Laden der Projekte',
    retry: 'Wiederholen',
    duplicating: 'Projekt wird dupliziert...',
    duplicate: 'Duplizieren',
    duplicateSuccess: 'Projekt erfolgreich dupliziert',
    duplicateFailed: 'Projektduplizierung fehlgeschlagen',
    duplicateTitle: 'Projekt duplizieren',
    duplicateProject: 'Projekt duplizieren',
    duplicateProjectDescription:
      'Erstellen Sie eine Kopie dieses Projekts einschließlich aller Bilder. Sie können die Optionen unten anpassen.',
    duplicateCancelled: 'Projektduplizierung abgebrochen',
    duplicatingProject: 'Projekt wird dupliziert',
    duplicatingProjectDescription: 'Ihr Projekt wird dupliziert. Dies kann einen Moment dauern.',
    duplicateProgress: 'Duplizierungsfortschritt',
    duplicationComplete: 'Projektduplizierung abgeschlossen',
    duplicationTaskFetchError: 'Fehler beim Abrufen der Aufgabendaten',
    duplicationCancelError: 'Fehler beim Abbrechen der Duplizierung',
    duplicateProgressDescription:
      'Ihr Projekt wird dupliziert. Dieser Vorgang kann bei großen Projekten einige Zeit dauern.',
    duplicationPending: 'Ausstehend',
    duplicationProcessing: 'In Bearbeitung',
    duplicationCompleted: 'Abgeschlossen',
    duplicationFailed: 'Fehlgeschlagen',
    duplicationCancelled: 'Abgebrochen',
    duplicationCancellationFailed: 'Abbruch der Duplizierung fehlgeschlagen',
    duplicationSuccessMessage: 'Projekt erfolgreich dupliziert! Sie können jetzt auf das neue Projekt zugreifen.',
    copySegmentations: 'Segmentierungsergebnisse kopieren',
    resetImageStatus: 'Bildverarbeitungsstatus zurücksetzen',
    newProjectTitle: 'Neuer Projekttitel',
    itemsProcessed: 'Elemente verarbeitet',
    items: 'Elemente',
    unknownProject: 'Unbekanntes Projekt',
    activeTasks: 'Aktiv',
    allTasks: 'Alle',
    noActiveDuplications: 'Keine aktiven Duplizierungen',
    noDuplications: 'Keine Duplizierungsaufgaben gefunden',
    untitledProject: 'Unbenanntes Projekt',
    exportProject: 'Projekt exportieren',
    share: 'Teilen',
    export: 'Exportieren',
    archived: 'Archiviert',
    completed: 'Abgeschlossen',
    draft: 'Entwurf',
    active: 'Aktiv',
  },
  projectToolbar: {
    selectImages: 'Bilder auswählen',
    cancelSelection: 'Auswahl abbrechen',
    export: 'Exportieren',
    uploadImages: 'Bilder hochladen',
  },
  statsOverview: {
    title: 'Dashboard-Übersicht',
    totalProjects: 'Projekte gesamt',
    totalImages: 'Bilder gesamt',
    completedSegmentations: 'Abgeschlossene Segmentierungen',
    storageUsed: 'Verwendeter Speicher',
    recentActivity: 'Aktuelle Aktivitäten',
    moreStats: 'Detaillierte Statistiken anzeigen',
    completion: 'Abschlussrate',
    vsLastMonth: 'vs. letzten Monat',
    thisMonth: 'Diesen Monat',
    lastMonth: 'Letzten Monat',
    projectsCreated: 'Projekte erstellt',
    imagesUploaded: 'Bilder hochgeladen',
    fetchError: 'Statistiken konnten nicht geladen werden',
    storageLimit: 'Speicherlimit',
    activityTitle: 'Aktuelle Aktivitäten',
    noActivity: 'Keine aktuellen Aktivitäten',
    hide: 'Ausblenden',
    activityTypes: {
      project_created: 'Projekt erstellt',
      image_uploaded: 'Bild hochgeladen',
      segmentation_completed: 'Segmentierung abgeschlossen',
    },
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FJFI ČVUT in Prag',
    description: 'Fortschrittliche Plattform für Sphäroid-Segmentierung und -Analyse',
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FJFI ČVUT in Prag',
    resourcesTitle: 'Ressourcen',
    documentationLink: 'Dokumentation',
    featuresLink: 'Funktionen',
    tutorialsLink: 'Tutorials',
    researchLink: 'Forschung',
    legalTitle: 'Rechtliche Informationen',
    termsLink: 'Nutzungsbedingungen',
    privacyLink: 'Datenschutzrichtlinie',
    contactUsLink: 'Kontakt',
    informationTitle: 'Informationen',
    contactTitle: 'Kontakt',
    copyrightNotice: 'SpheroSeg. Alle Rechte vorbehalten.',
    madeWith: 'Erstellt mit',
    by: 'von',
    requestAccessLink: 'Zugang anfordern',
    githubRepository: 'GitHub-Repository',
    contactEmail: 'Kontakt-E-Mail',
  },
  features: {
    tag: 'Funktionen',
    title: 'Entdecken Sie unsere Plattformfähigkeiten',
    subtitle: 'Fortschrittliche Werkzeuge für die biomedizinische Forschung',
    cards: {
      segmentation: {
        title: 'Erweiterte Segmentierung',
        description: 'Präzise Sphäroiderkennung mit Grenzanalyse für genaue Zellmessungen',
      },
      aiAnalysis: {
        title: 'KI-gestützte Analyse',
        description: 'Nutzen Sie Deep-Learning-Algorithmen für automatisierte Zellerkennung und -klassifizierung',
      },
      uploads: {
        title: 'Einfaches Hochladen',
        description: 'Ziehen Sie Ihre Mikroskopiebilder per Drag & Drop für sofortige Verarbeitung und Analyse',
      },
      insights: {
        title: 'Statistische Einblicke',
        description: 'Umfassende Metriken und Visualisierungen zur Extraktion aussagekräftiger Datenmuster',
      },
      collaboration: {
        title: 'Team-Zusammenarbeit',
        description: 'Teilen Sie Projekte und Ergebnisse mit Kollegen für effizientere Forschung',
      },
      pipeline: {
        title: 'Automatisierte Pipeline',
        description: 'Optimieren Sie Ihren Workflow mit unseren Stapelverarbeitungstools',
      },
    },
  },
  index: {
    about: {
      tag: 'Über die Plattform',
      title: 'Was ist SpheroSeg?',
      imageAlt: 'Sphäroid-Segmentierungsbeispiel',
      paragraph1:
        'SpheroSeg ist eine fortschrittliche Plattform, die speziell für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern entwickelt wurde.',
      paragraph2:
        'Unser Tool kombiniert modernste Algorithmen künstlicher Intelligenz mit einer intuitiven Benutzeroberfläche, um Forschern präzise Sphäroidgrenzenerkennung und Analysefähigkeiten zu bieten.',
      paragraph3:
        'Die Plattform wurde von Michal Průšek von der FJFI ČVUT in Prag unter der Leitung von Adam Novozámský vom UTIA CAS in Zusammenarbeit mit Forschern des Instituts für Biochemie und Mikrobiologie der VŠCHT Prag entwickelt.',
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: 'Bereit, Ihre Forschung zu transformieren?',
      subtitle:
        'Beginnen Sie noch heute mit SpheroSeg und entdecken Sie neue Möglichkeiten in der Zellsphäroid-Analyse',
      boxTitle: 'Kostenloses Konto erstellen',
      boxText:
        'Erhalten Sie Zugriff auf alle Plattformfunktionen und beginnen Sie mit der Analyse Ihrer Mikroskopiebilder',
      button: 'Konto erstellen',
    },
  },
  tools: {
    zoomIn: 'Vergrößern',
    zoomOut: 'Verkleinern',
    resetView: 'Ansicht zurücksetzen',
    createPolygon: 'Neues Polygon erstellen',
    exitPolygonCreation: 'Polygon-Erstellungsmodus verlassen',
    splitPolygon: 'Polygon in zwei teilen',
    exitSlicingMode: 'Teilungsmodus verlassen',
    addPoints: 'Punkte zum Polygon hinzufügen',
    exitPointAddingMode: 'Punkt-Hinzufügen-Modus verlassen',
    undo: 'Rückgängig',
    redo: 'Wiederholen',
    save: 'Speichern',
    resegment: 'Neu segmentieren',
    title: 'Werkzeuge',
  },
  settings: {
    title: 'Einstellungen',
    pageTitle: 'Einstellungen',
    profile: 'Profil',
    account: 'Konto',
    appearance: 'Darstellung',
    profileSettings: 'Profileinstellungen',
    accountSettings: 'Kontoeinstellungen',
    securitySettings: 'Sicherheitseinstellungen',
    preferenceSettings: 'Präferenzeinstellungen',
    selectLanguage: 'Sprache wählen',
    selectTheme: 'Design wählen',
    updateProfile: 'Profil aktualisieren',
    changePassword: 'Passwort ändern',
    deleteAccount: 'Konto löschen',
    savedChanges: 'Änderungen erfolgreich gespeichert',
    saveChanges: 'Änderungen speichern',
    profileUpdated: 'Profil erfolgreich aktualisiert',
    languageSettings: 'Spracheinstellungen',
    themeSettings: 'Design-Einstellungen',
    privacySettings: 'Datenschutzeinstellungen',
    exportData: 'Daten exportieren',
    importData: 'Daten importieren',
    uploadAvatar: 'Profilbild hochladen',
    removeAvatar: 'Profilbild entfernen',
    twoFactorAuth: 'Zwei-Faktor-Authentifizierung',
    emailNotifications: 'E-Mail-Benachrichtigungen',
    pushNotifications: 'Push-Benachrichtigungen',
    weeklyDigest: 'Wöchentliche Zusammenfassung',
    monthlyReport: 'Monatsbericht',
    displaySettings: 'Anzeigeeinstellungen',
    accessibilitySettings: 'Barrierefreiheitseinstellungen',
    advancedSettings: 'Erweiterte Einstellungen',
    useBrowserLanguage: 'Browsersprache verwenden',
    language: 'Sprache',
    theme: 'Design',
    light: 'Hell',
    dark: 'Dunkel',
    system: 'System',
    languageUpdated: 'Sprache erfolgreich aktualisiert',
    themeUpdated: 'Design erfolgreich aktualisiert',
    toggleTheme: 'Design wechseln',
    languageDescription: 'Wählen Sie Ihre bevorzugte Sprache',
    themeDescription: 'Wählen Sie Ihr bevorzugtes Design',
    profileLoadError: 'Profil konnte nicht geladen werden',
    appearanceDescription: 'Passen Sie das Erscheinungsbild der Anwendung an',
    personal: 'Persönliche Informationen',
    fullName: 'Vollständiger Name',
    organization: 'Organisation',
    department: 'Abteilung',
    publicProfile: 'Öffentliches Profil',
    makeProfileVisible: 'Mein Profil für andere Forscher sichtbar machen',
    passwordSettings: 'Passworteinstellungen',
    currentPassword: 'Aktuelles Passwort',
    newPassword: 'Neues Passwort',
    confirmNewPassword: 'Neues Passwort bestätigen',
    dangerZone: 'Gefahrenzone',
    deleteAccountWarning:
      'Sobald Sie Ihr Konto löschen, gibt es kein Zurück mehr. Alle Ihre Daten werden dauerhaft gelöscht.',
    savingChanges: 'Änderungen werden gespeichert...',
    savePreferences: 'Einstellungen speichern',
    usernameTaken: 'Dieser Benutzername ist bereits vergeben',
    deleteAccountDescription: 'Diese Aktion ist unwiderruflich. Alle Ihre Daten werden dauerhaft gelöscht.',
    confirmUsername: 'Bestätigen Sie Ihre E-Mail',
    password: 'Passwort',
    enterPassword: 'Geben Sie Ihr Passwort ein',
    passwordChangeError: 'Fehler beim Ändern des Passworts',
    passwordChangeSuccess: 'Passwort erfolgreich geändert',
    passwordsDoNotMatch: 'Passwörter stimmen nicht überein',
    accountDeleteSuccess: 'Konto erfolgreich gelöscht',
    accountDeleteError: 'Fehler beim Löschen des Kontos',
    passwordChanged: 'Passwort geändert',
    changingPassword: 'Passwort wird geändert...',
    confirmPasswordLabel: 'Passwort bestätigen',
    changePasswordDescription: 'Ändern Sie Ihr Passwort, um Ihr Konto sicher zu halten',
    dangerZoneDescription: 'Diese Aktionen sind unwiderruflich und werden Ihre Daten dauerhaft entfernen',
    deletingAccount: 'Konto wird gelöscht...',
    deleteAccountError: 'Fehler beim Löschen des Kontos',
  },
  accessibility: {
    skipToContent: 'Zum Hauptinhalt springen',
  },
  profile: {
    title: 'Titel',
    about: 'Über',
    activity: 'Aktivität',
    projects: 'Projekte',
    recentProjects: 'Aktuelle Projekte',
    recentAnalyses: 'Aktuelle Analysen',
    accountDetails: 'Kontodetails',
    accountType: 'Kontotyp',
    joinDate: 'Beitrittsdatum',
    lastActive: 'Zuletzt aktiv',
    projectsCreated: 'Erstellte Projekte',
    imagesUploaded: 'Hochgeladene Bilder',
    segmentationsCompleted: 'Abgeschlossene Segmentierungen',
    pageTitle: 'Benutzerprofil',
    editProfile: 'Profil bearbeiten',
    joined: 'Beigetreten',
    statistics: 'Statistiken',
    images: 'Bilder',
    analyses: 'Analysen',
    storageUsed: 'Verwendeter Speicher',
    recentActivity: 'Aktuelle Aktivitäten',
    noRecentActivity: 'Keine aktuellen Aktivitäten',
    fetchError: 'Profildaten konnten nicht geladen werden',
    aboutMe: 'Über mich',
    noBio: 'Keine Biografie angegeben',
    avatarHelp: 'Klicken Sie auf das Kamera-Symbol, um ein Profilbild hochzuladen',
    avatarImageOnly: 'Bitte wählen Sie eine Bilddatei aus',
    avatarTooLarge: 'Bild muss kleiner als 5MB sein',
    avatarUpdated: 'Profilbild aktualisiert',
    avatarUploadError: 'Profilbild konnte nicht hochgeladen werden',
    avatarRemoved: 'Profilbild entfernt',
    avatarRemoveError: 'Profilbild konnte nicht entfernt werden',
    cropAvatarDescription: 'Passen Sie den Zuschneidebereich an, um Ihr Profilbild festzulegen',
    description: 'Aktualisieren Sie Ihre persönlichen Informationen und Ihr Profilbild',
    saveButton: 'Profil speichern',
    username: 'Benutzername',
    usernamePlaceholder: 'Geben Sie Ihren Benutzernamen ein',
    fullName: 'Vollständiger Name',
    fullNamePlaceholder: 'Geben Sie Ihren vollständigen Namen ein',
    titlePlaceholder: 'z.B. Forscher, Professor',
    organization: 'Organisation',
    organizationPlaceholder: 'Geben Sie Ihre Organisation oder Institution ein',
    bio: 'Biografie',
    bioPlaceholder: 'Erzählen Sie uns etwas über sich',
    bioDescription: 'Eine kurze Beschreibung über Sie, die auf Ihrem Profil sichtbar ist',
    location: 'Standort',
    locationPlaceholder: 'z.B. Prag, Tschechische Republik',
    uploadAvatar: 'Profilbild hochladen',
    removeAvatar: 'Profilbild entfernen',
    cropAvatar: 'Profilbild zuschneiden',
    activityDescription: 'Systemaktivität',
    email: 'E-Mail',
    notProvided: 'Nicht angegeben',
  },
  termsPage: {
    title: 'Nutzungsbedingungen',
    acceptance: {
      title: '1. Annahme der Bedingungen',
      paragraph1:
        'Durch den Zugriff auf oder die Nutzung von SpheroSeg erklären Sie sich damit einverstanden, an diese Nutzungsbedingungen und alle geltenden Gesetze und Vorschriften gebunden zu sein. Wenn Sie mit diesen Bedingungen nicht einverstanden sind, ist Ihnen die Nutzung dieses Dienstes untersagt.',
    },
    useLicense: {
      title: '2. Nutzungslizenz',
      paragraph1:
        'Es wird die Erlaubnis erteilt, SpheroSeg vorübergehend nur für persönliche, nicht-kommerzielle oder akademische Forschungszwecke zu nutzen. Dies ist die Gewährung einer Lizenz, nicht die Übertragung des Eigentums.',
    },
    dataUsage: {
      title: '3. Datennutzung',
      paragraph1:
        'Alle auf SpheroSeg hochgeladenen Daten bleiben Ihr Eigentum. Wir beanspruchen kein Eigentum an Ihren Inhalten, benötigen jedoch bestimmte Berechtigungen, um den Dienst bereitzustellen.',
    },
    limitations: {
      title: '4. Einschränkungen',
      paragraph1:
        'In keinem Fall haftet SpheroSeg für Schäden, die aus der Nutzung oder der Unmöglichkeit der Nutzung der Plattform entstehen, selbst wenn wir auf die Möglichkeit solcher Schäden hingewiesen wurden.',
    },
    revisions: {
      title: '5. Überarbeitungen und Fehler',
      paragraph1:
        'Die auf SpheroSeg erscheinenden Materialien können technische, typografische oder fotografische Fehler enthalten. Wir garantieren nicht, dass die Materialien genau, vollständig oder aktuell sind.',
    },
    governingLaw: {
      title: '6. Geltendes Recht',
      paragraph1:
        'Diese Bedingungen unterliegen den Gesetzen des Landes, in dem der Dienst gehostet wird, und werden nach diesen ausgelegt. Sie unterwerfen sich unwiderruflich der ausschließlichen Gerichtsbarkeit der Gerichte an diesem Ort.',
    },
    lastUpdated: 'Zuletzt aktualisiert: 7. Januar 2025',
  },
  privacyPage: {
    title: 'Datenschutzrichtlinie',
    introduction: {
      title: '1. Einleitung',
      paragraph1:
        'Diese Datenschutzrichtlinie erklärt, wie SpheroSeg ("wir", "uns", "unser") Ihre Informationen sammelt, verwendet und teilt, wenn Sie unsere Plattform für Sphäroid-Segmentierung und -Analyse nutzen.',
    },
    dataCollection: {
      title: '2. Informationen, die wir sammeln',
      paragraph1: 'Wir sammeln Informationen, die Sie uns direkt zur Verfügung stellen, einschließlich:',
      list: [
        'Kontoinformationen (E-Mail, Name, Institution)',
        'Hochgeladene Bilder und Daten zur Segmentierung',
        'Projektmetadaten und Analyseergebnisse',
        'Nutzungsdaten und Aktivitätsprotokolle',
      ],
    },
    dataUsage: {
      title: '3. Wie wir Ihre Informationen verwenden',
      paragraph1: 'Wir verwenden die gesammelten Informationen, um:',
      list: [
        'Unsere Dienste bereitzustellen und zu warten',
        'Ihre Bildsegmentierungsanfragen zu verarbeiten',
        'Unsere Algorithmen und Dienste zu verbessern',
        'Mit Ihnen über Ihr Konto zu kommunizieren',
        'Sicherheit zu gewährleisten und Missbrauch zu verhindern',
      ],
    },
    dataStorage: {
      title: '4. Datenspeicherung und Sicherheit',
      paragraph1:
        'Wir implementieren angemessene technische und organisatorische Maßnahmen, um Ihre persönlichen Informationen vor unbefugtem Zugriff, Änderung, Offenlegung oder Zerstörung zu schützen.',
      paragraph2:
        'Ihre Daten werden auf sicheren Servern gespeichert und gemäß unserer Datenaufbewahrungsrichtlinie gelöscht.',
    },
    dataSharing: {
      title: '5. Datenweitergabe',
      paragraph1:
        'Wir verkaufen, handeln oder übertragen Ihre persönlichen Informationen nicht an Dritte ohne Ihre Zustimmung, außer wie in dieser Richtlinie beschrieben oder gesetzlich vorgeschrieben.',
    },
    userRights: {
      title: '6. Ihre Rechte',
      paragraph1: 'Sie haben das Recht:',
      list: [
        'Auf Ihre persönlichen Informationen zuzugreifen',
        'Ungenaue Informationen zu korrigieren',
        'Die Löschung Ihrer Daten zu verlangen',
        'Ihre Daten zu exportieren',
        'Der Verarbeitung Ihrer Daten zu widersprechen',
      ],
    },
    cookies: {
      title: '7. Cookies und Tracking-Technologien',
      paragraph1:
        'Wir verwenden Cookies und ähnliche Technologien, um Ihre Erfahrung zu verbessern, die Nutzung der Website zu analysieren und Inhalte zu personalisieren.',
    },
    changes: {
      title: '8. Änderungen dieser Richtlinie',
      paragraph1:
        'Wir können unsere Datenschutzrichtlinie von Zeit zu Zeit aktualisieren. Wir werden Sie über Änderungen informieren, indem wir die neue Datenschutzrichtlinie auf dieser Seite veröffentlichen.',
    },
    contact: {
      title: '9. Kontaktieren Sie uns',
      paragraph1: 'Wenn Sie Fragen zu dieser Datenschutzrichtlinie haben, kontaktieren Sie uns bitte unter:',
      email: '<EMAIL>',
    },
    lastUpdated: 'Zuletzt aktualisiert: 7. Januar 2025',
  },
  shortcuts: {
    button: 'Tastenkürzel',
    editMode: 'Zum Bearbeitungsmodus wechseln',
    sliceMode: 'Zum Teilungsmodus wechseln',
    addPointMode: 'Zum Punkte-Hinzufügen-Modus wechseln',
    holdShift: 'Shift gedrückt halten für automatisches Hinzufügen von Punkten (im Bearbeitungsmodus)',
    undo: 'Rückgängig',
    redo: 'Wiederholen',
    deletePolygon: 'Ausgewähltes Polygon löschen',
    cancel: 'Aktuelle Operation abbrechen',
    zoomIn: 'Vergrößern',
    zoomOut: 'Verkleinern',
    resetView: 'Ansicht zurücksetzen',
    title: 'Tastenkürzel',
    viewMode: 'Ansichtsmodus',
    editVerticesMode: 'Eckpunkte bearbeiten-Modus',
    addPointsMode: 'Punkte hinzufügen-Modus',
    createPolygonMode: 'Polygon erstellen-Modus',
    save: 'Speichern',
    description:
      'Diese Tastenkürzel funktionieren im Segmentierungs-Editor für schnelleres und komfortableres Arbeiten.',
  },
  imageProcessor: {
    segmentationStarted: 'Segmentierungsprozess wurde gestartet...',
    startSegmentationTooltip: 'Segmentierung starten',
    processingTooltip: 'In Bearbeitung...',
    savingTooltip: 'Speichert...',
    completedTooltip: 'Segmentierung abgeschlossen',
    retryTooltip: 'Segmentierung wiederholen',
  },
  uploader: {
    dragDrop: 'Bilder hier ablegen oder klicken, um Dateien auszuwählen',
    dropFiles: 'Dateien hier ablegen...',
    segmentAfterUploadLabel: 'Bilder sofort nach dem Upload segmentieren',
    filesToUpload: 'Hochzuladende Dateien',
    uploadBtn: 'Hochladen',
    uploadError: 'Beim Upload ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
    clickToUpload: 'Klicken, um Dateien zu durchsuchen',
    selectProjectLabel: 'Projekt auswählen',
    selectProjectPlaceholder: 'Projekt auswählen...',
    noProjectsFound: 'Keine Projekte gefunden. Erstellen Sie zuerst ein neues.',
    imageOnly: '(Nur Bilddateien)',
    uploadingImages: 'Bilder werden hochgeladen...',
    uploadComplete: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    processingImages: 'Bilder werden verarbeitet...',
    dragAndDropFiles: 'Dateien hier ablegen',
    or: 'oder',
    clickToSelect: 'Klicken, um Dateien auszuwählen',
  },
  images: {
    uploadImages: 'Bilder hochladen',
    dragDrop: 'Bilder hier ablegen',
    clickToSelect: 'oder klicken, um Dateien auszuwählen',
    acceptedFormats: 'Unterstützte Formate: JPEG, PNG, TIFF, BMP (max. 10MB)',
    uploadProgress: 'Upload-Fortschritt',
    uploadingTo: 'Hochladen zu',
    currentProject: 'Aktuelles Projekt',
    autoSegment: 'Bilder nach dem Upload automatisch segmentieren',
    uploadCompleted: 'Upload abgeschlossen',
    uploadFailed: 'Upload fehlgeschlagen',
    imagesUploaded: 'Bilder erfolgreich hochgeladen',
    imagesFailed: 'Bild-Upload fehlgeschlagen',
    viewAnalyses: 'Analysen anzeigen',
    noAnalysesYet: 'Noch keine Analysen',
    runAnalysis: 'Analyse durchführen',
    viewResults: 'Ergebnisse anzeigen',
    dropImagesHere: 'Bilder hier ablegen...',
    selectProjectFirst: 'Bitte wählen Sie zuerst ein Projekt aus',
    projectRequired: 'Sie müssen ein Projekt auswählen, bevor Sie Bilder hochladen',
    imageOnly: '(Nur Bilddateien)',
    dropFiles: 'Dateien hier ablegen...',
    filesToUpload: 'Hochzuladende Dateien ({{count}})',
    uploadBtn: '{{count}} Bilder hochladen',
    uploadError: 'Beim Upload ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
    noProjectsToUpload: 'Keine Projekte verfügbar. Erstellen Sie zuerst ein Projekt.',
    notFound: 'Projekt "{{projectName}}" nicht gefunden. Es wurde möglicherweise gelöscht.',
    errors: {
      imageOrProjectNotFound: 'Bild oder Projekt nicht gefunden.',
      failedToDeleteImage: 'Löschen des Bildes fehlgeschlagen',
      imageOrProjectNotFoundForNavigation: 'Bild oder Projekt für Navigation nicht gefunden, oder UUID fehlt.',
      imageNotFoundForClearingSegmentation: 'Bild zum Löschen der Segmentierung nicht gefunden oder UUID fehlt.',
      failedToClearSegmentation: 'Löschen der Segmentierung fehlgeschlagen',
    },
    success: {
      localImageDeleted: 'Lokales Bild erfolgreich gelöscht',
      imageDeleted: 'Bild erfolgreich gelöscht',
      segmentationCleared: 'Segmentierung erfolgreich gelöscht.',
    },
    info: {
      clearingSegmentation: 'Lösche Segmentierung für Bild {{imageName}}...',
      selectAtLeastOneImage: 'Bitte wählen Sie mindestens ein Bild aus.',
    },
  },
  export: {
    formatDescriptions: {
      COCO: 'Common Objects in Context (COCO) JSON-Format für Objekterkennung',
      YOLO: 'You Only Look Once (YOLO) Textformat für Objekterkennung',
      MASK: 'Binäre Maskenbilder für jedes segmentierte Objekt',
      POLYGONS: 'Polygonkoordinaten im JSON-Format',
      DATUMARO: 'Datumaro-Format - einheitliche Datensatzrepräsentation',
      CVAT_MASKS: 'CVAT XML-Format mit Polygon-Annotationen',
      CVAT_YAML: 'CVAT YAML-Format für Annotationsaustausch',
    },
    exportCompleted: 'Export abgeschlossen',
    exportFailed: 'Export fehlgeschlagen',
    title: 'Segmentierungsdaten exportieren',
    spheroidMetrics: 'Sphäroid-Metriken',
    visualization: 'Visualisierung',
    cocoFormat: 'COCO-Format',
    close: 'Schließen',
    metricsExported: 'Metriken erfolgreich exportiert',
    options: {
      includeMetadata: 'Metadaten einschließen',
      includeSegmentation: 'Segmentierung einschließen',
      selectExportFormat: 'Exportformat wählen',
      includeObjectMetrics: 'Objektmetriken einschließen',
      selectMetricsFormat: 'Metrikformat wählen',
      metricsFormatDescription: {
        EXCEL: 'Excel-Datei (.xlsx)',
        CSV: 'CSV-Datei (.csv)',
      },
      includeImages: 'Originalbilder einschließen',
      exportMetricsOnly: 'Nur Metriken exportieren',
      metricsRequireSegmentation: 'Der Export von Metriken erfordert eine abgeschlossene Segmentierung',
    },
    formats: {
      COCO: 'COCO JSON',
      YOLO: 'YOLO TXT',
      MASK: 'Maske (TIFF)',
      POLYGONS: 'Polygone (JSON)',
      DATUMARO: 'Datumaro',
      CVAT_MASKS: 'CVAT Masken (XML)',
      CVAT_YAML: 'CVAT YAML',
    },
    metricsFormats: {
      EXCEL: 'Excel (.xlsx)',
      CSV: 'CSV (.csv)',
    },
    selectImagesForExport: 'Bilder für Export auswählen',
    selectImagesToExport: 'Bilder zum Exportieren auswählen',
    noImagesAvailable: 'Keine Bilder verfügbar',
    backToProject: 'Zurück zum Projekt',
    exportImages: 'Bilder exportieren',
    maskExportError: 'Fehler beim Exportieren der Maske',
    maskExportStarted: 'Maskenexport gestartet',
    metricsRequireSegmentation: 'Metriken erfordern eine abgeschlossene Segmentierung',
    noImageSelectedError: 'Kein Bild für den Export ausgewählt',
  },
  metrics: {
    area: 'Fläche',
    perimeter: 'Umfang',
    circularity: 'Kreisförmigkeit',
    sphericity: 'Sphärizität',
    solidity: 'Solidität',
    compactness: 'Kompaktheit',
    convexity: 'Konvexität',
    visualization: 'Metrik-Visualisierung',
    visualizationHelp: 'Visuelle Darstellung der Metriken für alle Sphäroide in diesem Bild',
    barChart: 'Balkendiagramm',
    pieChart: 'Kreisdiagramm',
    comparisonChart: 'Vergleichsdiagramm',
    keyMetricsComparison: 'Vergleich der Hauptmetriken',
    areaDistribution: 'Flächenverteilung',
    shapeMetricsComparison: 'Vergleich der Formmetriken',
    noPolygonsFound: 'Keine Polygone für die Analyse gefunden',
  },
  imageStatus: {
    completed: 'Verarbeitet',
    processing: 'In Bearbeitung',
    pending: 'Ausstehend',
    failed: 'Fehlgeschlagen',
    noImage: 'Kein Bild',
    untitledImage: 'Unbenanntes Bild',
  },
  projectActions: {
    duplicateTooltip: 'Projekt duplizieren',
    deleteTooltip: 'Projekt löschen',
    deleteConfirmTitle: 'Sind Sie sicher?',
    deleteConfirmDesc:
      'Sind Sie sicher, dass Sie das Projekt "{{projectName}}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    deleteSuccess: 'Projekt "{{projectName}}" wurde erfolgreich gelöscht.',
    deleteError: 'Projektlöschung fehlgeschlagen.',
    duplicateSuccess: 'Projekt "{{projectName}}" wurde erfolgreich dupliziert.',
    duplicateError: 'Projektduplizierung fehlgeschlagen.',
    makePrivateTooltip: 'Als privat markieren',
    makePublicTooltip: 'Als öffentlich markieren',
    shareTooltip: 'Projekt teilen',
    downloadTooltip: 'Projekt herunterladen',
    notFound: 'Projekt "{{projectName}}" nicht gefunden. Es wurde möglicherweise bereits gelöscht.',
  },
  editor: {
    backButtonTooltip: 'Zurück zur Projektübersicht',
    exportButtonTooltip: 'Aktuelle Segmentierungsdaten exportieren',
    saveTooltip: 'Änderungen speichern',
    image: 'Bild',
    previousImage: 'Vorheriges Bild',
    nextImage: 'Nächstes Bild',
    resegmentButton: 'Neu segmentieren',
    resegmentButtonTooltip: 'Segmentierung für dieses Bild erneut ausführen',
    exportMaskButton: 'Maske exportieren',
    exportMaskButtonTooltip: 'Segmentierungsmaske für dieses Bild exportieren',
    backButton: 'Zurück',
    exportButton: 'Exportieren',
    saveButton: 'Speichern',
    loadingProject: 'Projekt wird geladen...',
    loadingImage: 'Bild wird geladen...',
    sliceErrorInvalidPolygon: 'Schneiden nicht möglich: Ungültiges Polygon ausgewählt.',
    sliceWarningInvalidResult: 'Das Schneiden hat zu kleine und ungültige Polygone erstellt.',
    sliceWarningInvalidIntersections:
      'Ungültiger Schnitt: Die Schnittlinie muss das Polygon an genau zwei Punkten schneiden.',
    sliceSuccess: 'Polygon erfolgreich geschnitten.',
    noPolygonToSlice: 'Keine Polygone zum Schneiden verfügbar.',
    savingTooltip: 'Speichert...',
  },
  segmentationPage: {
    noImageSelected: 'Kein Bild für die Neusegmentierung ausgewählt.',
    resegmentationStarted: 'Starte Neusegmentierung mit ResUNet neuronalem Netzwerk...',
    resegmentationQueued: 'Neusegmentierung wurde in die Warteschlange gestellt.',
    resegmentationCompleted: 'Neusegmentierung erfolgreich abgeschlossen.',
    resegmentationFailed: 'Neusegmentierung fehlgeschlagen.',
    resegmentationTimeout: 'Zeitüberschreitung bei Neusegmentierung. Überprüfen Sie den Warteschlangenstatus.',
    resegmentationError: 'Neusegmentierung konnte nicht gestartet werden.',
    resegmentTooltip: 'Neu segmentieren',
  },
  share: {
    accepted: 'Akzeptiert',
    alreadyShared: 'Bereits mit diesem Benutzer geteilt',
    canEdit: 'Kann bearbeiten',
    copyToClipboard: 'In Zwischenablage kopieren',
    edit: 'Bearbeiten',
    email: 'E-Mail',
    failedToCopy: 'Link konnte nicht kopiert werden',
    failedToGenerateLink: 'Freigabelink konnte nicht generiert werden',
    failedToLoadShares: 'Geteilte Benutzer konnten nicht geladen werden',
    failedToRemove: 'Freigabe konnte nicht entfernt werden',
    failedToShare: 'Projekt konnte nicht geteilt werden',
    generateLink: 'Link generieren',
    generateNewLink: 'Neuen Link generieren',
    generating: 'Generiert...',
    invalidEmail: 'Ungültige E-Mail-Adresse',
    invalidEmailOrPermission: 'Ungültige E-Mail oder Berechtigung',
    invite: 'Einladen',
    inviteByEmail: 'Per E-Mail einladen',
    inviteByLink: 'Per Link einladen',
    linkCopied: 'Link in Zwischenablage kopiert',
    linkGenerated: 'Freigabelink generiert',
    linkPermissions: 'Link-Berechtigungen',
    noPermission: 'Keine Berechtigung',
    noShares: 'Keine geteilten Benutzer',
    pendingAcceptance: 'Annahme ausstehend',
    permissions: 'Berechtigungen',
    projectNotFound: 'Projekt nicht gefunden',
    removeShare: 'Freigabe entfernen',
    selectAccessLevel: 'Zugriffsebene auswählen',
    selectPermission: 'Bitte wählen Sie einen Berechtigungstyp',
    shareDescription: 'Teilen Sie dieses Projekt mit anderen Benutzern',
    sharedWith: 'Geteilt mit',
    shareLinkDescription: 'Jeder mit diesem Link kann auf das Projekt zugreifen',
    shareProject: 'Projekt teilen',
    shareProjectTitle: 'Projekt "{{projectName}}" teilen',
    sharing: 'Teilt...',
    sharedSuccess: 'Projekt "{{projectName}}" wurde mit {{email}} geteilt',
    removedSuccess: 'Freigabe für {{email}} wurde entfernt',
    status: 'Status',
    userEmail: 'Benutzer-E-Mail',
    view: 'Anzeigen',
    viewOnly: 'Nur anzeigen',
  },
  invitation: {
    title: 'Projekteinladung',
    processing: 'Einladung wird verarbeitet...',
    successTitle: 'Einladung angenommen!',
    successMessage: 'Sie haben jetzt Zugriff auf "{{projectName}}", geteilt von {{ownerName}}.',
    redirecting: 'Weiterleitung zum Projekt...',
    errorTitle: 'Einladung kann nicht angenommen werden',
    loginRequired: 'Anmeldung erforderlich',
    loginMessage: 'Bitte melden Sie sich an, um diese Projekteinladung anzunehmen.',
    signIn: 'Anmelden',
    createAccount: 'Konto erstellen',
    goToDashboard: 'Zum Dashboard',
    invalidLink: 'Ungültiger Einladungslink',
    expired: 'Dieser Einladungslink ist abgelaufen oder ungültig',
    notForYou: 'Diese Einladung ist nicht für Ihr Konto bestimmt',
    genericError: 'Einladung konnte nicht angenommen werden. Bitte versuchen Sie es erneut.',
    acceptedSuccess: 'Einladung erfolgreich angenommen',
  },
  about: {
    title: 'Über SpheroSeg',
    mission: {
      title: 'Unsere Mission',
      description:
        'SpheroSeg ist eine fortschrittliche Plattform, die speziell für die Segmentierung und Analyse von Zellsphäroiden in mikroskopischen Bildern entwickelt wurde. Wir kombinieren modernste Algorithmen künstlicher Intelligenz mit einer intuitiven Benutzeroberfläche, um Forschern präzise Sphäroidgrenzenerkennung und Analysefähigkeiten zu bieten.',
      vision:
        'Unsere Vision ist es, wissenschaftliche Entdeckungen zu beschleunigen, indem wir fortschrittliche Bildanalyse für Forscher weltweit zugänglich machen und es ihnen ermöglichen, sich auf ihre Forschung statt auf technische Herausforderungen zu konzentrieren.',
    },
    technology: {
      title: 'Unsere Technologie',
      description:
        'Basierend auf modernsten Deep-Learning-Modellen und Computer-Vision-Techniken bietet SpheroSeg beispiellose Genauigkeit bei der Sphäroidsegmentierung.',
      feature1: {
        title: 'KI-gestützte Segmentierung',
        description:
          'Fortschrittliche Deep-Learning-Modelle, die auf vielfältigen Sphäroidbildern trainiert wurden, gewährleisten genaue und zuverlässige Segmentierungsergebnisse.',
      },
      feature2: {
        title: 'Echtzeitverarbeitung',
        description:
          'Optimierte Algorithmen bieten schnelle Verarbeitungszeiten und ermöglichen es Ihnen, große Datensätze effizient zu analysieren.',
      },
      feature3: {
        title: 'Umfassende Analyse',
        description:
          'Extrahieren Sie detaillierte Metriken wie Fläche, Umfang, Kreisförmigkeit und mehr für jedes segmentierte Sphäroid.',
      },
    },
    team: {
      title: 'Unser Team',
      description:
        'SpheroSeg wurde von einem engagierten Team von Forschern und Ingenieuren entwickelt, die sich leidenschaftlich für die Förderung der biomedizinischen Forschung einsetzen',
      member1: {
        name: 'Michal Průšek',
        role: 'Leitender Entwickler, FJFI ČVUT Prag',
      },
      member2: {
        name: 'Adam Novozámský',
        role: 'Betreuer, UTIA CAS',
      },
      member3: {
        name: 'Forschungsteam',
        role: 'Institut für Biochemie und Mikrobiologie, VŠCHT Prag',
      },
    },
    contact: {
      title: 'Kontaktieren Sie uns',
      description: 'Haben Sie Fragen oder benötigen Unterstützung? Wir sind hier, um zu helfen!',
      email: 'Kontakt per E-Mail',
      github: 'Auf GitHub ansehen',
      twitter: 'Auf Twitter folgen',
    },
  },
};
