// Chinese translations
export default {
  'segmentation': {
    'contextMenu': {
      'editPolygon': '编辑多边形', 'splitPolygon': '分割多边形', 'deletePolygon': '删除多边形', 'confirmDeleteTitle': '您确定要删除多边形吗？', 'confirmDeleteMessage': '此操作不可逆。多边形将从分割中永久删除。', 'duplicateVertex': '复制顶点', 'deleteVertex': '删除顶点'
    }, 'title': '分割编辑器', 'resolution': '{width}x{height}', 'queue': {
      'title': '分割队列', 'summary': '总共 {{total}} 个任务（{{running}} 个处理中，{{queued}} 个排队中）', 'noRunningTasks': '没有运行中的任务', 'noQueuedTasks': '没有排队的任务', 'task': '任务', 'statusRunning': '分割：{{count}} 个运行中{{queued}}', 'statusQueued': '，{{count}} 个排队中', 'statusOnlyQueued': '分割：{{count}} 个排队中', 'statusOnlyQueued_one': '分割：1 个排队中', 'statusOnlyQueued_other': '分割：{{count}} 个排队中', 'processing': '处理中', 'queued': '排队中', 'statusProcessing': '分割：{{count}} 个处理中', 'statusReady': '就绪', 'tasksTotal': '总共 {{total}} 个任务（{{running}} 个处理中，{{queued}} 个排队中）'
    }, 'selectPolygonForEdit': '选择要编辑的多边形', 'selectPolygonForSlice': '选择要切片的多边形', 'selectPolygonForAddPoints': '选择要添加点的多边形', 'clickToAddPoint': '点击添加点', 'clickToCompletePolygon': '点击第一个点以完成多边形', 'clickToAddFirstSlicePoint': '点击添加第一个切片点', 'clickToAddSecondSlicePoint': '点击添加第二个切片点', 'polygonCreationMode': '多边形创建模式', 'polygonEditMode': '多边形编辑模式', 'polygonSliceMode': '多边形切片模式', 'polygonAddPointsMode': '添加点模式', 'viewMode': '查看模式', 'totalPolygons': '总多边形数', 'totalVertices': '总顶点数', 'vertices': '顶点', 'zoom': '缩放', 'mode': '模式', 'selected': '已选择', 'inProgress': '进行中', 'segmenting': '分割中：{{current}} / {{total}}', 'polygonsCreated': '已创建{{count}}个多边形', 'processingComplete': '处理完成', 'notStarted': '未开始', 'currentCell': '当前细胞', 'outOf': '共', 'noPolygonsFound': '未找到多边形', 'errorLoadingSegmentation': '加载分割时出错', 'autoSegment': '自动分割', 'saveChanges': '保存更改', 'discardChanges': '放弃更改', 'unsavedChanges': '未保存的更改', 'confirmDiscardChanges': '您有未保存的更改。确定要放弃它们吗？', 'savingChanges': '正在保存更改...', 'changesSaved': '更改已保存', 'errorSavingChanges': '保存更改时出错', 'imageNotFound': '未找到图片', 'polygonCreated': '多边形已创建', 'polygonDeleted': '多边形已删除', 'polygonUpdated': '多边形已更新', 'segmentationNotFound': '未找到分割', 'loadingSegmentation': '正在加载分割...', 'noSegmentationResults': '此图像没有分割结果', 'createNewPolygon': '创建新多边形', 'editExistingPolygon': '编辑现有多边形', 'slicePolygon': '切片多边形', 'addPointsToPolygon': '向多边形添加点', 'finishEditing': '完成编辑', 'cancelEditing': '取消编辑', 'confirmDeletePolygon': '确定要删除此多边形吗？', 'modeSelect': '选择', 'modeCreate': '创建', 'modeEdit': '编辑', 'modeSlice': '切片', 'modeAddPoints': '添加点', 'instructions': {
      'select': '点击多边形进行选择', 'create': '点击添加顶点。点击第一个顶点以关闭多边形。', 'edit': '拖动顶点移动它们。右键单击顶点以获取更多选项。', 'slice': '点击两个点在它们之间画一条线并切片多边形。', 'addPoints': '点击多边形边缘添加新顶点。'
    }, 'tools': {
      'select': '选择工具', 'create': '创建工具', 'edit': '编辑工具', 'slice': '切片工具', 'addPoints': '添加点工具', 'pan': '平移', 'zoomIn': '放大', 'zoomOut': '缩小', 'fitToScreen': '适应屏幕', 'resetView': '重置视图'
    }, 'sidebar': {
      'title': '多边形', 'searchPlaceholder': '搜索多边形...', 'noPolygons': '没有多边形', 'polygon': '多边形', 'area': '面积', 'perimeter': '周长', 'vertices': '顶点', 'selected': '已选择'
    }, 'history': {
      'undo': '撤销', 'redo': '重做', 'cannotUndo': '无法撤销', 'cannotRedo': '无法重做'
    }, 'export': {
      'title': '导出分割', 'format': '格式', 'formatJSON': 'JSON', 'formatCSV': 'CSV', 'formatMask': '掩膜图像', 'includeMetrics': '包括指标', 'export': '导出', 'exporting': '正在导出...', 'exportSuccess': '导出成功', 'exportError': '导出时出错'
    }, 'batch': {
      'mixed': '分割：{{successCount}} 张图片成功排队，{{failCount}} 张失败', 'allSuccess': '分割：所有 {{count}} 张图片成功排队', 'allFailed': '分割：所有 {{count}} 张图片失败'
    }, 'none': '无', 'polygons': '多边形', 'returnToProject': '返回项目', 'backToProject': '返回项目', 'previousImage': '上一张图片', 'nextImage': '下一张图片', 'toggleShortcuts': '切换快捷键', 'modes': {
      'view': '查看模式', 'edit': '编辑模式', 'create': '创建模式', 'slice': '切片模式', 'addPoints': '添加点模式', 'deletePolygon': '删除多边形模式', 'createPolygon': '创建多边形模式', 'editVertices': '编辑顶点模式', 'editMode': '编辑模式', 'slicingMode': '切片模式', 'pointAddingMode': '点添加模式'
    }, 'status': {
      'processing': '处理中', 'queued': '排队中', 'completed': '已完成', 'failed': '失败', 'pending': '待处理', 'withoutSegmentation': '无分割'
    }, 'autoSave': {
      'enabled': '自动保存：已启用', 'disabled': '自动保存：已禁用', 'idle': '自动保存：空闲', 'pending': '待处理...', 'saving': '正在保存...', 'success': '已保存', 'error': '错误'
    }, 'loading': '正在加载分割...', 'polygon': '多边形', 'noData': '没有可用的分割数据', 'noPolygons': '未找到多边形', 'regions': '分割', 'position': '位置', 'processingImage': '正在处理图片...', 'helpTips': {
      'view': {
        'pan': '按住鼠标左键并拖动以平移', 'selectPolygon': '单击多边形以选择它', 'zoom': '使用鼠标滚轮进行缩放'
      }
    }, 'imageNotFoundDescription': '请求的图片无法找到。', 'invalidImageDimensions': '无效的图片尺寸', 'noDataToSave': '没有要保存的数据', 'polygonDuplicated': '多边形已复制', 'polygonNotFound': '未找到多边形', 'invalidPolygonData': '无效的多边形数据', 'notEnoughPointsForPolygon': '多边形需要至少3个点', 'saveSuccess': '分割已保存', 'saveError': '保存分割时出错', 'loadError': '加载分割时出错', 'invalidSlicePoints': '无效的切片点', 'sliceError': '切片多边形时出错', 'sliceSuccess': '多边形切片成功', 'vertexAdded': '顶点已添加', 'vertexDeleted': '顶点已删除', 'vertexDuplicated': '顶点已复制', 'invalidVertexOperation': '无效的顶点操作', 'cellsUpdated': '细胞已更新', 'cellUpdateError': '更新细胞时出错', 'clearConfirmTitle': '清除所有分割？', 'clearConfirmMessage': '这将删除所有多边形。此操作无法撤消。', 'cleared': '分割已清除', 'error': {
      'exception': '分割错误：{{error}}', 'failed': '分割失败'
    }, 'modeTips': {
      'view': '点击并拖动以平移，滚动以缩放', 'create': '点击以添加顶点，关闭以完成', 'edit': '拖动顶点以调整形状', 'slice': '点击两个点以切割多边形'
    }, 'segmentationLoading': '正在加载分割...', 'segmentationPolygon': '分割多边形', 'selectPolygonFirst': '请先选择一个多边形', 'addFirstPoint': '点击以添加第一个点', 'clickToClose': '点击第一个点以关闭多边形', 'minPoints': '至少需要 {{count}} 个点', 'shortcuts': {
      'title': '键盘快捷键', 'toggleShortcuts': '切换快捷键 (H)', 'viewMode': '查看模式 (V)', 'createMode': '创建模式 (C)', 'editMode': '编辑模式 (E)', 'sliceMode': '切片模式 (S)', 'deletePolygon': '删除多边形 (Delete)', 'save': '保存 (Ctrl+S)', 'undo': '撤销 (Ctrl+Z)', 'redo': '重做 (Ctrl+Y)', 'nextImage': '下一张图片 (→)', 'previousImage': '上一张图片 (←)'
    }, 'resegment': {
      'button': '重新分割', 'title': '重新分割图片', 'confirm': '您确定要重新分割这张图片吗？当前的分割将被替换。', 'inProgress': '正在重新分割...', 'success': '重新分割成功', 'failed': '重新分割失败', 'noImagesSelected': '未选择图片', 'triggeringResegmentation': '正在触发 {{count}} 张图片的重新分割...', 'deleteConfirmation': '您确定要删除 {{count}} 张图片吗？此操作无法撤消。'
    }
  }, 'errors': {
    'somethingWentWrong': '出现问题', 'componentError': '此组件发生错误。我们已收到通知，将尽快解决问题。', 'errorDetails': '错误详情', 'tryAgain': '重试', 'reloadPage': '重新加载页面', 'goBack': '返回', 'notFound': '404', 'pageNotFoundMessage': '哎呀！页面未找到', 'returnToHome': '返回首页', 'unauthorized': '未授权', 'forbidden': '禁止访问', 'serverError': '服务器错误', 'networkError': '网络错误', 'timeout': '请求超时', 'unknown': '未知错误', 'invalidEmail': '无效的电子邮件地址', 'invalidPassword': '密码必须至少为8个字符', 'passwordMismatch': '密码不匹配', 'emailAlreadyExists': '该电子邮件地址已被使用', 'invalidCredentials': '无效的凭据', 'sessionExpired': '您的会话已过期。请重新登录。', 'accessDenied': '访问被拒绝', 'resourceNotFound': '未找到资源', 'validationError': '验证错误', 'uploadError': '上传错误', 'downloadError': '下载错误', 'deleteError': '删除错误', 'updateError': '更新错误', 'createError': '创建错误', 'loadError': '加载错误', 'saveError': '保存错误', 'exportError': '导出错误', 'importError': '导入错误', 'connectionError': '连接错误', 'unexpectedError': '发生意外错误', 'retryMessage': '请稍后重试', 'contactSupport': '如果问题仍然存在，请联系支持', 'errorCode': '错误代码', 'errorMessage': '错误消息', 'stackTrace': '堆栈跟踪', 'reportIssue': '报告问题', 'copyError': '复制错误', 'copiedToClipboard': '已复制到剪贴板', 'failedToCopy': '复制失败', 'loadingImages': '加载图片时出错', 'loadingProjects': '加载项目时出错', 'savingData': '保存数据时出错', 'deletingItem': '删除项目时出错', 'unknownError': '未知错误'
  }, 'requestAccess': {
    'title': '请求访问', 'description': '填写此表格以请求访问SpheroSeg平台。我们将审核您的请求并尽快回复。', 'and': '和', 'form': {
      'firstName': '名字', 'lastName': '姓氏', 'email': '电子邮件', 'institution': '机构', 'message': '消息', 'submit': '提交请求', 'submitting': '正在提交...', 'firstNamePlaceholder': '输入您的名字', 'lastNamePlaceholder': '输入您的姓氏', 'emailPlaceholder': '输入您的电子邮件', 'institutionPlaceholder': '输入您的机构', 'messagePlaceholder': '告诉我们您为什么需要访问（可选）', 'firstNameRequired': '名字是必需的', 'lastNameRequired': '姓氏是必需的', 'emailRequired': '电子邮件是必需的', 'emailInvalid': '无效的电子邮件地址', 'institutionRequired': '机构是必需的'
    }, 'success': {
      'title': '请求已提交', 'message': '感谢您的关注！我们已收到您的访问请求，将尽快审核。', 'checkEmail': '请查看您的电子邮件以获取更新。'
    }, 'error': {
      'title': '提交失败', 'message': '提交您的访问请求时出现问题。请稍后重试。', 'retry': '重试'
    }, 'alreadyRequested': {
      'title': '已请求访问', 'message': '您已使用此电子邮件地址提交了访问请求。', 'checkEmail': '请查看您的电子邮件以获取有关您请求的更新。'
    }, 'alreadyHaveAccess': {
      'title': '您已经有访问权限', 'message': '此电子邮件地址已与现有帐户关联。', 'signIn': '登录您的帐户'
    }
  }, 'requestAccessForm': {
    'title': '请求访问', 'subtitle': '填写下面的表格以请求访问SpheroSeg平台。', 'firstName': '名字', 'lastName': '姓氏', 'email': '电子邮件地址', 'institution': '机构', 'message': '消息（可选）', 'submit': '提交请求', 'submitting': '正在提交...', 'success': '您的访问请求已成功提交！', 'error': '提交您的请求时出错。请重试。', 'emailExists': '具有此电子邮件的帐户已存在。', 'requestExists': '已使用此电子邮件提交了访问请求。'
  }, 'documentation': {
    'tag': '用户指南', 'title': 'SpheroSeg文档', 'subtitle': '了解如何有效使用球体分割平台。', 'sidebar': {
      'title': '部分', 'introduction': '介绍', 'gettingStarted': '入门', 'uploadingImages': '上传图像', 'segmentationProcess': '分割过程', 'apiReference': 'API参考'
    }, 'introduction': {
      'title': '介绍', 'imageAlt': '球体分析工作流程说明', 'whatIs': {
        'title': '什么是SpheroSeg？', 'paragraph1': 'SpheroSeg是一个专门的生物医学图像分析平台，专注于球体分割。我们的先进算法旨在帮助研究人员准确识别和分析显微镜图像中的细胞球体。', 'paragraph2': '该平台支持多种图像格式，并提供自动和手动分割工具，为研究人员提供分析细胞培养物所需的灵活性。'
      }, 'keyFeatures': {
        'title': '主要特点', 'feature1': '基于AI的自动球体检测', 'feature2': '精确编辑的手动细化工具', 'feature3': '批处理能力，用于大型数据集', 'feature4': '详细的形态学指标和测量', 'feature5': '导出选项，用于进一步分析', 'feature6': '安全的云端存储，用于您的项目'
      }, 'benefits': {
        'title': '使用SpheroSeg的好处', 'benefit1': '减少手动分析时间高达90%', 'benefit2': '提高测量准确性和一致性', 'benefit3': '随时随地访问您的数据', 'benefit4': '与团队成员协作项目', 'benefit5': '维护可再现的分析工作流程'
      }
    }, 'gettingStarted': {
      'title': '入门', 'accountCreation': {
        'title': '帐户创建', 'paragraph1': '要使用SpheroSeg，您需要创建一个帐户。这使我们能够安全地存储您的项目和图像。', 'step1Prefix': '访问', 'step1Link': '注册页面', 'step2': '输入您的机构电子邮件地址并创建密码', 'step3': '用您的姓名和机构完成您的个人资料', 'step4': '通过发送到您收件箱的链接验证您的电子邮件地址'
      }, 'creatingProject': {
        'title': '创建您的第一个项目', 'paragraph1': '项目帮助您组织工作。每个项目可以包含多个图像及其相应的分割结果。', 'step1': '在您的仪表板上，点击'新项目'', 'step2': '输入项目名称和描述', 'step3': '选择项目类型（默认：球体分析）', 'step4': '点击'创建项目'继续'
      }
    }, 'uploadingImages': {
      'title': '上传图像', 'paragraph1': 'SpheroSeg支持显微镜中常用的各种图像格式，包括TIFF、PNG和JPEG。', 'methods': {
        'title': '上传方法', 'paragraph1': '有几种上传图像的方法：', 'step1': '将文件直接拖放到上传区域', 'step2': '点击上传区域从计算机浏览和选择文件', 'step3': '一次批量上传多个图像'
      }, 'note': {
        'prefix': '注意：', 'text': '为获得最佳结果，请确保您的显微镜图像在球体和背景之间具有良好的对比度。'
      }
    }, 'segmentationProcess': {
      'title': '分割过程', 'paragraph1': '分割过程识别图像中球体的边界，允许精确分析其形态。', 'automatic': {
        'title': '自动分割', 'paragraph1': '我们的AI驱动的自动分割可以高精度检测球体边界：', 'step1': '从您的项目中选择一个图像', 'step2': '点击'自动分割'启动过程', 'step3': '系统将处理图像并显示检测到的边界', 'step4': '在分割编辑器中查看结果'
      }, 'manual': {
        'title': '手动调整', 'paragraph1': '有时自动分割可能需要细化。我们的编辑器提供以下工具：', 'step1': '沿边界添加或删除顶点', 'step2': '调整顶点位置以获得更准确的边界', 'step3': '分割或合并区域', 'step4': '在球体内添加或删除孔'
      }
    }, 'apiReference': {
      'title': 'API参考', 'paragraph1': 'SpheroSeg提供RESTful API，用于对平台功能进行编程访问。这非常适合与您现有的工作流程或批处理集成。', 'endpoint1Desc': '检索所有项目的列表', 'endpoint2Desc': '检索特定项目中的所有图像', 'endpoint3Desc': '为特定图像启动分割', 'contactPrefix': '有关完整的API文档和身份验证详细信息，请联系我们', 'backToHome': '返回首页', 'backToTop': '返回顶部'
    }
  }, 'hero': {
    'platformTag': '高级球体分割平台', 'title': '用于生物医学研究的AI驱动细胞分析', 'subtitle': '使用我们尖端的球体分割平台提升您的显微细胞图像分析。专为寻求精确性和效率的研究人员设计。', 'getStartedButton': '开始使用', 'learnMoreButton': '了解更多', 'imageAlt1': '球体显微镜图像', 'imageAlt2': '带分析的球体显微镜图像', 'welcomeTitle': '欢迎使用SpheroSeg', 'welcomeSubtitle': '细胞球体分割和分析的高级平台', 'welcomeDescription': '我们的平台将尖端的人工智能算法与直观的界面相结合，用于精确检测和分析显微镜图像中的细胞球体。', 'featuresTitle': '强大的功能', 'featuresSubtitle': '生物医学研究的高级工具', 'featureAiSegmentation': '高级分割', 'featureAiSegmentationDesc': '精确的球体检测，带有边界分析，用于准确的细胞测量。', 'featureEditing': 'AI驱动的分析', 'featureEditingDesc': '利用深度学习算法进行自动检测和细胞分类。', 'featureAnalytics': '轻松上传', 'featureAnalyticsDesc': '拖放您的显微镜图像以立即处理和分析。', 'featureExport': '统计洞察', 'featureExportDesc': '全面的指标和可视化，用于提取有意义的数据模式。', 'ctaTitle': '准备好改变您的细胞分析工作流程了吗？', 'ctaSubtitle': '加入已经使用我们平台加速发现的领先研究人员。', 'ctaButton': '创建帐户'
  }, 'navbar': {
    'home': '首页', 'features': '功能', 'documentation': '文档', 'terms': '条款', 'privacy': '隐私', 'login': '登录', 'requestAccess': '请求访问'
  }, 'project': {
    'detail': {
      'noImagesSelected': '未选择图像', 'triggeringResegmentation': '正在为{{count}}个图像触发重新分割...', 'deleteConfirmation': '确定要删除{{count}}个图像吗？此操作无法撤销。', 'deletingImages': '正在删除{{count}}个图像...', 'deleteSuccess': '成功删除{{count}}个图像', 'deleteFailed': '删除{{count}}个图像失败', 'preparingExport': '正在准备导出{{count}}个图像...'
    }, 'segmentation': {
      'processingInBatches': '正在为{{count}}个图像启动分割，分为{{batches}}批...', 'batchQueued': '批次{{current}}/{{total}}已成功排队', 'batchQueuedFallback': '批次{{current}}/{{total}}已成功排队（备用端点）', 'batchError': '处理批次{{current}}/{{total}}时出错', 'partialSuccess': '分割：{{success}}个图像成功排队，{{failed}}个失败', 'allSuccess': '分割：所有{{count}}个图像成功排队', 'allFailed': '分割：所有{{count}}个图像失败', 'startedImages': '已为{{count}}个图像启动分割', 'queuedLocallyWarning': '已在本地为{{count}}个图像排队分割。服务器连接失败。'
    }, 'loading': '正在加载项目...', 'notFound': '项目未找到', 'error': '加载项目时出错', 'empty': '此项目为空', 'noImages': {
      'title': '还没有图片', 'description': '该项目还没有任何图片。上传图片以开始使用。', 'uploadButton': '上传图片'
    }, 'addImages': '添加图像以开始', 'title': '项目详情', 'description': '描述', 'created': '创建时间', 'lastModified': '最后修改', 'images': '图像', 'owner': '所有者', 'collaborators': '协作者', 'status': '状态', 'active': '活动', 'archived': '已归档', 'shareProject': '共享项目', 'exportProject': '导出项目', 'deleteProject': '删除项目', 'settings': '项目设置', 'permissions': '权限', 'activity': '活动', 'noActivity': '还没有活动', 'imageUploadSuccess': '成功上传{{count}}个图像', 'imageUploadError': '上传图像时出错', 'saving': '正在保存...', 'saved': '已保存', 'confirmLeave': '您有未保存的更改。确定要离开吗？', 'resegmentImage': '重新分割图片', 'deleteImage': '删除图片'
  }, 'projects': {
    'title': '项目', 'subtitle': '管理您的研究项目和分析', 'newProject': '新项目', 'createProject': '创建项目', 'searchPlaceholder': '搜索项目...', 'filterByStatus': '按状态筛选', 'filterByDate': '按日期筛选', 'sortBy': '排序方式', 'sortByName': '名称', 'sortByDate': '日期', 'sortBySize': '大小', 'viewMode': '查看模式', 'gridView': '网格视图', 'listView': '列表视图', 'noProjects': '未找到项目', 'createFirstProject': '创建您的第一个项目以开始', 'loadingProjects': '正在加载项目...', 'errorLoadingProjects': '加载项目时出错', 'projectCard': {
      'images': '图像', 'lastModified': '最后修改', 'created': '创建时间', 'viewProject': '查看项目', 'editProject': '编辑项目', 'deleteProject': '删除项目', 'archiveProject': '归档项目', 'shareProject': '共享项目', 'exportProject': '导出项目'
    }, 'createProjectDialog': {
      'title': '创建新项目', 'nameLabel': '项目名称', 'namePlaceholder': '输入项目名称', 'descriptionLabel': '描述', 'descriptionPlaceholder': '输入项目描述（可选）', 'typeLabel': '项目类型', 'createButton': '创建项目', 'creating': '正在创建...', 'cancel': '取消', 'success': '项目创建成功', 'error': '创建项目失败', 'duplicateNameError': '具有此名称的项目已存在'
    }, 'editProject': {
      'title': '编辑项目', 'saveButton': '保存更改', 'saving': '正在保存...', 'success': '项目更新成功', 'error': '更新项目失败'
    }, 'deleteProject': {
      'title': '删除项目', 'message': '确定要删除此项目吗？此操作无法撤销。', 'confirm': '删除', 'cancel': '取消', 'deleting': '正在删除...', 'success': '项目删除成功', 'error': '删除项目失败'
    }, 'shareProject': {
      'title': '共享项目', 'message': '输入要与之共享此项目的用户的电子邮件地址。', 'emailLabel': '电子邮件地址', 'emailPlaceholder': '输入电子邮件地址', 'permissionLabel': '权限', 'permissionView': '查看', 'permissionEdit': '编辑', 'permissionAdmin': '管理员', 'shareButton': '共享', 'sharing': '正在共享...', 'success': '项目共享成功', 'error': '共享项目失败', 'sharedWith': '共享对象', 'removeAccess': '删除访问权限', 'noShares': '此项目尚未共享'
    }, 'exportProject': {
      'title': '导出项目', 'message': '选择导出格式和选项。', 'formatLabel': '格式', 'formatJSON': 'JSON', 'formatCSV': 'CSV', 'formatZIP': 'ZIP存档', 'includeImages': '包括图像', 'includeSegmentations': '包括分割', 'includeMetadata': '包括元数据', 'exportButton': '导出', 'exporting': '正在导出...', 'success': '项目导出成功', 'error': '导出项目失败'
    }, 'deleteSuccess': '项目删除成功', 'deleteError': '删除项目时出错'
  }, 'projectsPage': {
    'title': '项目', 'description': '管理研究项目', 'createNew': '创建新项目', 'createProject': '创建项目', 'createProjectDesc': '开始新的研究项目', 'projectName': '项目名称', 'projectDescription': '项目描述', 'projectNamePlaceholder': '输入项目名称', 'projectDescriptionPlaceholder': '输入项目描述', 'projectCreated': '项目创建成功', 'projectCreationFailed': '项目创建失败', 'projectDeleted': '项目删除成功', 'projectDeletionFailed': '项目删除失败', 'confirmDelete': '确定要删除此项目吗？', 'confirmDeleteDescription': '此操作无法撤销。与此项目相关的所有数据将被永久删除。', 'deleteProject': '删除项目', 'editProject': '编辑项目', 'viewProject': '查看项目', 'projectUpdated': '项目更新成功', 'projectUpdateFailed': '项目更新失败', 'noProjects': '未找到项目', 'createFirstProject': '创建您的第一个项目以开始', 'searchProjects': '搜索项目...', 'filterProjects': '筛选项目', 'sortProjects': '排序项目', 'projectNameRequired': '项目名称为必填项', 'loginRequired': '您必须登录才能创建项目', 'createdAt': '创建时间', 'updatedAt': '最后更新', 'imageCount': '图像', 'status': '状态', 'actions': '操作', 'loading': '正在加载项目...', 'error': '加载项目时出错', 'retry': '重试', 'duplicating': '正在复制项目...', 'duplicate': '复制', 'duplicateSuccess': '项目复制成功', 'duplicateFailed': '项目复制失败', 'duplicateTitle': '复制项目', 'duplicateProject': '复制项目', 'duplicateProjectDescription': '创建此项目的副本，包括所有图像。您可以在下面自定义选项。', 'duplicateCancelled': '项目复制已取消', 'duplicatingProject': '正在复制项目', 'duplicatingProjectDescription': '您的项目正在复制中。这可能需要几分钟时间。'
  }, 'projectToolbar': {
    'uploadImages': '上传图像', 'deleteSelected': '删除选定', 'exportSelected': '导出选定', 'segmentSelected': '分割选定', 'selectAll': '全选', 'deselectAll': '取消全选', 'selectedCount': '已选择{{count}}个', 'searchImages': '搜索图像...', 'filterImages': '筛选图像', 'sortImages': '排序图像', 'gridView': '网格视图', 'listView': '列表视图', 'imageSize': '图像大小', 'small': '小', 'medium': '中', 'large': '大', 'showSegmentations': '显示分割', 'hideSegmentations': '隐藏分割', 'refreshImages': '刷新图像', 'batchOperations': '批量操作', 'importImages': '导入图像', 'downloadOriginals': '下载原始文件'
  }, 'statsOverview': {
    'totalImages': '总图像', 'segmentedImages': '已分割图像', 'pendingSegmentation': '待分割', 'failedSegmentation': '分割失败', 'averageProcessingTime': '平均处理时间', 'totalProcessingTime': '总处理时间', 'successRate': '成功率', 'lastUpdated': '最后更新', 'refreshStats': '刷新统计', 'exportStats': '导出统计', 'viewDetails': '查看详情', 'statisticsTitle': '项目统计', 'performanceTitle': '性能指标', 'processingTitle': '处理信息', 'totalProjects': '项目总数', 'vsLastMonth': '相比上月', 'totalSegmentations': '分割总数', 'activeProjects': '活跃项目', 'recentActivity': '最近活动', 'viewAll': '查看全部', 'noData': '暂无数据'
  }, 'dashboard': {
    'title': '仪表板', 'welcomeMessage': '欢迎回来', 'noProjects': '您还没有任何项目', 'createFirstProject': '创建您的第一个项目', 'recentProjects': '最近的项目', 'projectStats': '项目统计', 'totalProjects': '总项目数', 'totalImages': '总图像数', 'loading': '正在加载...', 'error': '加载时出错', 'overview': '概览', 'activity': '活动', 'quickActions': '快速操作', 'newProject': '新项目', 'uploadImages': '上传图像', 'viewAllProjects': '查看所有项目', 'recentActivity': '最近活动', 'noRecentActivity': '没有最近的活动', 'systemStatus': '系统状态', 'operational': '运行正常', 'degraded': '性能下降', 'outage': '服务中断', 'notifications': '通知', 'noNotifications': '没有新通知', 'markAllRead': '全部标记为已读', 'viewAllNotifications': '查看所有通知', 'statistics': {
      'totalProjects': '项目总数', 'activeProjects': '活动项目', 'totalImages': '图像总数', 'processedImages': '已处理图像', 'storageUsed': '已用存储', 'processingTime': '处理时间'
    }, 'charts': {
      'projectsOverTime': '项目随时间变化', 'imagesProcessed': '已处理图像', 'storageUsage': '存储使用情况', 'activityHeatmap': '活动热图'
    }
  }, 'auth': {
    'signIn': '登录', 'signUp': '注册', 'signOut': '登出', 'email': '电子邮件', 'password': '密码', 'login': '登录', 'register': '注册', 'welcome': '欢迎', 'confirmPassword': '确认密码', 'firstName': '名字', 'lastName': '姓氏', 'institution': '机构', 'rememberMe': '记住我', 'forgotPassword': '忘记密码？', 'signInTitle': '登录', 'signInDescription': '登录到您的帐户', 'emailAddressLabel': '电子邮件地址', 'passwordLabel': '密码', 'forgotPasswordLink': '忘记密码？', 'noAccount': '还没有帐户？', 'emailPlaceholder': '输入您的电子邮件', 'passwordPlaceholder': '输入您的密码', 'fillAllFields': '请填写所有必填字段', 'resetPassword': '重置密码', 'newPassword': '新密码', 'confirmNewPassword': '确认新密码', 'sendResetLink': '发送重置链接', 'resetLinkSent': '重置链接已发送', 'checkEmail': '请查看您的电子邮件以获取密码重置链接', 'invalidResetToken': '无效或过期的重置令牌', 'passwordResetSuccess': '密码重置成功', 'returnToLogin': '返回登录', 'createAccount': '创建帐户', 'alreadyHaveAccount': '已有账户？', 'dontHaveAccount': '没有账户？', 'orContinueWith': '或继续使用', 'agreeToTerms': '我同意条款和条件', 'termsAndConditions': '条款和条件', 'privacyPolicy': '隐私政策', 'and': '和', 'signInError': '登录错误', 'signUpError': '注册错误', 'emailAlreadyExists': '电子邮件已存在', 'weakPassword': '密码太弱。请使用更强的密码。', 'accountCreated': '帐户创建成功', 'verifyEmail': '请验证您的电子邮件地址', 'resendVerification': '重新发送验证电子邮件', 'verificationSent': '验证电子邮件已发送', 'accountVerified': '帐户验证成功', 'invalidVerificationToken': '无效或过期的验证令牌', 'twoFactorAuth': '双因素身份验证', 'enterCode': '输入验证码', 'verify': '验证', 'enableTwoFactor': '启用双因素身份验证', 'disableTwoFactor': '禁用双因素身份验证', 'backupCodes': '备份代码', 'generateBackupCodes': '生成备份代码', 'downloadCodes': '下载代码', 'twoFactorEnabled': '双因素身份验证已启用', 'twoFactorDisabled': '双因素身份验证已禁用', 'incorrectCode': '验证码不正确', 'sessionExpired': '您的会话已过期', 'pleaseSignInAgain': '请重新登录', 'accountLocked': '您的账户已被锁定。请联系支持。', 'tooManyAttempts': '尝试次数过多。请稍后重试。', 'accountSuspended': '帐户已暂停', 'contactSupport': '请联系支持以获取帮助', 'logoutSuccess': '成功登出', 'loggingIn': '正在登录...', 'signingUp': '正在注册...', 'loggingOut': '正在登出...', 'requestAccess': '请求访问', 'termsAndPrivacy': '通过注册，您同意我们的', 'serverError': '服务器错误。请稍后再试。', 'signInFailed': '登录失败。请检查您的凭据。', 'alreadyHaveAccess': '已有访问权限？', 'alreadyLoggedInMessage': '您已登录', 'alreadyLoggedInTitle': '已登录', 'backToSignIn': '返回登录', 'creatingAccount': '正在创建账户...', 'emailHasPendingRequest': '此电子邮件有待处理的请求', 'enterEmail': '输入您的电子邮件地址', 'enterEmailForReset': '输入您的电子邮件以重置密码', 'enterInfoCreateAccount': '输入您的信息以创建账户', 'firstNamePlaceholder': '名字', 'forgotPasswordTitle': '重置您的密码', 'invalidCredentials': '无效的凭据', 'invalidTokenAction': '无效的令牌或操作', 'lastNamePlaceholder': '姓氏', 'needAnAccount': '需要一个账户？', 'newPasswordLabel': '新密码', 'confirmPasswordLabel': '确认密码', 'requestAccessTitle': '申请访问权限', 'requestSent': '请求已发送', 'requestSentMessage': '您的访问请求已提交。我们会尽快与您联系。', 'resendEmail': '重新发送电子邮件', 'resetPasswordEmailSent': '重置密码电子邮件已发送', 'resetPasswordInstructions': '请检查您的电子邮件以获取重置密码的说明。', 'signUpFailed': '注册失败。请稍后再试。', 'signUpSuccess': '注册成功！', 'signUpSuccessMessage': '您的账户已创建。请检查您的电子邮件以验证您的账户。', 'submitRequest': '提交请求', 'welcomeBack': '欢迎回来', 'signInToYourAccount': '登录您的账户', 'emailVerificationRequired': '需要电子邮件验证', 'emailVerificationSent': '验证电子邮件已发送到您的电子邮件地址。', 'resendVerificationEmail': '重新发送验证电子邮件', 'checkYourEmail': '检查您的电子邮件'
  }, 'footer': {
    'developerName': 'Bc. Michal Průšek', 'facultyName': '布拉格捷克理工大学核物理与工程学院', 'description': '先进的球体分割和分析平台', 'contactLabel': '<EMAIL>', 'developerLabel': 'Bc. Michal Průšek', 'facultyLabel': '布拉格捷克理工大学核物理与工程学院', 'resourcesTitle': '资源', 'documentationLink': '文档', 'featuresLink': '功能', 'tutorialsLink': '教程', 'researchLink': '研究', 'legalTitle': '法律信息', 'termsLink': '服务条款', 'privacyLink': '隐私政策', 'contactUsLink': '联系我们', 'informationTitle': '信息', 'contactTitle': '联系方式', 'copyrightNotice': 'SpheroSeg。保留所有权利。', 'madeWith': '制作', 'by': '由', 'requestAccessLink': '请求访问', 'githubRepository': 'GitHub 仓库', 'contactEmail': '联系邮箱'
  }, 'features': {
    'tag': '功能特性', 'title': '探索我们的平台功能', 'subtitle': '用于生物医学研究的先进工具', 'cards': {
      'segmentation': {
        'title': '高级分割', 'description': '精确的球体检测与边界分析，实现准确的细胞测量'
      }, 'aiAnalysis': {
        'title': 'AI驱动分析', 'description': '利用深度学习算法进行自动细胞检测和分类'
      }, 'uploads': {
        'title': '便捷上传', 'description': '拖放您的显微镜图像即可立即处理和分析'
      }, 'insights': {
        'title': '统计洞察', 'description': '全面的指标和可视化，提取有意义的数据模式'
      }, 'collaboration': {
        'title': '团队协作', 'description': '与同事共享项目和结果，提高研究效率'
      }, 'pipeline': {
        'title': '自动化流程', 'description': '使用我们的批处理工具简化您的工作流程'
      }
    }, 'automation': {
      'title': '自动化', 'description': '使用 AI 驱动的工具自动分割和分析'
    }, 'collaboration': {
      'title': '协作', 'description': '与您的团队共享项目和结果'
    }, 'export': {
      'title': '导出', 'description': '以多种格式导出您的数据'
    }, 'security': {
      'title': '安全', 'description': '您的数据通过企业级安全保护'
    }
  }, 'navigation': {
    'home': '首页', 'projects': '项目', 'dashboard': '仪表板', 'profile': '个人资料', 'settings': '设置', 'documentation': '文档', 'support': '支持', 'logout': '登出', 'login': '登录', 'signup': '注册', 'back': '返回', 'language': '语言', 'notifications': '通知', 'help': '帮助', 'search': '搜索', 'menu': '菜单', 'close': '关闭'
  }, 'common': {
    'appName': 'Spheroid Segmentation', 'appNameShort': 'SpheroSeg', 'loading': '正在加载...', 'error': '错误', 'save': '保存', 'cancel': '取消', 'delete': '删除', 'edit': '编辑', 'create': '创建', 'close': '关闭', 'confirm': '确认', 'back': '返回', 'next': '下一个', 'previous': '上一个', 'search': '搜索', 'filter': '筛选', 'sort': '排序', 'view': '查看', 'export': '导出', 'upload': '上传', 'download': '下载', 'refresh': '刷新', 'retry': '重试', 'signIn': '登录', 'signOut': '登出', 'dashboard': '仪表板', 'submit': '提交', 'reset': '重置', 'clear': '清除', 'select': '选择', 'selectAll': '全选', 'deselectAll': '取消全选', 'yes': '是', 'no': '否', 'ok': '确定', 'apply': '应用', 'remove': '删除', 'add': '添加', 'update': '更新', 'change': '更改', 'continue': '继续', 'finish': '完成', 'start': '开始', 'stop': '停止', 'pause': '暂停', 'resume': '恢复', 'restart': '重新开始', 'copy': '复制', 'paste': '粘贴', 'cut': '剪切', 'undo': '撤销', 'redo': '重做', 'duplicate': '复制', 'archive': '归档', 'unarchive': '取消归档', 'expand': '展开', 'collapse': '折叠', 'more': '更多', 'less': '更少', 'showMore': '显示更多', 'showLess': '显示更少', 'details': '详情', 'info': '信息', 'warning': '警告', 'success': '成功', 'name': '名称', 'description': '描述', 'date': '日期', 'time': '时间', 'status': '状态', 'type': '类型', 'size': '大小', 'actions': '操作', 'options': '选项', 'settings': '设置', 'preferences': '偏好设置', 'help': '帮助', 'about': '关于', 'version': '版本', 'copyright': '版权', 'license': '许可证', 'language': '语言', 'theme': '主题', 'darkMode': '深色模式', 'lightMode': '浅色模式', 'system': '系统', 'auto': '自动', 'manual': '手动', 'default': '默认', 'custom': '自定义', 'enabled': '已启用', 'disabled': '已禁用', 'on': '开', 'off': '关', 'active': '活动', 'inactive': '非活动', 'pending': '待处理', 'completed': '已完成', 'failed': '失败', 'cancelled': '已取消', 'running': '运行中', 'queued': '排队中', 'processing': '处理中...', 'uploading': '正在上传...', 'downloading': '下载中', 'deleting': '删除中', 'saving': '正在保存', 'searching': '搜索中', 'noResults': '没有结果', 'noData': '没有数据', 'empty': '空', 'notFound': '未找到', 'unauthorized': '您无权执行此操作', 'forbidden': '访问被禁止', 'offline': '离线', 'online': '在线', 'connected': '已连接', 'disconnected': '已断开', 'retrying': '重试中', 'unknown': '未知', 'other': '其他', 'total': '总计', 'count': '计数', 'average': '平均', 'min': '最小', 'max': '最大', 'sum': '总和', 'all': '全部', 'none': '无', 'selected': '已选择', 'of': '的', 'to': '到', 'from': '从', 'by': '由', 'in': '在', 'at': '在', 'for': '为', 'with': '与', 'and': '和', 'or': '或', 'not': '不', 'is': '是', 'are': '是', 'was': '曾是', 'were': '曾是', 'been': '已经', 'have': '有', 'has': '有', 'had': '曾有', 'will': '将', 'would': '会', 'could': '能', 'should': '应该', 'may': '可能', 'might': '可能', 'must': '必须', 'shall': '将', 'can': '能', 'cannot': '不能', 'true': '真', 'false': '假', 'null': '空', 'undefined': '未定义', 'NaN': '非数字', 'infinity': '无穷', 'today': '今天', 'yesterday': '昨天', 'tomorrow': '明天', 'now': '现在', 'later': '稍后', 'soon': '很快', 'never': '从不', 'always': '总是', 'sometimes': '有时', 'often': '经常', 'rarely': '很少', 'daily': '每日', 'weekly': '每周', 'monthly': '每月', 'yearly': '每年', 'seconds': '秒', 'minutes': '分钟', 'hours': '小时', 'days': '天', 'weeks': '周', 'months': '月', 'years': '年', 'ago': '前', 'remaining': '剩余', 'elapsed': '已过', 'duration': '持续时间', 'startDate': '开始日期', 'endDate': '结束日期', 'startTime': '开始时间', 'endTime': '结束时间', 'timeZone': '时区', 'format': '格式', 'example': '示例', 'examples': '示例', 'documentation': '文档', 'learnMore': '了解更多', 'seeAll': '查看全部', 'hide': '隐藏', 'show': '显示', 'toggle': '切换', 'open': '打开', 'minimize': '最小化', 'maximize': '最大化', 'restore': '恢复', 'fullscreen': '全屏', 'exitFullscreen': '退出全屏', 'zoomIn': '放大', 'zoomOut': '缩小', 'resetZoom': '重置缩放', 'fitToScreen': '适应屏幕', 'actualSize': '实际大小', 'rotateLeft': '向左旋转', 'rotateRight': '向右旋转', 'flipHorizontal': '水平翻转', 'flipVertical': '垂直翻转', 'crop': '裁剪', 'resize': '调整大小', 'scale': '缩放', 'transform': '变换', 'adjust': '调整', 'enhance': '增强', 'effects': '效果', 'brightness': '亮度', 'contrast': '对比度', 'saturation': '饱和度', 'hue': '色调', 'sharpness': '锐度', 'blur': '模糊', 'noise': '噪点', 'grain': '颗粒', 'vignette': '晕影', 'shadow': '阴影', 'highlight': '高光', 'exposure': '曝光', 'gamma': '伽马', 'vibrance': '自然饱和度', 'warmth': '暖度', 'tint': '色调', 'fade': '褪色', 'clarity': '清晰度', 'dehaze': '去雾', 'sharpen': '锐化', 'smoothing': '平滑', 'structure': '结构', 'termsOfService': '服务条款', 'privacyPolicy': '隐私政策', 'loadingAccount': '正在加载您的账户...', 'loadingApplication': '正在加载应用程序...', 'signUp': '注册', 'signingIn': '正在登录...', 'profile': '个人资料', 'project': '项目', 'projects': '项目', 'newProject': '新项目', 'removeAll': '全部删除', 'uploadImages': '上传图片', 'recentAnalyses': '最近的分析', 'noProjects': '未找到项目', 'noImages': '未找到图片', 'createYourFirst': '创建您的第一个项目以开始使用', 'tryAgain': '重试', 'email': '电子邮件', 'password': '密码', 'confirmPassword': '确认密码', 'firstName': '名字', 'lastName': '姓氏', 'username': '用户名', 'projectName': '项目名称', 'projectDescription': '项目描述', 'image': '图片', 'light': '浅色', 'dark': '深色', 'welcome': '欢迎使用球体分割平台', 'account': '账户', 'passwordConfirm': '确认密码', 'manageAccount': '管理账户', 'changePassword': '更改密码', 'deleteAccount': '删除账户', 'requestAccess': '请求访问', 'accessRequest': '访问请求', 'createAccount': '创建账户', 'signInToAccount': '登录账户', 'termsOfServiceLink': '服务条款', 'privacyPolicyLink': '隐私政策', 'optional': '可选', 'saveChanges': '保存更改', 'notSpecified': '未指定', 'enable': '启用', 'disable': '禁用', 'backToHome': '返回主页', 'lastChange': '最后更改', 'emailPlaceholder': '输入您的电子邮件', 'passwordPlaceholder': '输入您的密码', 'selectImages': '选择图片', 'noImagesDescription': '上传图片以开始您的项目', 'images': '图片', 'files': '文件', 'validationFailed': '验证失败', 'cropAvatar': '裁剪头像', 'profileTitle': '个人资料', 'profileDescription': '更新其他用户可见的个人资料信息', 'profileUsername': '用户名', 'profileUsernamePlaceholder': '输入您的用户名', 'profileFullName': '全名', 'profileFullNamePlaceholder': '输入您的全名', 'profileTitlePlaceholder': '例如：研究员、教授', 'profileOrganization': '组织', 'profileOrganizationPlaceholder': '输入您的组织或机构', 'profileBio': '简介', 'profileBioPlaceholder': '写一份关于您自己的简短介绍', 'profileBioDescription': '简要描述您的研究兴趣和专业知识', 'profileLocation': '位置', 'profileLocationPlaceholder': '例如：中国北京', 'profileSaveButton': '保存个人资料', 'share': '分享', 'projectNamePlaceholder': '输入项目名称', 'projectDescPlaceholder': '输入项目描述', 'creatingProject': '正在创建项目...', 'createSuccess': '项目创建成功', 'maxFileSize': '最大文件大小：{{size}}MB', 'accepted': '已接受', 'uploadComplete': '上传完成', 'uploadFailed': '上传失败', 'deletePolygon': '删除多边形', 'pleaseLogin': '请登录以继续', 'segmentation': '分割', 'copiedToClipboard': '已复制到剪贴板！', 'failedToCopy': '复制到剪贴板失败'
  }, 'index': {
    'title': 'SpheroSeg - 高级球体分割平台', 'description': '用于生物医学研究的AI驱动细胞分析平台', 'keywords': '球体分割，细胞分析，生物医学研究，AI，深度学习', 'about': {
      'tag': '关于平台', 'title': '什么是 SpheroSeg？', 'imageAlt': '球体分割示例', 'paragraph1': 'SpheroSeg 是一个专门为显微图像中细胞球体的分割和分析而设计的高级平台。', 'paragraph2': '我们的工具将尖端的人工智能算法与直观的界面相结合，为研究人员提供精确的球体边界检测和分析功能。', 'paragraph3': '该平台由布拉格捷克理工大学核物理与工程学院的 Michal Průšek 在捷克科学院理论与应用力学研究所的 Adam Novozámský 指导下开发，与布拉格化工大学生物化学与微生物学系的研究人员合作完成。', 'contactPrefix': '<EMAIL>'
    }, 'cta': {
      'title': '准备改变您的研究方式？', 'subtitle': '立即开始使用 SpheroSeg，探索细胞球体分析的新可能', 'boxTitle': '创建免费账户', 'boxText': '获取所有平台功能的访问权限，开始分析您的显微镜图像', 'button': '创建账户'
    }
  }, 'tools': {
    'select': '选择', 'polygon': '多边形', 'slice': '切片', 'pan': '平移', 'zoom': '缩放', 'edit': '编辑', 'delete': '删除', 'measure': '测量', 'annotate': '注释', 'export': '导出'
  }, 'settings': {
    'title': '设置', 'profile': '个人资料', 'general': '通用', 'appearance': '外观', 'notifications': '通知', 'privacy': '隐私', 'security': '安全', 'account': '帐户', 'billing': '计费', 'integrations': '集成', 'api': 'API', 'advanced': '高级', 'language': '语言', 'theme': '主题', 'toggleTheme': '切换主题', 'darkMode': '深色模式', 'emailNotifications': '电子邮件通知', 'pushNotifications': '推送通知', 'twoFactorAuth': '双因素身份验证', 'changePassword': '更改密码', 'deleteAccount': '删除帐户', 'exportData': '导出数据', 'importData': '导入数据', 'clearCache': '清除缓存', 'resetSettings': '重置设置', 'version': '版本', 'updates': '更新', 'checkForUpdates': '检查更新', 'autoUpdate': '自动更新', 'releaseNotes': '发行说明', 'about': '关于', 'help': '帮助', 'support': '支持', 'feedback': '反馈', 'reportBug': '报告错误', 'requestFeature': '请求功能', 'contact': '联系', 'documentation': '文档', 'tutorial': '教程', 'faq': '常见问题', 'community': '社区', 'forum': '论坛', 'discord': 'Discord', 'github': 'GitHub', 'twitter': 'Twitter', 'linkedin': 'LinkedIn', 'youtube': 'YouTube', 'changingPassword': '正在更改密码...', 'generalTitle': '常规设置', 'generalDescription': '管理您的基本账户设置', 'securityTitle': '安全', 'securityDescription': '管理您的密码和安全设置', 'notificationsTitle': '通知', 'notificationsDescription': '配置您希望如何接收通知', 'privacyTitle': '隐私', 'privacyDescription': '控制您的数据和隐私设置'
  }, 'accessibility': {
    'skipToContent': '跳到内容', 'skipToNavigation': '跳到导航', 'closeDialog': '关闭对话框', 'openMenu': '打开菜单', 'closeMenu': '关闭菜单', 'expandSection': '展开部分', 'collapseSection': '折叠部分', 'previousSlide': '上一张幻灯片', 'nextSlide': '下一张幻灯片', 'pauseCarousel': '暂停轮播', 'playCarousel': '播放轮播', 'mute': '静音', 'unmute': '取消静音', 'increaseVolume': '增加音量', 'decreaseVolume': '减小音量', 'enterFullscreen': '进入全屏', 'exitFullscreen': '退出全屏', 'closeModal': '关闭模态框', 'openInNewWindow': '在新窗口中打开', 'downloadFile': '下载文件', 'copyToClipboard': '复制到剪贴板', 'shareOnSocialMedia': '在社交媒体上分享', 'printPage': '打印页面', 'increaseTextSize': '增大文本大小', 'decreaseTextSize': '减小文本大小', 'resetTextSize': '重置文本大小', 'highContrast': '高对比度', 'normalContrast': '正常对比度', 'keyboardShortcuts': '键盘快捷键', 'helpMenu': '帮助菜单', 'accessibilityStatement': '无障碍声明', 'reportAccessibilityIssue': '报告无障碍问题'
  }, 'profile': {
    'title': '个人资料', 'personalInfo': '个人信息', 'accountSettings': '帐户设置', 'preferences': '偏好设置', 'security': '安全', 'privacy': '隐私', 'notifications': '通知', 'billing': '计费', 'subscription': '订阅', 'usage': '使用情况', 'activity': '活动', 'sessions': '会话', 'devices': '设备', 'applications': '应用程序', 'connections': '连接', 'firstName': '名字', 'lastName': '姓氏', 'email': '电子邮件', 'phone': '电话', 'institution': '机构', 'department': '部门', 'position': '职位', 'bio': '简介', 'website': '网站', 'location': '位置', 'timezone': '时区', 'language': '语言', 'avatar': '头像', 'uploadAvatar': '上传头像', 'removeAvatar': '删除头像', 'changeEmail': '更改电子邮件', 'changePassword': '更改密码', 'enableTwoFactor': '启用双因素', 'disableTwoFactor': '禁用双因素', 'connectedAccounts': '连接的帐户', 'connectAccount': '连接帐户', 'disconnectAccount': '断开帐户', 'deleteAccount': '删除帐户', 'downloadData': '下载数据', 'exportData': '导出数据', 'dataRetention': '数据保留', 'activityLog': '活动日志', 'loginHistory': '登录历史', 'apiKeys': 'API密钥', 'createApiKey': '创建API密钥', 'revokeApiKey': '撤销API密钥', 'webhooks': 'Webhooks', 'createWebhook': '创建Webhook', 'deleteWebhook': '删除Webhook', 'integrations': '集成', 'authorizedApps': '授权的应用程序', 'revokeAccess': '撤销访问', 'storageUsage': '存储使用情况', 'bandwidth': '带宽', 'credits': '信用', 'invoices': '发票', 'paymentMethods': '付款方式', 'addPaymentMethod': '添加付款方式', 'updatePaymentMethod': '更新付款方式', 'removePaymentMethod': '删除付款方式', 'billingAddress': '账单地址', 'taxInformation': '税务信息', 'receipts': '收据', 'statements': '对账单', 'editProfile': '编辑个人资料', 'joined': '加入时间', 'statistics': '统计', 'recentActivity': '最近活动', 'totalProjects': '项目总数', 'totalImages': '图片总数', 'storageUsed': '已用存储', 'projects': '项目', 'images': '图片', 'analyses': '分析', 'noRecentActivity': '没有最近的活动', 'aboutMe': '关于我', 'noBio': '没有可用的简介', 'fetchError': '加载个人资料时出错', 'updateError': '更新个人资料时出错', 'updateSuccess': '个人资料更新成功', 'avatarHelp': '上传个人头像', 'avatarImageOnly': '仅限图片文件', 'avatarTooLarge': '图片太大。最大大小为 5MB', 'avatarUpdated': '头像更新成功', 'avatarUploadError': '上传头像时出错', 'avatarRemoved': '头像删除成功', 'avatarRemoveError': '删除头像时出错', 'cropAvatarDescription': '调整您的个人头像', 'saveButton': '保存更改', 'username': '用户名', 'usernamePlaceholder': '输入您的用户名', 'fullName': '全名', 'fullNamePlaceholder': '输入您的全名', 'titlePlaceholder': '输入您的职称', 'organization': '组织', 'organizationPlaceholder': '输入您的组织', 'bioPlaceholder': '输入您的简介', 'bioDescription': '简要描述您的研究兴趣和专业知识', 'locationPlaceholder': '输入您的位置', 'activityDescription': '您的最近活动', 'imagesUploaded': '上传的图片', 'segmentationsCompleted': '完成的分割', 'pageTitle': '用户个人资料'
  }, 'termsPage': {
    'title': '服务条款', 'acceptance': {
      'title': '1. 接受条款', 'paragraph1': '通过访问或使用SpheroSeg，您接受并同意受这些服务条款的约束。如果您不接受这些条款，请不要使用我们的服务。'
    }, 'useLicense': {
      'title': '2. 使用许可', 'paragraph1': 'SpheroSeg授予您有限的、非独占的、不可转让的许可，用于研究和教育目的。商业使用需要单独的许可协议。'
    }, 'dataUsage': {
      'title': '3. 数据使用', 'paragraph1': '您保留对上传图像和数据的所有权利。未经您的明确同意，我们不会与第三方共享您的数据。您的数据仅用于提供和改进我们的分割服务。'
    }, 'limitations': {
      'title': '4. 服务限制', 'paragraph1': 'SpheroSeg按'原样'提供，不提供任何明示或暗示的保证。我们不保证100%的可用性、准确性或可靠性。用户负责验证其特定应用的结果。'
    }, 'revisions': {
      'title': '5. 修订', 'paragraph1': '我们保留随时修改这些条款的权利。更改将在发布后立即生效。您继续使用服务即表示接受修订后的条款。'
    }, 'governingLaw': {
      'title': '6. 管辖法律', 'paragraph1': '这些条款受捷克共和国法律管辖。任何争议将由布拉格的主管法院解决。'
    }, 'lastUpdated': '最后更新：2025年1月7日'
  }, 'privacyPage': {
    'title': '隐私政策', 'introduction': {
      'title': '1. 简介', 'paragraph1': '在SpheroSeg，我们致力于保护您的隐私。本隐私政策解释了我们收集哪些信息、如何使用这些信息以及您对您的信息享有的权利。', 'content': '我们重视您的隐私，并致力于保护您的个人数据。'
    }, 'dataCollection': {
      'title': '2. 我们收集的信息', 'paragraph1': '我们收集您直接提供给我们的信息，包括：', 'list': ['账户信息（电子邮件、姓名、机构）', '用于分割的上传图像和数据', '项目元数据和分析结果', '使用数据和活动日志'], 'content': '我们收集以下类型的数据：'
    }, 'dataUsage': {
      'title': '3. 我们如何使用您的信息', 'paragraph1': '我们使用收集的信息用于：', 'list': ['提供和维护我们的服务', '处理您的图像分割请求', '改进我们的算法和服务', '就您的账户与您沟通', '确保安全并防止滥用'], 'content': '我们使用您的数据来：'
    }, 'dataStorage': {
      'title': '4. 数据存储和安全', 'paragraph1': '我们实施适当的技术和组织措施来保护您的个人信息免受未经授权的访问、更改、披露或破坏。', 'paragraph2': '您的数据存储在安全的服务器上，并根据我们的数据保留政策进行删除。'
    }, 'dataSharing': {
      'title': '5. 数据共享', 'paragraph1': '未经您的同意，我们不会出售、交易或以其他方式将您的个人信息转让给第三方，除非本政策中描述的情况或法律要求。'
    }, 'userRights': {
      'title': '6. 您的权利', 'paragraph1': '您有权：', 'list': ['访问您的个人信息', '更正不准确的信息', '请求删除您的数据', '导出您的数据', '反对处理您的数据'], 'content': '您有权：'
    }, 'cookies': {
      'title': '7. Cookie和跟踪技术', 'paragraph1': '我们使用cookie和类似技术来改善您的体验、分析网站使用情况并个性化内容。'
    }, 'changes': {
      'title': '8. 本政策的变更', 'paragraph1': '我们可能会不时更新我们的隐私政策。我们将通过在此页面上发布新的隐私政策来通知您任何更改。', 'content': '我们可能会更新此隐私政策。变更将在此页面上发布。'
    }, 'contact': {
      'title': '9. 联系我们', 'paragraph1': '如果您对本隐私政策有任何疑问，请通过以下方式联系我们：', 'email': '<EMAIL>', 'content': '如果您对我们的隐私政策有任何疑问，请联系我们：'
    }, 'lastUpdated': '最后更新：2025年1月7日', 'dataProtection': {
      'title': '数据保护', 'content': '我们实施行业标准的安全措施来保护您的数据。'
    }
  }, 'shortcuts': {
    'title': '键盘快捷键', 'general': '通用', 'navigation': '导航', 'editing': '编辑', 'view': '查看', 'tools': '工具', 'save': '保存', 'undo': '撤销', 'redo': '重做', 'copy': '复制', 'paste': '粘贴', 'cut': '剪切', 'selectAll': '全选', 'delete': '删除', 'escape': '退出', 'zoomIn': '放大', 'zoomOut': '缩小', 'fitToScreen': '适应屏幕', 'actualSize': '实际大小', 'toggleFullscreen': '切换全屏', 'search': '搜索', 'help': '帮助', 'preferences': '偏好设置', 'newProject': '新项目', 'openProject': '打开项目', 'closeProject': '关闭项目', 'exportProject': '导出项目', 'uploadImage': '上传图像', 'nextImage': '下一个图像', 'previousImage': '上一个图像', 'toggleSidebar': '切换侧边栏', 'toggleToolbar': '切换工具栏', 'selectTool': '选择工具', 'polygonTool': '多边形工具', 'sliceTool': '切片工具', 'panTool': '平移工具', 'measureTool': '测量工具', 'annotateTool': '注释工具'
  }, 'imageProcessor': {
    'noRunningTasks': '没有运行中的任务', 'noQueuedTasks': '没有排队的任务', 'showLess': '显示更少', 'showMore': '显示更多', 'running': '运行中', 'queued': '排队中', 'failed': '失败', 'completed': '已完成', 'cancelled': '已取消', 'processing': '处理中', 'pending': '待处理', 'status': '状态', 'progress': '进度', 'timeElapsed': '经过时间', 'timeRemaining': '剩余时间', 'eta': '预计到达时间', 'startTime': '开始时间', 'endTime': '结束时间', 'duration': '持续时间', 'retry': '重试', 'cancel': '取消', 'pause': '暂停', 'resume': '恢复', 'restart': '重新开始', 'details': '详情', 'logs': '日志', 'errors': '错误', 'warnings': '警告', 'info': '信息', 'segmentationStarted': '分割过程已开始...', 'startSegmentationTooltip': '开始分割', 'waitingTooltip': '等待处理', 'processingTooltip': '正在处理', 'failedTooltip': '分割失败', 'completedTooltip': '分割完成', 'retryTooltip': '重试分割'
  }, 'uploader': {
    'dragDropText': '拖放文件到这里，或', 'browseText': '浏览', 'supportedFormats': '支持的格式', 'maxFileSize': '最大文件大小', 'maxFiles': '最大文件数', 'uploading': '上传中', 'uploadComplete': '上传完成', 'uploadFailed': '上传失败', 'retry': '重试', 'remove': '删除', 'cancel': '取消', 'pause': '暂停', 'resume': '恢复', 'fileSize': '文件大小', 'fileName': '文件名', 'fileType': '文件类型', 'lastModified': '最后修改', 'uploadSpeed': '上传速度', 'timeRemaining': '剩余时间', 'filesSelected': '已选择文件', 'totalSize': '总大小', 'clearAll': '清除全部', 'uploadAll': '上传全部', 'selectFiles': '选择文件', 'dropFilesHere': '将文件拖放到这里', 'or': '或', 'clickToSelect': '点击选择', 'processingFiles': '正在处理文件', 'analyzingImage': '正在分析图像', 'extractingMetadata': '正在提取元数据', 'generatingThumbnail': '正在生成缩略图', 'validatingFile': '正在验证文件', 'preparingUpload': '正在准备上传', 'uploadProgress': '上传进度', 'uploadSuccess': '上传成功', 'uploadError': '上传错误', 'invalidFileType': '无效的文件类型', 'fileTooLarge': '文件太大', 'tooManyFiles': '文件太多', 'duplicateFile': '重复文件', 'unsupportedFormat': '不支持的格式', 'corruptedFile': '损坏的文件', 'networkError': '网络错误', 'serverError': '服务器错误', 'quotaExceeded': '超出配额', 'permissionDenied': '权限被拒绝'
  }, 'images': {
    'title': '图像', 'noImages': '没有图像', 'uploadImages': '上传图像', 'imageDetails': '图像详情', 'imageName': '图像名称', 'imageSize': '图像大小', 'imageFormat': '图像格式', 'imageDimensions': '图像尺寸', 'uploadDate': '上传日期', 'lastModified': '最后修改', 'segmentationStatus': '分割状态', 'notSegmented': '未分割', 'segmented': '已分割', 'segmenting': '分割中', 'segmentationFailed': '分割失败', 'resegment': '重新分割', 'viewSegmentation': '查看分割', 'editSegmentation': '编辑分割', 'deleteImage': '删除图像', 'downloadImage': '下载图像', 'imageMetadata': '图像元数据', 'imageHistogram': '图像直方图', 'imageStatistics': '图像统计', 'pixelIntensity': '像素强度', 'channelInfo': '通道信息', 'colorSpace': '色彩空间', 'bitDepth': '位深度', 'compression': '压缩', 'resolution': '分辨率', 'aspectRatio': '纵横比', 'fileSize': '文件大小', 'tags': '标签', 'addTag': '添加标签', 'removeTag': '删除标签', 'notes': '笔记', 'addNote': '添加笔记', 'editNote': '编辑笔记', 'deleteNote': '删除笔记', 'share': '共享', 'export': '导出', 'print': '打印', 'compare': '比较', 'duplicate': '复制', 'rotate': '旋转', 'flip': '翻转', 'crop': '裁剪', 'adjust': '调整', 'enhance': '增强', 'annotate': '注释', 'errors': {
      'uploadFailed': '上传失败', 'processingFailed': '处理失败', 'invalidFormat': '无效的格式', 'sizeTooLarge': '文件太大', 'networkError': '网络错误', 'serverError': '服务器错误', 'notFound': '未找到图片', 'accessDenied': '访问被拒绝'
    }, 'success': {
      'uploaded': '上传成功', 'processed': '处理成功', 'deleted': '删除成功', 'updated': '更新成功'
    }, 'info': {
      'uploading': '正在上传...', 'processing': '正在处理...', 'analyzing': '正在分析...', 'loading': '正在加载...'
    }
  }, 'export': {
    'title': '导出数据', 'selectFormat': '选择导出格式', 'exportOptions': '导出选项', 'includeImages': '包含原始图片', 'includeSegmentations': '包含分割', 'includeMetadata': '包含元数据', 'includeAnalysis': '包括分析', 'fileFormat': '文件格式', 'imageFormat': '图像格式', 'quality': '质量', 'compression': '压缩', 'resolution': '分辨率', 'colorSpace': '色彩空间', 'embedMetadata': '嵌入元数据', 'preserveStructure': '保留结构', 'flattenLayers': '展平图层', 'exportButton': '导出', 'exporting': '正在导出', 'exportComplete': '导出完成', 'exportFailed': '导出失败', 'download': '下载', 'saveToCloud': '保存到云', 'emailExport': '通过电子邮件发送导出', 'shareLink': '共享链接', 'copyLink': '复制链接', 'linkCopied': '链接已复制', 'preparingExport': '正在准备导出', 'generatingFiles': '正在生成文件', 'compressingData': '正在压缩数据', 'uploadingToCloud': '正在上传到云', 'sendingEmail': '正在发送电子邮件', 'exportHistory': '导出历史', 'previousExports': '以前的导出', 'downloadAgain': '再次下载', 'deleteExport': '删除导出', 'exportSettings': '导出设置', 'defaultFormat': '默认格式', 'defaultQuality': '默认质量', 'autoExport': '自动导出', 'exportSchedule': '导出计划', 'exportDestination': '导出目标', 'localFolder': '本地文件夹', 'cloudStorage': '云存储', 'ftpServer': 'FTP服务器', 'apiEndpoint': 'API端点', 'selectImagesForExport': '选择要导出的图像', 'selectImagesToExport': '选择要导出的图像', 'noImagesAvailable': '没有可用的图像', 'backToProject': '返回项目', 'exportImages': '导出图片', 'maskExportError': '生成遮罩时出错', 'segmentationExportError': '生成分割数据时出错', 'description': '选择要导出的数据和格式', 'selectData': '选择数据类型', 'options': '导出选项', 'includeMeasurements': '包含测量', 'startExport': '开始导出', 'exportInProgress': '正在导出...', 'downloadReady': '您的下载已准备就绪', 'downloadFile': '下载文件', 'noDataToExport': '没有要导出的数据', 'selectAtLeastOne': '请至少选择一个导出选项'
  }, 'metrics': {
    'title': '指标', 'overview': '概览', 'details': '详情', 'statistics': '统计', 'charts': '图表', 'tables': '表格', 'reports': '报告', 'area': '面积', 'perimeter': '周长', 'circularity': '圆度', 'eccentricity': '偏心率', 'solidity': '实心度', 'extent': '范围', 'aspectRatio': '纵横比', 'roundness': '圆度', 'convexity': '凸度', 'sphericity': '球形度', 'diameter': '直径', 'radius': '半径', 'majorAxis': '长轴', 'minorAxis': '短轴', 'orientation': '方向', 'centroid': '质心', 'boundingBox': '边界框', 'convexHull': '凸包', 'moments': '矩', 'texture': '纹理', 'intensity': '强度', 'meanIntensity': '平均强度', 'stdIntensity': '强度标准差', 'minIntensity': '最小强度', 'maxIntensity': '最大强度', 'integratedIntensity': '积分强度', 'histogram': '直方图', 'distribution': '分布', 'correlation': '相关性', 'covariance': '协方差', 'skewness': '偏度', 'kurtosis': '峰度', 'entropy': '熵', 'energy': '能量', 'contrast': '对比度', 'homogeneity': '均匀性', 'dissimilarity': '不相似性', 'roughness': '粗糙度', 'complexity': '复杂性', 'fractality': '分形', 'lacunarity': '空隙度'
  }, 'imageStatus': {
    'uploaded': '已上传', 'processing': '处理中', 'processed': '已处理', 'failed': '失败', 'pending': '待处理', 'queued': '排队中', 'analyzing': '分析中', 'segmenting': '分割中', 'complete': '完成', 'error': '错误', 'warning': '警告', 'cancelled': '已取消', 'paused': '已暂停', 'resumed': '已恢复', 'retrying': '重试中', 'timeout': '超时', 'invalid': '无效', 'corrupted': '损坏', 'unsupported': '不支持', 'duplicate': '重复', 'skipped': '跳过', 'partial': '部分', 'ready': '就绪', 'notReady': '未就绪', 'available': '可用', 'unavailable': '不可用', 'locked': '锁定', 'unlocked': '解锁', 'archived': '已归档', 'deleted': '已删除', 'restored': '已恢复', 'modified': '已修改', 'unchanged': '未更改', 'new': '新', 'old': '旧', 'current': '当前', 'previous': '以前', 'next': '下一个', 'latest': '最新', 'oldest': '最旧', 'recent': '最近', 'upcoming': '即将到来', 'scheduled': '已计划', 'draft': '草稿', 'published': '已发布', 'unpublished': '未发布', 'approved': '已批准', 'rejected': '已拒绝', 'pending_review': '待审核', 'under_review': '审核中', 'reviewed': '已审核', 'verified': '已验证', 'unverified': '未验证'
  }, 'projectActions': {
    'createNew': '创建新项目', 'open': '打开项目', 'save': '保存项目', 'saveAs': '另存为', 'close': '关闭项目', 'delete': '删除项目', 'duplicate': '复制项目', 'rename': '重命名项目', 'export': '导出项目', 'import': '导入项目', 'archive': '归档项目', 'unarchive': '取消归档项目', 'share': '共享项目', 'unshare': '取消共享项目', 'publish': '发布项目', 'unpublish': '取消发布项目', 'backup': '备份项目', 'restore': '恢复项目', 'merge': '合并项目', 'split': '拆分项目', 'compare': '比较项目', 'sync': '同步项目', 'refresh': '刷新项目', 'lock': '锁定项目', 'unlock': '解锁项目', 'transfer': '转移项目', 'clone': '克隆项目', 'fork': '分叉项目', 'pull': '拉取更改', 'push': '推送更改', 'commit': '提交更改', 'revert': '恢复更改', 'branch': '创建分支', 'merge_branch': '合并分支', 'tag': '标记版本', 'release': '发布版本', 'download': '下载项目', 'upload': '上传项目', 'preview': '预览项目', 'print': '打印项目', 'email': '通过电子邮件发送项目', 'schedule': '计划项目', 'automate': '自动化项目', 'analyze': '分析项目', 'report': '报告项目', 'audit': '审计项目', 'optimize': '优化项目', 'validate': '验证项目', 'test': '测试项目', 'debug': '调试项目', 'monitor': '监控项目', 'track': '跟踪项目', 'log': '记录项目', 'annotate': '注释项目', 'comment': '评论项目', 'rate': '评价项目', 'favorite': '收藏项目', 'bookmark': '书签项目', 'pin': '固定项目', 'unpin': '取消固定项目', 'hide': '隐藏项目', 'show': '显示项目', 'filter': '筛选项目', 'sort': '排序项目', 'group': '分组项目', 'categorize': '分类项目', 'organize': '组织项目', 'search': '搜索项目', 'find': '查找项目', 'replace': '替换项目', 'select': '选择项目', 'selectAll': '全选项目', 'deselectAll': '取消全选项目', 'invertSelection': '反转选择', 'expand': '展开项目', 'collapse': '折叠项目', 'maximize': '最大化项目', 'minimize': '最小化项目', 'resize': '调整项目大小', 'move': '移动项目', 'copy': '复制项目', 'paste': '粘贴项目', 'cut': '剪切项目', 'undo': '撤销', 'redo': '重做', 'history': '历史', 'rollback': '回滚', 'forward': '前进', 'backward': '后退', 'jump': '跳转', 'navigate': '导航', 'browse': '浏览', 'explore': '探索', 'discover': '发现'
  }, 'editor': {
    'title': '编辑器', 'toolbar': '工具栏', 'canvas': '画布', 'layers': '图层', 'properties': '属性', 'history': '历史', 'preferences': '偏好设置', 'shortcuts': '快捷键', 'help': '帮助', 'selectTool': '选择工具', 'moveTool': '移动工具', 'rotateTool': '旋转工具', 'scaleTool': '缩放工具', 'cropTool': '裁剪工具', 'drawTool': '绘制工具', 'eraseTool': '橡皮擦工具', 'fillTool': '填充工具', 'textTool': '文本工具', 'shapeTool': '形状工具', 'penTool': '钢笔工具', 'brushTool': '画笔工具', 'cloneTool': '克隆工具', 'healTool': '修复工具', 'blurTool': '模糊工具', 'sharpenTool': '锐化工具', 'smudgeTool': '涂抹工具', 'dodgeTool': '减淡工具', 'burnTool': '加深工具', 'spongeTool': '海绵工具', 'handTool': '手形工具', 'zoomTool': '缩放工具', 'eyedropperTool': '吸管工具', 'rulerTool': '标尺工具', 'guideTool': '参考线工具', 'gridTool': '网格工具', 'alignTool': '对齐工具', 'distributeTool': '分布工具', 'transformTool': '变换工具', 'warpTool': '扭曲工具', 'perspectiveTool': '透视工具', 'liquifyTool': '液化工具', 'newLayer': '新建图层', 'deleteLayer': '删除图层', 'duplicateLayer': '复制图层', 'mergeLayer': '合并图层', 'flattenLayers': '展平图层', 'hideLayer': '隐藏图层', 'showLayer': '显示图层', 'lockLayer': '锁定图层', 'unlockLayer': '解锁图层', 'renameLayer': '重命名图层', 'opacity': '不透明度', 'blendMode': '混合模式', 'filters': '滤镜', 'adjustments': '调整', 'effects': '效果', 'styles': '样式', 'masks': '蒙版', 'channels': '通道', 'paths': '路径', 'selections': '选区', 'transform': '变换', 'flip': '翻转', 'rotate': '旋转', 'scale': '缩放', 'skew': '倾斜', 'distort': '扭曲', 'perspective': '透视', 'warp': '变形', 'autoAlign': '自动对齐', 'autoBlend': '自动混合', 'contentAware': '内容感知', 'smartObject': '智能对象', 'rasterize': '栅格化', 'vectorize': '矢量化', 'trace': '描摹', 'simplify': '简化', 'smooth': '平滑', 'sharpen': '锐化', 'noise': '噪点', 'grain': '颗粒', 'pixelate': '像素化', 'posterize': '色调分离', 'threshold': '阈值', 'curves': '曲线', 'levels': '色阶', 'colorBalance': '色彩平衡', 'hueStaturation': '色相饱和度', 'colorize': '着色', 'desaturate': '去色', 'invert': '反相', 'equalize': '均衡', 'autoTone': '自动色调', 'autoContrast': '自动对比度', 'autoColor': '自动颜色', 'shadows': '阴影', 'highlights': '高光', 'midtones': '中间调', 'exposure': '曝光', 'vibrance': '自然饱和度', 'clarity': '清晰度', 'dehaze': '去雾', 'vignette': '晕影', 'gradientMap': '渐变映射', 'photoFilter': '照片滤镜', 'channelMixer': '通道混合器', 'colorLookup': '颜色查找', 'selectiveColor': '可选颜色', 'replaceColor': '替换颜色', 'matchColor': '匹配颜色', 'blackAndWhite': '黑白', 'sepia': '棕褐色', 'duotone': '双色调', 'splitToning': '分离色调', 'crossProcess': '交叉处理', 'infrared': '红外', 'negative': '负片', 'solarize': '曝光过度', 'edgeDetection': '边缘检测', 'emboss': '浮雕', 'findEdges': '查找边缘', 'contour': '轮廓', 'stylize': '风格化', 'oilPaint': '油画', 'watercolor': '水彩', 'pencilSketch': '铅笔素描', 'charcoal': '木炭', 'pastel': '粉彩', 'smudgeStick': '涂抹棒', 'sponge': '海绵', 'poster': '海报', 'cutout': '剪贴画', 'dryBrush': '干画笔', 'filmGrain': '胶片颗粒', 'fresco': '壁画', 'neonGlow': '霓虹灯', 'paintDaubs': '绘画涂抹', 'palette': '调色板', 'plastic': '塑料', 'roughPastels': '粗糙粉彩', 'smudge': '涂抹', 'underpainting': '底色', 'texturizer': '纹理化', 'patchwork': '拼接', 'stainedGlass': '彩色玻璃', 'mosaic': '马赛克', 'crystallize': '晶格化', 'pointillize': '点状化', 'colorHalftone': '彩色半调', 'fragment': '碎片', 'mezzotint': '铜版', 'plaster': '石膏', 'reticulation': '网状', 'stamp': '图章', 'tornEdges': '撕边', 'waterPaper': '水彩纸', 'glowingEdges': '发光边缘', 'anisotropic': '各向异性', 'bilateral': '双边', 'boxBlur': '方框模糊', 'gaussianBlur': '高斯模糊', 'lensBlur': '镜头模糊', 'motionBlur': '运动模糊', 'radialBlur': '径向模糊', 'shapeBlur': '形状模糊', 'smartBlur': '智能模糊', 'surfaceBlur': '表面模糊', 'unsharpMask': 'USM锐化', 'sharpenEdges': '锐化边缘', 'sharpenMore': '进一步锐化', 'smartSharpen': '智能锐化', 'shake': '抖动', 'defringe': '去边', 'removeNoise': '去除噪点', 'despeckle': '去斑', 'median': '中值', 'reduce': '减少噪点', 'addNoise': '添加噪点', 'uniform': '均匀', 'gaussian': '高斯', 'clouds': '云彩', 'difference': '差异云彩', 'fibers': '纤维', 'lensFlare': '镜头光晕', 'lighting': '光照效果', 'error': '错误', 'success': '成功', 'edit': '编辑', 'create': '创建', 'delete': '删除', 'save': '保存', 'cancel': '取消', 'close': '关闭', 'confirm': '确认', 'undo': '撤销', 'redo': '重做', 'tools': {
      'select': '选择', 'pan': '平移', 'zoom': '缩放', 'draw': '绘制', 'erase': '擦除', 'measure': '测量'
    }, 'confirmDelete': '您确定要删除这个吗？', 'unsavedChanges': '您有未保存的更改。您确定要离开吗？', 'autoSave': {
      'enabled': '自动保存已启用', 'disabled': '自动保存已禁用', 'saving': '正在自动保存...', 'saved': '自动保存完成', 'failed': '自动保存失败'
    }
  }, 'segmentationPage': {
    'title': '分割', 'selectImage': '选择要分割的图像', 'noImageSelected': '未选择图像', 'startSegmentation': '开始分割', 'segmentationInProgress': '分割进行中', 'segmentationComplete': '分割完成', 'segmentationFailed': '分割失败', 'editSegmentation': '编辑分割', 'saveSegmentation': '保存分割', 'cancelSegmentation': '取消分割', 'resetSegmentation': '重置分割', 'exportSegmentation': '导出分割', 'importSegmentation': '导入分割', 'compareSegmentations': '比较分割', 'segmentationHistory': '分割历史', 'undoSegmentation': '撤销分割', 'redoSegmentation': '重做分割', 'segmentationSettings': '分割设置', 'algorithm': '算法', 'threshold': '阈值', 'iterations': '迭代', 'smoothing': '平滑', 'refinement': '细化', 'postProcessing': '后处理', 'preProcessing': '预处理', 'enhancement': '增强', 'denoising': '去噪', 'normalization': '归一化', 'augmentation': '增强', 'validation': '验证', 'evaluation': '评估', 'metrics': '指标', 'accuracy': '准确度', 'precision': '精确度', 'recall': '召回率', 'f1Score': 'F1分数', 'iou': 'IoU', 'dice': 'Dice系数', 'sensitivity': '灵敏度', 'specificity': '特异性', 'falsePositive': '假阳性', 'falseNegative': '假阴性', 'truePositive': '真阳性', 'trueNegative': '真阴性', 'confusionMatrix': '混淆矩阵', 'rocCurve': 'ROC曲线', 'aucScore': 'AUC分数', 'prCurve': 'PR曲线', 'lossFunction': '损失函数', 'optimizer': '优化器', 'learningRate': '学习率', 'batchSize': '批大小', 'epochs': '时期', 'modelArchitecture': '模型架构', 'backbone': '骨干网络', 'decoder': '解码器', 'encoder': '编码器', 'skipConnections': '跳跃连接', 'attentionMechanism': '注意力机制', 'dataAugmentation': '数据增强', 'transferLearning': '迁移学习', 'fineTuning': '微调', 'hyperparameters': '超参数', 'crossValidation': '交叉验证', 'trainTestSplit': '训练测试分割', 'overfitting': '过拟合', 'underfitting': '欠拟合', 'regularization': '正则化', 'dropout': 'Dropout', 'batchNormalization': '批归一化', 'earlyStop': '早停', 'modelCheckpoint': '模型检查点', 'tensorBoard': 'TensorBoard', 'wandb': 'Weights & Biases', 'mlflow': 'MLflow', 'experimentTracking': '实验跟踪', 'modelRegistry': '模型注册', 'modelDeployment': '模型部署', 'inference': '推理', 'prediction': '预测', 'confidence': '置信度', 'uncertainty': '不确定性', 'explainability': '可解释性', 'interpretability': '可解释性', 'gradCam': 'Grad-CAM', 'lime': 'LIME', 'shap': 'SHAP', 'featureImportance': '特征重要性', 'ablationStudy': '消融研究', 'errorAnalysis': '错误分析', 'performanceProfile': '性能概况', 'computationalCost': '计算成本', 'memoryUsage': '内存使用', 'latency': '延迟', 'throughput': '吞吐量', 'scalability': '可扩展性', 'optimization': '优化', 'quantization': '量化', 'pruning': '剪枝', 'distillation': '蒸馏', 'compression': '压缩', 'acceleration': '加速', 'parallelization': '并行化', 'distributed': '分布式', 'gpu': 'GPU', 'cpu': 'CPU', 'tpu': 'TPU', 'edge': '边缘', 'cloud': '云', 'hybrid': '混合', 'onPremise': '本地', 'containerization': '容器化', 'orchestration': '编排', 'kubernetes': 'Kubernetes', 'docker': 'Docker', 'serverless': '无服务器', 'microservices': '微服务', 'api': 'API', 'sdk': 'SDK', 'cli': 'CLI', 'gui': 'GUI', 'documentation': '文档', 'tutorial': '教程', 'example': '示例', 'demo': '演示', 'benchmark': '基准', 'dataset': '数据集', 'annotation': '标注', 'labeling': '标记', 'crowdsourcing': '众包', 'qualityControl': '质量控制', 'activeWhenLearning': '主动学习', 'semiSupervised': '半监督', 'unsupervised': '无监督', 'supervised': '监督', 'reinforcement': '强化', 'selfSupervised': '自监督', 'fewShot': '少样本', 'zeroShot': '零样本', 'oneShot': '单样本', 'metaLearning': '元学习', 'continualLearning': '持续学习', 'onlineLearning': '在线学习', 'batchLearning': '批量学习', 'incrementalLearning': '增量学习', 'multiTask': '多任务', 'multiModal': '多模态', 'ensemble': '集成', 'voting': '投票', 'bagging': 'Bagging', 'boosting': 'Boosting', 'stacking': 'Stacking', 'blending': '混合', 'fusion': '融合', 'cascade': '级联', 'hierarchy': '层次', 'pipeline': '管道', 'workflow': '工作流', 'automation': '自动化', 'scheduling': '调度', 'monitoring': '监控', 'logging': '日志', 'debugging': '调试', 'profiling': '性能分析', 'testing': '测试', 'unitTest': '单元测试', 'integrationTest': '集成测试', 'endToEndTest': '端到端测试', 'regression': '回归测试', 'performance': '性能测试', 'load': '负载测试', 'stress': '压力测试', 'security': '安全测试', 'penetration': '渗透测试', 'vulnerability': '漏洞', 'compliance': '合规', 'certification': '认证', 'audit': '审计', 'governance': '治理', 'ethics': '伦理', 'bias': '偏见', 'fairness': '公平', 'transparency': '透明度', 'accountability': '问责', 'privacy': '隐私', 'confidentiality': '保密', 'integrity': '完整性', 'availability': '可用性', 'reliability': '可靠性', 'robustness': '鲁棒性', 'resilience': '韧性', 'fault': '容错', 'recovery': '恢复', 'backup': '备份', 'disaster': '灾难恢复', 'continuity': '业务连续性', 'maintenance': '维护', 'support': '支持', 'training': '培训', 'onboarding': '入职', 'knowledge': '知识库', 'community': '社区', 'forum': '论坛', 'slack': 'Slack', 'discord': 'Discord', 'github': 'GitHub', 'stackoverflow': 'Stack Overflow', 'medium': 'Medium', 'blog': '博客', 'podcast': '播客', 'video': '视频', 'webinar': '网络研讨会', 'conference': '会议', 'workshop': '研讨会', 'hackathon': '黑客马拉松', 'competition': '竞赛', 'challenge': '挑战', 'award': '奖项', 'grant': '资助', 'funding': '资金', 'investment': '投资', 'partnership': '合作伙伴关系', 'collaboration': '协作', 'consortium': '联盟', 'alliance': '联盟', 'network': '网络', 'ecosystem': '生态系统', 'platform': '平台', 'marketplace': '市场', 'exchange': '交换', 'integration': '集成', 'interoperability': '互操作性', 'standardization': '标准化', 'protocol': '协议', 'specification': '规范', 'rfc': 'RFC', 'iso': 'ISO', 'ieee': 'IEEE', 'w3c': 'W3C', 'ietf': 'IETF', 'oasis': 'OASIS', 'openapi': 'OpenAPI', 'graphql': 'GraphQL', 'rest': 'REST', 'soap': 'SOAP', 'grpc': 'gRPC', 'websocket': 'WebSocket', 'mqtt': 'MQTT', 'amqp': 'AMQP', 'kafka': 'Kafka', 'rabbitmq': 'RabbitMQ', 'redis': 'Redis', 'memcached': 'Memcached', 'elasticsearch': 'Elasticsearch', 'mongodb': 'MongoDB', 'postgresql': 'PostgreSQL', 'mysql': 'MySQL', 'sqlite': 'SQLite', 'cassandra': 'Cassandra', 'dynamodb': 'DynamoDB', 'firebase': 'Firebase', 'supabase': 'Supabase', 'hasura': 'Hasura', 'prisma': 'Prisma', 'sequelize': 'Sequelize', 'typeorm': 'TypeORM', 'mongoose': 'Mongoose', 'knex': 'Knex', 'sql': 'SQL', 'nosql': 'NoSQL', 'acid': 'ACID', 'base': 'BASE', 'cap': 'CAP', 'consistency': '一致性', 'partition': '分区容错', 'replication': '复制', 'sharding': '分片', 'indexing': '索引', 'caching': '缓存', 'cdn': 'CDN', 'loadBalancer': '负载均衡器', 'reverseProxy': '反向代理', 'firewall': '防火墙', 'vpn': 'VPN', 'ssl': 'SSL', 'tls': 'TLS', 'https': 'HTTPS', 'oauth': 'OAuth', 'jwt': 'JWT', 'saml': 'SAML', 'ldap': 'LDAP', 'mfa': 'MFA', 'sso': 'SSO', 'rbac': 'RBAC', 'abac': 'ABAC', 'encryption': '加密', 'hashing': '哈希', 'signing': '签名', 'certificate': '证书', 'key': '密钥', 'token': '令牌', 'session': '会话', 'cookie': 'Cookie', 'cors': 'CORS', 'csrf': 'CSRF', 'xss': 'XSS', 'sqli': 'SQL注入', 'dos': 'DoS', 'ddos': 'DDoS', 'mitm': '中间人', 'phishing': '钓鱼', 'malware': '恶意软件', 'ransomware': '勒索软件', 'virus': '病毒', 'trojan': '木马', 'worm': '蠕虫', 'spyware': '间谍软件', 'adware': '广告软件', 'rootkit': 'Rootkit', 'botnet': '僵尸网络', 'exploit': '漏洞利用', 'zeroDay': '零日', 'patch': '补丁', 'update': '更新', 'upgrade': '升级', 'version': '版本', 'release': '发布', 'changelog': '更改日志', 'roadmap': '路线图', 'milestone': '里程碑', 'sprint': '冲刺', 'agile': '敏捷', 'scrum': 'Scrum', 'kanban': '看板', 'waterfall': '瀑布', 'devops': 'DevOps', 'cicd': 'CI/CD', 'gitops': 'GitOps', 'infrastructure': '基础设施', 'terraform': 'Terraform', 'ansible': 'Ansible', 'puppet': 'Puppet', 'chef': 'Chef', 'saltstack': 'SaltStack', 'vagrant': 'Vagrant', 'packer': 'Packer', 'consul': 'Consul', 'vault': 'Vault', 'nomad': 'Nomad', 'prometheus': 'Prometheus', 'grafana': 'Grafana', 'elk': 'ELK', 'splunk': 'Splunk', 'datadog': 'Datadog', 'newrelic': 'New Relic', 'sentry': 'Sentry', 'rollbar': 'Rollbar', 'bugsnag': 'Bugsnag', 'pagerduty': 'PagerDuty', 'opsgenie': 'OpsGenie', 'victorops': 'VictorOps', 'statuspage': 'Statuspage', 'confluence': 'Confluence', 'jira': 'Jira', 'trello': 'Trello', 'asana': 'Asana', 'monday': 'Monday', 'notion': 'Notion', 'airtable': 'Airtable', 'basecamp': 'Basecamp', 'clickup': 'ClickUp', 'linear': 'Linear', 'shortcut': 'Shortcut', 'pivotal': 'Pivotal', 'targetprocess': 'Targetprocess', 'rally': 'Rally', 'versionone': 'VersionOne', 'azure': 'Azure DevOps', 'gitlab': 'GitLab', 'bitbucket': 'Bitbucket', 'jenkins': 'Jenkins', 'travis': 'Travis CI', 'circleci': 'CircleCI', 'bamboo': 'Bamboo', 'teamcity': 'TeamCity', 'codeship': 'Codeship', 'codefresh': 'Codefresh', 'buildkite': 'Buildkite', 'drone': 'Drone', 'harness': 'Harness', 'spinnaker': 'Spinnaker', 'argocd': 'ArgoCD', 'flux': 'Flux', 'helm': 'Helm', 'kustomize': 'Kustomize', 'skaffold': 'Skaffold', 'telepresence': 'Telepresence', 'tilt': 'Tilt', 'garden': 'Garden', 'okteto': 'Okteto', 'devspace': 'DevSpace', 'draft': 'Draft', 'brigade': 'Brigade', 'tekton': 'Tekton', 'knative': 'Knative', 'openshift': 'OpenShift', 'rancher': 'Rancher', 'portainer': 'Portainer', 'lens': 'Lens', 'k9s': 'K9s', 'kubectl': 'kubectl', 'kubeadm': 'kubeadm', 'minikube': 'Minikube', 'kind': 'Kind', 'k3s': 'K3s', 'microk8s': 'MicroK8s', 'eks': 'EKS', 'gke': 'GKE', 'aks': 'AKS', 'doks': 'DOKS', 'linode': 'LKE', 'vultr': 'VKE', 'scaleway': 'Kapsule', 'ovh': 'OVHcloud', 'alibaba': 'ACK', 'tencent': 'TKE', 'huawei': 'CCE', 'baidu': 'CCE', 'oracle': 'OKE', 'ibm': 'IKS', 'redhat': 'OpenShift', 'vmware': 'Tanzu', 'nutanix': 'Karbon', 'platform9': 'PMK', 'spectro': 'Spectro Cloud', 'd2iq': 'D2iQ', 'rafay': 'Rafay', 'nirmata': 'Nirmata', 'kublr': 'Kublr', 'kubermatic': 'Kubermatic', 'loft': 'Loft', 'vcluster': 'vcluster', 'crossplane': 'Crossplane', 'cluster': 'Cluster API', 'metal': 'Metal³', 'rook': 'Rook', 'longhorn': 'Longhorn', 'openebs': 'OpenEBS', 'storagejos': 'StorageOS', 'robin': 'Robin', 'portworx': 'Portworx', 'gluster': 'GlusterFS', 'ceph': 'Ceph', 'minio': 'MinIO', 'nfs': 'NFS', 'iscsi': 'iSCSI', 'fc': 'Fibre Channel', 'nas': 'NAS', 'san': 'SAN', 'object': '对象存储', 'block': '块存储', 'file': '文件存储', 'snapshot': '快照', 'migration': '迁移', 'tiering': '分层', 'deduplication': '重复数据删除', 'erasure': '纠删码', 'raid': 'RAID', 'zfs': 'ZFS', 'btrfs': 'Btrfs', 'xfs': 'XFS', 'ext4': 'ext4', 'ntfs': 'NTFS', 'apfs': 'APFS', 'hfs': 'HFS+', 'fat32': 'FAT32', 'exfat': 'exFAT'
  }, 'share': {
    'title': '共享', 'shareProject': '共享项目', 'shareWithTeam': '与团队共享', 'shareWithUser': '与用户共享', 'shareViaLink': '通过链接共享', 'shareViaEmail': '通过电子邮件共享', 'permissions': '权限', 'viewOnly': '仅查看', 'canEdit': '可以编辑', 'canComment': '可以评论', 'fullAccess': '完全访问', 'copyLink': '复制链接', 'sendInvite': '发送邀请', 'invitePeople': '邀请人员', 'enterEmail': '输入电子邮件地址', 'addMessage': '添加消息（可选）', 'send': '发送', 'cancel': '取消', 'linkCopied': '链接已复制到剪贴板', 'inviteSent': '邀请已发送', 'shareSettings': '共享设置', 'makePublic': '公开', 'makePrivate': '设为私有', 'allowDownload': '允许下载', 'allowComments': '允许评论', 'expirationDate': '过期日期', 'password': '密码保护', 'tracking': '跟踪查看', 'revokeAccess': '撤销访问', 'sharedWith': '共享对象', 'noOneShared': '尚未与任何人共享', 'owner': '所有者', 'editor': '编辑者', 'viewer': '查看者', 'commenter': '评论者', 'remove': '删除', 'changePermissions': '更改权限', 'transferOwnership': '转让所有权', 'leaveProject': '离开项目', 'requestAccess': '请求访问', 'pendingInvites': '待处理邀请', 'acceptInvite': '接受邀请', 'declineInvite': '拒绝邀请', 'shareHistory': '共享历史', 'activityLog': '活动日志', 'accessLog': '访问日志', 'lastAccessed': '最后访问', 'timesViewed': '查看次数', 'activeUsers': '活跃用户', 'linkSettings': '链接设置', 'regenerateLink': '重新生成链接', 'customizeLink': '自定义链接', 'qrCode': '二维码', 'embedCode': '嵌入代码', 'socialShare': '社交分享', 'facebook': 'Facebook', 'twitter': 'Twitter', 'linkedin': 'LinkedIn', 'whatsapp': 'WhatsApp', 'telegram': 'Telegram', 'slack': 'Slack', 'teams': 'Microsoft Teams', 'copyEmbedCode': '复制嵌入代码', 'preview': '预览', 'shareOptions': '共享选项', 'internalOnly': '仅内部', 'anyoneWithLink': '拥有链接的任何人', 'specificPeople': '特定人员', 'groups': '组', 'departments': '部门', 'external': '外部', 'guestAccess': '访客访问', 'sso': '单点登录', 'domain': '域限制', 'ipRestriction': 'IP限制', 'watermark': '水印', 'disclaimer': '免责声明', 'nda': '保密协议', 'auditTrail': '审计跟踪', 'compliance': '合规', 'gdpr': 'GDPR', 'hipaa': 'HIPAA', 'sox': 'SOX', 'iso27001': 'ISO 27001', 'dataResidency': '数据驻留', 'retention': '保留政策', 'deletion': '删除政策', 'archival': '归档政策', 'backup': '备份政策', 'recovery': '恢复政策', 'sync': '同步', 'offline': '离线访问', 'mobile': '移动访问', 'desktop': '桌面访问', 'api': 'API访问', 'webhooks': 'Webhooks', 'integrations': '集成', 'notifications': '通知', 'emailNotify': '电子邮件通知', 'pushNotify': '推送通知', 'smsNotify': '短信通知', 'inAppNotify': '应用内通知', 'digest': '摘要', 'realtime': '实时', 'daily': '每日', 'weekly': '每周', 'monthly': '每月', 'custom': '自定义', 'preferences': '偏好设置', 'unsubscribe': '取消订阅', 'manage': '管理', 'bulkShare': '批量共享', 'import': '导入', 'export': '导出', 'template': '模板', 'wizard': '向导', 'advanced': '高级', 'simple': '简单', 'classic': '经典', 'modern': '现代', 'minimal': '极简', 'detailed': '详细', 'summary': '摘要', 'report': '报告', 'analytics': '分析', 'insights': '洞察', 'metrics': '指标', 'dashboard': '仪表板', 'visualization': '可视化', 'chart': '图表', 'graph': '图形', 'table': '表格', 'list': '列表', 'grid': '网格', 'calendar': '日历', 'timeline': '时间线', 'kanban': '看板', 'gantt': '甘特图', 'mind': '思维导图', 'flowchart': '流程图', 'diagram': '图表', 'whiteboard': '白板', 'canvas': '画布', 'collaboration': '协作', 'presence': '在线状态', 'cursor': '光标', 'selection': '选择', 'comment': '评论', 'annotation': '注释', 'mention': '提及', 'reaction': '反应', 'emoji': '表情符号', 'sticker': '贴纸', 'gif': 'GIF', 'attachment': '附件', 'version': '版本', 'history': '历史', 'compare': '比较', 'merge': '合并', 'conflict': '冲突', 'resolution': '解决', 'branch': '分支', 'fork': '分叉', 'pull': '拉取', 'push': '推送', 'clone': '克隆', 'download': '下载', 'upload': '上传', 'print': '打印', 'pdf': 'PDF', 'image': '图像', 'video': '视频', 'audio': '音频', 'document': '文档', 'spreadsheet': '电子表格', 'presentation': '演示文稿', 'code': '代码', 'text': '文本', 'rich': '富文本', 'markdown': 'Markdown', 'latex': 'LaTeX', 'html': 'HTML', 'xml': 'XML', 'json': 'JSON', 'yaml': 'YAML', 'csv': 'CSV', 'tsv': 'TSV', 'sql': 'SQL', 'sharedSuccess': '项目'{{projectName}}'已成功与{{email}}共享', 'inviteMembers': '邀请成员', 'email': '电子邮件', 'role': '角色', 'inviteError': '发送邀请时出错', 'publicAccess': '公共访问', 'privateAccess': '私人访问', 'readOnly': '只读', 'canDelete': '可以删除', 'member': '成员', 'removeAccess': '移除访问权限', 'confirmRemove': '您确定要移除此用户的访问权限吗？', 'failedToCopy': '复制链接失败', 'generateLink': '生成共享链接', 'expiresIn': '过期时间', 'never': '从不', 'oneDay': '1 天', 'oneWeek': '1 周', 'oneMonth': '1 个月'
  }, 'invitation': {
    'title': '项目邀请', 'loading': '正在加载邀请...', 'accepting': '正在接受邀请...', 'accepted': '邀请已接受', 'acceptedDescription': '您现在可以访问该项目。', 'expired': '邀请已过期', 'expiredDescription': '此邀请链接已过期或无效。', 'invalid': '无效的邀请', 'invalidDescription': '此邀请链接无效。', 'error': '接受邀请时出错', 'errorDescription': '处理您的邀请时出现问题。请稍后再试。', 'goToProject': '前往项目', 'backToDashboard': '返回仪表板', 'alreadyMember': '您已经是该项目的成员', 'alreadyMemberDescription': '您可以从仪表板访问此项目。', 'signInRequired': '需要登录', 'signInRequiredDescription': '请登录以接受此邀请。', 'signIn': '登录'
  }, 'about': {
    'title': '关于 SpherosegV4', 'subtitle': '先进的球体分割和分析', 'description': 'SpherosegV4 是一个用于 3D 球体培养物的自动分割和分析的尖端平台。我们的深度学习模型提供准确可靠的结果，用于您的研究需求。', 'team': {
      'title': '认识团队', 'description': '我们的跨学科专家团队', 'members': {
        'martin': {
          'name': 'Martin Maška', 'role': '首席研究员', 'bio': '生物图像分析和计算机视觉专家'
        }, 'tomas': {
          'name': 'Tomáš Sixta', 'role': '软件架构师', 'bio': '全栈开发和系统设计'
        }, 'jana': {
          'name': 'Jana Čmielová', 'role': '生物学家', 'bio': '细胞生物学和球体培养专家'
        }, 'marie': {
          'name': 'Marie Nováková', 'role': 'UI/UX 设计师', 'bio': '用户体验和界面设计'
        }
      }
    }, 'features': {
      'title': '主要特性', 'aiPowered': {
        'title': 'AI 驱动分析', 'description': '使用深度学习进行准确分割的先进算法'
      }, 'multiFormat': {
        'title': '多格式支持', 'description': '支持各种图像格式和导出选项'
      }, 'collaborative': {
        'title': '协作工作', 'description': '与您的团队共享项目和结果'
      }, 'automated': {
        'title': '自动化工作流', 'description': '批处理和自动分析管道'
      }
    }, 'contact': {
      'title': '联系我们', 'description': '有问题或反馈？我们很乐意听取您的意见。', 'email': '电子邮件', 'github': 'GitHub', 'documentation': '文档'
    }, 'version': '版本', 'license': '许可证', 'copyright': '© 2024 SpherosegV4。保留所有权利。'
  }, 'validation': {
    'required': '此字段是必需的', 'email': '请输入有效的电子邮件地址', 'minLength': '至少需要 {{min}} 个字符', 'maxLength': '最多 {{max}} 个字符', 'passwordMatch': '密码不匹配', 'strongPassword': '密码必须包含大写字母、小写字母、数字和特殊字符', 'invalidFormat': '格式无效', 'numberOnly': '仅限数字', 'alphanumeric': '仅限字母和数字', 'url': '请输入有效的 URL', 'date': '请输入有效的日期', 'fileSize': '文件大小不得超过 {{size}}', 'fileType': '不支持的文件类型', 'unique': '此值必须是唯一的'
  }, 'notifications': {
    'projectCreated': '项目创建成功', 'projectUpdated': '项目更新成功', 'projectDeleted': '项目删除成功', 'imageUploaded': '图片上传成功', 'imageDeleted': '图片删除成功', 'segmentationComplete': '分割完成', 'segmentationFailed': '分割失败', 'exportReady': '导出准备就绪', 'inviteSent': '邀请已发送', 'settingsUpdated': '设置已更新', 'passwordChanged': '密码已更改', 'profileUpdated': '个人资料已更新', 'error': '发生错误', 'success': '操作成功', 'info': '信息', 'warning': '警告'
  }
};
