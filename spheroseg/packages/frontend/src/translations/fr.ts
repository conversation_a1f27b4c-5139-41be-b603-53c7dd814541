// French translations
export default {
  // Segmentation context menu
  segmentation: {
    contextMenu: {
      editPolygon: 'Modifier le polygone',
      splitPolygon: 'Diviser le polygone',
      deletePolygon: 'Supprimer le polygone',
      confirmDeleteTitle: 'Êtes-vous sûr de vouloir supprimer le polygone ?',
      confirmDeleteMessage:
        'Cette action est irréversible. Le polygone sera supprimé définitivement de la segmentation.',
      duplicateVertex: 'Dupliquer le sommet',
      deleteVertex: 'Supprimer le sommet',
    },
    title: 'Éditeur de segmentation',
    resolution: '{width}x{height}',
    batch: {
      mixed: "Segmentation : {{successCount}} images mises en file d'attente avec succès, {{failCount}} échouées",
      allSuccess: "Segmentation : Toutes les {{count}} images mises en file d'attente avec succès",
      allFailed: 'Segmentation : Toutes les {{count}} images ont échoué',
    },
    queue: {
      title: "File d'attente de segmentation",
      summary: '{{total}} tâches au total ({{running}} en traitement, {{queued}} en attente)',
      noRunningTasks: 'Aucune tâche en cours',
      noQueuedTasks: 'Aucune tâche en attente',
      task: 'Tâche',
      statusRunning: 'Segmentation : {{count}} en cours{{queued}}',
      statusQueued: ', {{count}} en attente',
      statusOnlyQueued: 'Segmentation : {{count}} en attente',
      statusOnlyQueued_one: 'Segmentation : 1 en attente',
      statusOnlyQueued_other: 'Segmentation : {{count}} en attente',
      processing: 'En traitement',
      queued: 'En attente',
      statusProcessing: 'Segmentation : {{count}} en traitement',
      statusReady: 'Prêt',
      tasksTotal: '{{total}} tâches au total ({{running}} en traitement, {{queued}} en attente)',
    },
    selectPolygonForEdit: 'Sélectionnez un polygone à modifier',
    selectPolygonForSlice: 'Sélectionnez un polygone à diviser',
    selectPolygonForAddPoints: 'Sélectionnez un polygone pour ajouter des points',
    clickToAddPoint: 'Cliquez pour ajouter un point',
    clickToCompletePolygon: 'Cliquez sur le premier point pour fermer le polygone',
    clickToAddFirstSlicePoint: 'Cliquez pour ajouter le premier point de coupe',
    clickToAddSecondSlicePoint: 'Cliquez pour ajouter le second point de coupe',
    polygonCreationMode: 'Mode création de polygone',
    polygonEditMode: 'Mode édition de polygone',
    polygonSliceMode: 'Mode division de polygone',
    polygonAddPointsMode: 'Mode ajout de points',
    viewMode: 'Mode visualisation',
    totalPolygons: 'Total des polygones',
    totalVertices: 'Total des sommets',
    vertices: 'Sommets',
    zoom: 'Zoom',
    mode: 'Mode',
    selected: 'Sélectionné',
    none: 'Aucun',
    polygons: 'Polygones',
    imageNotFound: 'Image introuvable',
    returnToProject: 'Retour au projet',
    backToProject: 'Retour au projet',
    previousImage: 'Image précédente',
    nextImage: 'Image suivante',
    toggleShortcuts: 'Afficher les raccourcis',
    modes: {
      view: 'Mode visualisation',
      edit: 'Mode édition',
      create: 'Mode création',
      slice: 'Mode division',
      addPoints: 'Mode ajout de points',
      deletePolygon: 'Mode suppression de polygone',
      createPolygon: 'Mode création de polygone',
      editVertices: 'Mode édition des sommets',
      editMode: 'Mode édition',
      slicingMode: 'Mode division',
      pointAddingMode: 'Mode ajout de points',
    },
    status: {
      processing: 'En traitement',
      queued: 'En attente',
      completed: 'Terminé',
      failed: 'Échoué',
      pending: 'En attente',
      withoutSegmentation: 'Sans segmentation',
    },
    autoSave: {
      enabled: 'Sauvegarde auto : Activée',
      disabled: 'Sauvegarde auto : Désactivée',
      idle: 'Sauvegarde auto : Inactive',
      pending: 'En attente...',
      saving: 'Sauvegarde...',
      success: 'Sauvegardé',
      error: 'Erreur',
    },
    loading: 'Chargement de la segmentation...',
    polygon: 'Polygone',
    unsavedChanges: 'Modifications non sauvegardées',
    noData: 'Aucune donnée de segmentation disponible',
    noPolygons: 'Aucun polygone trouvé',
    regions: 'Segmentation',
    position: 'Position',
    polygonDeleted: 'Polygone supprimé avec succès',
    saveSuccess: 'Segmentation sauvegardée avec succès',
    resegmentSuccess: 'Resegmentation démarrée avec succès',
    resegmentComplete: 'Resegmentation terminée avec succès',
    resegmentError: "Échec de la resegmentation de l'image",
    resegmentButton: 'Resegmenter',
    completedSegmentation: 'Terminé',
    resegmentButtonTooltip: 'Resegmenter avec le réseau de neurones',
    processingImage: "Traitement de l'image...",
    helpTips: {
      title: 'Conseils :',
      edit: {
        createPoint: 'Cliquez pour créer un nouveau point',
        shiftPoints: 'Maintenez Shift pour créer automatiquement une séquence de points',
        closePolygon: 'Fermez le polygone en cliquant sur le premier point',
      },
      slice: {
        start: 'Cliquez pour commencer la coupe',
        finish: 'Cliquez à nouveau pour terminer la coupe',
        cancel: 'Échap pour annuler la coupe',
      },
      addPoint: {
        hover: 'Survolez la ligne du polygone',
        click: 'Cliquez pour ajouter un point au polygone sélectionné',
        exit: 'Échap pour quitter le mode ajout',
      },
      view: {
        pan: 'Panoramique : Cliquez et faites glisser',
        selectPolygon: 'Sélectionner : Cliquez sur le polygone',
        zoom: 'Zoom : Molette de la souris',
      },
    },
    imageNotFoundDescription: "L'image demandée est introuvable",
    invalidImageDimensions: "Dimensions d'image invalides",
    noDataToSave: 'Aucune modification à enregistrer',
    polygonDuplicated: 'Polygone dupliqué',
    polygonNotFound: 'Polygone introuvable',
    polygonSimplified: 'Polygone simplifié',
    polygonSimplifyFailed: 'Échec de la simplification du polygone',
    polygonSliced: 'Polygone divisé avec succès',
    resegment: {
      error: {
        exception: 'Erreur de resegmentation : {{error}}',
        failed: 'La resegmentation a échoué',
        missingData: 'Données requises manquantes pour la resegmentation',
      },
      success: 'Resegmentation terminée avec succès',
    },
    resegmentMultipleError: 'Erreur lors de la resegmentation de plusieurs images',
    resegmentMultipleSuccess: 'Plusieurs images resegmentées avec succès',
    resegmenting: 'Resegmentation...',
    resegmentingMultiple: 'Resegmentation de plusieurs images...',
    saveError: 'Erreur lors de la sauvegarde de la segmentation',
    segmentationLoading: 'Chargement de la segmentation...',
    segmentationPolygon: 'Polygone de segmentation',
    selectPolygonFirst: "Veuillez d'abord sélectionner un polygone",
    sliceFailed: 'Échec de la division du polygone',
    undoRestored: 'Action annulée',
    undoWhileDraggingError: "Impossible d'annuler pendant le glissement",
    vertexDeleteFailed: 'Échec de la suppression du sommet',
    vertexDeleted: 'Sommet supprimé',
    vertexDuplicateFailed: 'Échec de la duplication du sommet',
    vertexDuplicated: 'Sommet dupliqué',
  },
  // Error messages
  errors: {
    somethingWentWrong: "Une erreur s'est produite",
    componentError: "Une erreur s'est produite dans ce composant",
    errorDetails: "Détails de l'erreur",
    tryAgain: 'Réessayer',
    reloadPage: 'Recharger la page',
    goBack: 'Retour',
    notFound: 'Page introuvable',
    pageNotFoundMessage: 'La page demandée est introuvable',
    returnToHome: "Retour à l'accueil",
    unauthorized: 'Accès non autorisé',
    forbidden: 'Accès interdit',
    serverError: 'Erreur serveur',
    networkError: 'Erreur réseau',
    timeoutError: "Délai d'attente dépassé",
    validationError: 'Erreur de validation',
    unknownError: 'Erreur inconnue',
    goHome: "Aller à la page d'accueil",
    fetchSegmentationFailed: 'Échec du chargement de la segmentation',
    fetchImageFailed: "Échec du chargement de l'image",
    saveSegmentationFailed: 'Échec de la sauvegarde de la segmentation',
    missingPermissions: 'Permissions insuffisantes',
    invalidInput: 'Entrée invalide',
    resourceNotFound: 'Ressource introuvable',
  },
  project: {
    detail: {
      noImagesSelected: 'Aucune image sélectionnée',
      triggeringResegmentation: 'Déclenchement de la resegmentation pour {{count}} images...',
      deleteConfirmation:
        'Êtes-vous sûr de vouloir supprimer {{count}} images ? Cette action ne peut pas être annulée.',
      deletingImages: 'Suppression de {{count}} images...',
      deleteSuccess: '{{count}} images supprimées avec succès',
      deleteFailed: 'Échec de la suppression de {{count}} images',
      preparingExport: "Préparation de l'export de {{count}} images...",
    },
    segmentation: {
      processingInBatches: 'Démarrage de la segmentation pour {{count}} images en {{batches}} lots...',
      batchQueued: "Lot {{current}}/{{total}} mis en file d'attente avec succès",
      batchQueuedFallback:
        "Lot {{current}}/{{total}} mis en file d'attente avec succès (point de terminaison de secours)",
      batchError: 'Erreur lors du traitement du lot {{current}}/{{total}}',
      partialSuccess: "Segmentation : {{success}} images mises en file d'attente avec succès, {{failed}} échouées",
      allSuccess: "Segmentation : Toutes les {{count}} images mises en file d'attente avec succès",
      allFailed: 'Segmentation : Toutes les {{count}} images ont échoué',
      startedImages: 'Segmentation démarrée pour {{count}} images',
      queuedLocallyWarning:
        "Segmentation mise en file d'attente localement pour {{count}} images. Connexion serveur échouée.",
    },
    loading: 'Chargement du projet...',
    notFound: 'Projet introuvable',
    error: 'Erreur lors du chargement du projet',
    empty: 'Ce projet est vide',
    noImagesText: 'Aucune image trouvée dans ce projet',
    addImages: 'Ajoutez des images pour commencer',
    noImages: {
      title: "Pas encore d'images",
      description: "Ce projet ne contient pas encore d'images. Téléchargez des images pour commencer.",
      uploadButton: 'Télécharger des images',
    },
    deleteProject: 'Supprimer le projet',
    deleteConfirmation:
      'Êtes-vous sûr de vouloir supprimer le projet "{{projectName}}" ? Cette action ne peut pas être annulée.',
    resegmentImage: "Resegmenter l'image",
    deleteImage: "Supprimer l'image",
  },
  projectsPage: {
    title: 'Projets',
    description: 'Gérer les projets de recherche',
    createNew: 'Créer un nouveau projet',
    createProject: 'Créer un projet',
    createProjectDesc: 'Démarrer un nouveau projet de recherche',
    projectName: 'Nom du projet',
    projectDescription: 'Description du projet',
    projectNamePlaceholder: 'Entrez le nom du projet',
    projectDescriptionPlaceholder: 'Entrez la description du projet',
    projectCreated: 'Projet créé avec succès',
    projectCreationFailed: 'Échec de la création du projet',
    projectDeleted: 'Projet supprimé avec succès',
    projectDeletionFailed: 'Échec de la suppression du projet',
    confirmDelete: 'Êtes-vous sûr de vouloir supprimer ce projet ?',
    confirmDeleteDescription:
      'Cette action ne peut pas être annulée. Toutes les données associées à ce projet seront définitivement supprimées.',
    deleteProject: 'Supprimer le projet',
    editProject: 'Modifier le projet',
    viewProject: 'Voir le projet',
    projectUpdated: 'Projet mis à jour avec succès',
    projectUpdateFailed: 'Échec de la mise à jour du projet',
    noProjects: 'Aucun projet trouvé',
    createFirstProject: 'Créez votre premier projet pour commencer',
    searchProjects: 'Rechercher des projets...',
    filterProjects: 'Filtrer les projets',
    sortProjects: 'Trier les projets',
    projectNameRequired: 'Le nom du projet est requis',
    loginRequired: 'Vous devez être connecté pour créer un projet',
    createdAt: 'Créé',
    updatedAt: 'Dernière mise à jour',
    imageCount: 'Images',
    status: 'Statut',
    actions: 'Actions',
    loading: 'Chargement des projets...',
    error: 'Erreur lors du chargement des projets',
    retry: 'Réessayer',
    duplicating: 'Duplication du projet...',
    duplicate: 'Dupliquer',
    duplicateSuccess: 'Projet dupliqué avec succès',
    duplicateFailed: 'Échec de la duplication du projet',
    duplicateTitle: 'Dupliquer le projet',
    duplicateProject: 'Dupliquer le projet',
    duplicateProjectDescription:
      'Créez une copie de ce projet incluant toutes les images. Vous pouvez personnaliser les options ci-dessous.',
    duplicateCancelled: 'Duplication du projet annulée',
    duplicatingProject: 'Duplication du projet',
    duplicatingProjectDescription: 'Votre projet est en cours de duplication. Cela peut prendre quelques instants.',
    duplicateProgress: 'Progression de la duplication',
    duplicationComplete: 'Duplication du projet terminée',
    duplicationTaskFetchError: 'Erreur lors de la récupération des données de la tâche',
    duplicationCancelError: "Erreur lors de l'annulation de la duplication",
    duplicateProgressDescription:
      'Votre projet est en cours de duplication. Ce processus peut prendre du temps pour les grands projets.',
    duplicationPending: 'En attente',
    duplicationProcessing: 'En traitement',
    duplicationCompleted: 'Terminé',
    duplicationFailed: 'Échoué',
    duplicationCancelled: 'Annulé',
    duplicationCancellationFailed: "Échec de l'annulation de la duplication",
    duplicationSuccessMessage: 'Projet dupliqué avec succès ! Vous pouvez maintenant accéder au nouveau projet.',
    copySegmentations: 'Copier les résultats de segmentation',
    resetImageStatus: 'Réinitialiser le statut de traitement des images',
    newProjectTitle: 'Nouveau titre du projet',
    itemsProcessed: 'éléments traités',
    items: 'éléments',
    unknownProject: 'Projet inconnu',
    activeTasks: 'Actif',
    allTasks: 'Tout',
    noActiveDuplications: 'Aucune duplication active',
    noDuplications: 'Aucune tâche de duplication trouvée',
    deleteProjectDescription: 'Cette action supprimera définitivement le projet et toutes les données associées.',
    deleteWarning:
      'Cette action ne peut pas être annulée. Toutes les données associées à ce projet seront définitivement supprimées.',
    untitledProject: 'Projet sans titre',
    typeToConfirm: 'Tapez "delete" pour confirmer',
    deleteConfirm: 'Êtes-vous sûr de vouloir supprimer ce projet ?',
    exportProject: 'Exporter le projet',
    archived: 'Archivé',
    completed: 'Terminé',
    draft: 'Brouillon',
    active: 'Actif',
    createDate: 'Créé',
    lastModified: 'Dernière modification',
    projectDescPlaceholder: 'Entrez la description du projet',
    creatingProject: 'Création du projet...',
    noImages: {
      title: "Pas encore d'images",
      description: "Ce projet ne contient pas encore d'images. Téléchargez des images pour commencer la segmentation.",
      uploadButton: 'Télécharger des images',
    },
  },
  common: {
    appName: 'Segmentation de sphéroïdes',
    appNameShort: 'SpheroSeg',
    loading: 'Chargement...',
    loadingAccount: 'Chargement de votre compte...',
    loadingApplication: "Chargement de l'application...",
    selectAll: 'Tout sélectionner',
    deselectAll: 'Tout désélectionner',
    save: 'Enregistrer',
    cancel: 'Annuler',
    delete: 'Supprimer',
    edit: 'Modifier',
    create: 'Créer',
    search: 'Rechercher',
    error: 'Erreur',
    success: 'Succès',
    reset: 'Réinitialiser',
    clear: 'Effacer',
    close: 'Fermer',
    back: 'Retour',
    signIn: 'Se connecter',
    signUp: "S'inscrire",
    signOut: 'Se déconnecter',
    signingIn: 'Connexion...',
    settings: 'Paramètres',
    profile: 'Profil',
    dashboard: 'Tableau de bord',
    project: 'Projet',
    projects: 'Projets',
    newProject: 'Nouveau projet',
    upload: 'Télécharger',
    download: 'Télécharger',
    removeAll: 'Tout supprimer',
    uploadImages: 'Télécharger des images',
    recentAnalyses: 'Analyses récentes',
    noProjects: 'Aucun projet trouvé',
    noImages: 'Aucune image trouvée',
    createYourFirst: 'Créez votre premier projet pour commencer',
    tryAgain: 'Réessayer',
    email: 'E-mail',
    password: 'Mot de passe',
    confirmPassword: 'Confirmer le mot de passe',
    firstName: 'Prénom',
    lastName: 'Nom',
    username: "Nom d'utilisateur",
    name: 'Nom',
    description: 'Description',
    date: 'Date',
    status: 'Statut',
    image: 'Image',
    projectName: 'Nom du projet',
    projectDescription: 'Description du projet',
    language: 'Langue',
    theme: 'Thème',
    light: 'Clair',
    dark: 'Sombre',
    system: 'Système',
    welcome: 'Bienvenue sur la plateforme de segmentation de sphéroïdes',
    account: 'Compte',
    passwordConfirm: 'Confirmer le mot de passe',
    manageAccount: 'Gérer le compte',
    changePassword: 'Changer le mot de passe',
    deleteAccount: 'Supprimer le compte',
    requestAccess: "Demander l'accès",
    accessRequest: "Demande d'accès",
    createAccount: 'Créer un compte',
    signInToAccount: 'Se connecter au compte',
    termsOfService: "Conditions d'utilisation",
    privacyPolicy: 'Politique de confidentialité',
    termsOfServiceLink: "Conditions d'utilisation",
    privacyPolicyLink: 'Politique de confidentialité',
    optional: 'Optionnel',
    saveChanges: 'Enregistrer les modifications',
    saving: 'Enregistrement',
    notSpecified: 'Non spécifié',
    enable: 'Activer',
    disable: 'Désactiver',
    backToHome: "Retour à l'accueil",
    and: 'et',
    lastChange: 'Dernière modification',
    sort: 'Trier',
    emailPlaceholder: 'Entrez votre e-mail',
    passwordPlaceholder: 'Entrez votre mot de passe',
    export: 'Exporter',
    selectImages: 'Sélectionner des images',
    noImagesDescription: 'Téléchargez des images pour commencer votre projet',
    yes: 'Oui',
    no: 'Non',
    images: 'Images',
    files: 'Fichiers',
    validationFailed: 'Validation échouée',
    cropAvatar: 'Recadrer la photo de profil',
    profileTitle: 'Profil',
    profileDescription: 'Mettez à jour vos informations de profil visibles par les autres utilisateurs',
    profileUsername: "Nom d'utilisateur",
    profileUsernamePlaceholder: "Entrez votre nom d'utilisateur",
    profileFullName: 'Nom complet',
    profileFullNamePlaceholder: 'Entrez votre nom complet',
    profileTitlePlaceholder: 'ex. Chercheur, Professeur',
    profileOrganization: 'Organisation',
    profileOrganizationPlaceholder: 'Entrez votre organisation ou institution',
    profileBio: 'Biographie',
    profileBioPlaceholder: 'Écrivez une courte biographie',
    profileBioDescription: 'Brève description de vos intérêts de recherche et expertise',
    profileLocation: 'Localisation',
    profileLocationPlaceholder: 'ex. Prague, République tchèque',
    profileSaveButton: 'Enregistrer le profil',
    actions: 'Actions',
    view: 'Voir',
    share: 'Partager',
    projectNamePlaceholder: 'Entrez le nom du projet',
    projectDescPlaceholder: 'Entrez la description du projet',
    creatingProject: 'Création du projet...',
    createSuccess: 'Projet créé avec succès',
    unauthorized: "Vous n'êtes pas autorisé à effectuer cette action",
    forbidden: 'Accès interdit',
    maxFileSize: 'Taille max : {{size}}MB',
    accepted: 'Accepté',
    processing: 'Traitement...',
    uploading: 'Téléchargement...',
    uploadComplete: 'Téléchargement terminé',
    uploadFailed: 'Échec du téléchargement',
    deletePolygon: 'Supprimer le polygone',
    pleaseLogin: 'Veuillez vous connecter pour continuer',
    retry: 'Réessayer',
    segmentation: 'Segmentation',
    copiedToClipboard: 'Copié dans le presse-papiers !',
    failedToCopy: 'Échec de la copie dans le presse-papiers',
    confirm: 'Confirmer',
    editor: {
      error: 'Erreur',
      success: 'Succès',
      edit: 'Modifier',
      create: 'Créer',
    },
  },
  auth: {
    signIn: 'Se connecter',
    signUp: "S'inscrire",
    signOut: 'Se déconnecter',
    signingIn: 'Connexion...',
    email: 'E-mail',
    password: 'Mot de passe',
    forgotPassword: 'Mot de passe oublié ?',
    resetPassword: 'Réinitialiser le mot de passe',
    dontHaveAccount: "Vous n'avez pas de compte ?",
    alreadyHaveAccount: 'Vous avez déjà un compte ?',
    createAccount: 'Créer un compte',
    signInWithGoogle: 'Se connecter avec Google',
    signInWithGithub: 'Se connecter avec GitHub',
    or: 'ou',
    signInTitle: 'Connexion',
    signInDescription: 'Connectez-vous à votre compte',
    noAccount: 'Pas de compte ?',
    emailAddressLabel: 'Adresse e-mail',
    passwordLabel: 'Mot de passe',
    currentPasswordLabel: 'Mot de passe actuel',
    newPasswordLabel: 'Nouveau mot de passe',
    confirmPasswordLabel: 'Confirmer le mot de passe',
    rememberMe: 'Se souvenir de moi',
    emailRequired: "L'e-mail est requis",
    passwordRequired: 'Le mot de passe est requis',
    alreadyLoggedInTitle: 'Vous êtes déjà connecté',
    alreadyLoggedInMessage: 'Vous êtes déjà connecté à votre compte',
    goToDashboardLink: 'Aller au tableau de bord',
    invalidEmail: 'Adresse e-mail invalide',
    passwordTooShort: 'Le mot de passe doit contenir au moins 6 caractères',
    passwordsDontMatch: 'Les mots de passe ne correspondent pas',
    invalidCredentials: 'E-mail ou mot de passe invalide',
    accountCreated: 'Compte créé avec succès',
    resetLinkSent: 'Lien de réinitialisation du mot de passe envoyé à votre e-mail',
    resetSuccess: 'Mot de passe réinitialisé avec succès',
    signInSuccess: 'Connexion réussie',
    signOutSuccess: 'Déconnexion réussie',
    sessionExpired: 'Votre session a expiré. Veuillez vous reconnecter.',
    unauthorized: "Vous n'êtes pas autorisé à accéder à cette ressource",
    verifyEmail: 'Veuillez vérifier votre adresse e-mail',
    verificationLinkSent: 'Lien de vérification envoyé à votre e-mail',
    verificationSuccess: 'E-mail vérifié avec succès',
    resendVerification: "Renvoyer l'e-mail de vérification",
    requestAccess: "Demander l'accès",
    termsAndPrivacy:
      "En vous inscrivant, vous acceptez nos conditions d'utilisation et notre politique de confidentialité.",
    forgotPasswordLink: 'Mot de passe oublié ?',
    passwordChanged: 'Mot de passe modifié avec succès',
    currentPasswordIncorrect: 'Le mot de passe actuel est incorrect',
    registerTitle: 'Créer un compte',
    registerDescription: 'Inscrivez-vous pour un nouveau compte',
    registerSuccess: 'Inscription réussie ! Vous pouvez maintenant vous connecter.',
    emailPlaceholder: 'Entrez votre e-mail',
    passwordPlaceholder: 'Entrez votre mot de passe',
    firstNamePlaceholder: 'ex. Jean',
    lastNamePlaceholder: 'ex. Dupont',
    passwordConfirmPlaceholder: 'Confirmez votre mot de passe',
    signUpTitle: 'Créer un compte',
    signUpDescription: 'Inscrivez-vous pour un nouveau compte',
    enterInfoCreateAccount: 'Entrez vos informations pour créer un compte',
    creatingAccount: 'Création du compte...',
    emailAlreadyExists: 'Cet e-mail est déjà enregistré. Veuillez utiliser un autre e-mail ou vous connecter.',
    emailHasPendingRequest: "Cet e-mail a déjà une demande d'accès en attente. Veuillez attendre l'approbation.",
    signUpSuccess: 'Inscription réussie !',
    signUpSuccessEmail:
      "Inscription réussie ! Veuillez vérifier votre e-mail ou attendre l'approbation de l'administrateur.",
    signUpFailed: "Échec de l'inscription. Veuillez réessayer.",
    alreadyHaveAccess: 'Déjà un accès ?',
    forgotPasswordTitle: 'Réinitialiser votre mot de passe',
    checkYourEmail: 'Vérifiez votre e-mail pour un nouveau mot de passe',
    enterEmailForReset: 'Entrez votre adresse e-mail et nous vous enverrons un nouveau mot de passe',
    passwordResetLinkSent: 'Si un compte existe pour cet e-mail, un nouveau mot de passe a été envoyé',
    passwordResetFailed: "Échec de l'envoi du nouveau mot de passe. Veuillez réessayer.",
    enterEmail: 'Veuillez entrer votre adresse e-mail',
    sendingResetLink: 'Envoi du nouveau mot de passe...',
    sendResetLink: 'Envoyer un nouveau mot de passe',
    backToSignIn: 'Retour à la connexion',
    accountLocked: 'Votre compte a été verrouillé. Veuillez contacter le support.',
    fillAllFields: 'Veuillez remplir tous les champs obligatoires',
    serverError: 'Erreur serveur. Veuillez réessayer plus tard.',
    signInError: 'Erreur de connexion',
    signInFailed: 'Échec de la connexion. Veuillez vérifier vos identifiants.',
  },
  requestAccess: {
    and: 'et',
    title: "Demander l'accès à la plateforme de segmentation de sphéroïdes",
    description:
      "Remplissez le formulaire suivant pour demander l'accès à notre plateforme. Nous examinerons votre demande et vous contacterons bientôt.",
    emailLabel: 'Votre adresse e-mail',
    nameLabel: 'Votre nom',
    institutionLabel: 'Institution/Entreprise',
    reasonLabel: "Raison de l'accès",
    submitRequest: 'Soumettre la demande',
    requestReceived: 'Demande reçue',
    thankYou: 'Merci de votre intérêt',
    weWillContact: 'Nous examinerons votre demande et vous contacterons bientôt',
    submitSuccess: 'Demande soumise avec succès !',
    emailPlaceholder: 'Entrez votre adresse e-mail',
    namePlaceholder: 'Entrez votre nom complet',
    institutionPlaceholder: 'Entrez le nom de votre institution ou entreprise',
    reasonPlaceholder: "Veuillez décrire comment vous prévoyez d'utiliser la plateforme",
    fillRequired: 'Veuillez remplir tous les champs obligatoires',
    submittingRequest: 'Soumission de la demande...',
    submitError: 'Échec de la soumission de la demande',
    alreadyPending: "Une demande d'accès pour cet e-mail est déjà en attente",
    agreeToTerms: 'En soumettant cette demande, vous acceptez nos',
  },
  requestAccessForm: {
    title: "Demander l'accès à la plateforme de segmentation de sphéroïdes",
    description:
      "Remplissez le formulaire suivant pour demander l'accès à notre plateforme. Nous examinerons votre demande et vous contacterons bientôt.",
    emailLabel: 'Votre adresse e-mail',
    nameLabel: 'Votre nom',
    institutionLabel: 'Institution/Entreprise',
    reasonLabel: "Raison de l'accès",
    submitButton: 'Soumettre la demande',
    signInPrompt: 'Déjà un compte ?',
    signInLink: 'Se connecter',
    thankYouTitle: 'Merci de votre intérêt',
    weWillContact: 'Nous examinerons votre demande et vous contacterons bientôt',
    agreeToTerms: 'En soumettant cette demande, vous acceptez nos',
    and: 'et',
  },
  documentation: {
    tag: 'Guide utilisateur',
    title: 'Documentation SpheroSeg',
    subtitle: 'Apprenez à utiliser efficacement la plateforme de segmentation de sphéroïdes.',
    sidebar: {
      title: 'Sections',
      introduction: 'Introduction',
      gettingStarted: 'Démarrage',
      uploadingImages: "Téléchargement d'images",
      segmentationProcess: 'Processus de segmentation',
      apiReference: 'Référence API',
    },
    introduction: {
      title: 'Introduction',
      imageAlt: "Illustration du flux de travail d'analyse des sphéroïdes",
      whatIs: {
        title: "Qu'est-ce que SpheroSeg ?",
        paragraph1:
          "SpheroSeg est une plateforme de pointe conçue pour la segmentation et l'analyse de sphéroïdes cellulaires dans des images microscopiques. Notre outil offre aux chercheurs des capacités de détection et d'analyse précises.",
        paragraph2:
          "Il utilise des algorithmes IA avancés basés sur l'apprentissage profond pour identifier et segmenter automatiquement les sphéroïdes dans vos images avec une grande précision et cohérence.",
        paragraph3:
          "Cette documentation vous guidera à travers tous les aspects de l'utilisation de la plateforme, du démarrage aux fonctionnalités avancées et à l'intégration API.",
      },
    },
    gettingStarted: {
      title: 'Démarrage',
      accountCreation: {
        title: 'Création de compte',
        paragraph1:
          'Pour utiliser SpheroSeg, vous devez créer un compte. Cela nous permet de stocker en toute sécurité vos projets et images.',
        step1Prefix: 'Visitez la',
        step1Link: "page d'inscription",
        step2: 'Entrez votre adresse e-mail institutionnelle et créez un mot de passe',
        step3: 'Complétez votre profil avec votre nom et votre institution',
        step4: 'Vérifiez votre adresse e-mail via le lien envoyé dans votre boîte de réception',
      },
      creatingProject: {
        title: 'Créer votre premier projet',
        paragraph1:
          'Les projets vous aident à organiser votre travail. Chaque projet peut contenir plusieurs images et leurs résultats de segmentation correspondants.',
        step1: 'Sur votre tableau de bord, cliquez sur "Nouveau projet"',
        step2: 'Entrez un nom et une description de projet',
        step3: 'Sélectionnez le type de projet (par défaut : Analyse de sphéroïdes)',
        step4: 'Cliquez sur "Créer le projet" pour continuer',
      },
    },
    uploadingImages: {
      title: "Téléchargement d'images",
      paragraph1:
        "SpheroSeg prend en charge divers formats d'images couramment utilisés en microscopie, notamment TIFF, PNG et JPEG.",
      methods: {
        title: 'Méthodes de téléchargement',
        paragraph1: 'Il existe plusieurs façons de télécharger des images :',
        step1: 'Glissez et déposez des fichiers directement dans la zone de téléchargement',
        step2:
          'Cliquez sur la zone de téléchargement pour parcourir et sélectionner des fichiers depuis votre ordinateur',
        step3: 'Téléchargez plusieurs images en lot à la fois',
      },
      note: {
        prefix: 'Note :',
        text: "Pour des résultats optimaux, assurez-vous que vos images de microscopie ont un bon contraste entre le sphéroïde et l'arrière-plan.",
      },
    },
    segmentationProcess: {
      title: 'Processus de segmentation',
      paragraph1:
        'Le processus de segmentation identifie les limites des sphéroïdes dans vos images, permettant une analyse précise de leur morphologie.',
      automatic: {
        title: 'Segmentation automatique',
        paragraph1:
          "Notre segmentation automatique alimentée par l'IA peut détecter les limites des sphéroïdes avec une grande précision :",
        step1: 'Sélectionnez une image de votre projet',
        step2: 'Cliquez sur "Auto-segmenter" pour lancer le processus',
        step3: "Le système traitera l'image et affichera les limites détectées",
        step4: "Examinez les résultats dans l'éditeur de segmentation",
      },
      manual: {
        title: 'Ajustements manuels',
        paragraph1:
          'Parfois, la segmentation automatique peut nécessiter des ajustements. Notre éditeur fournit des outils pour :',
        step1: 'Ajouter ou supprimer des sommets le long de la limite',
        step2: 'Ajuster les positions des sommets pour des limites plus précises',
        step3: 'Diviser ou fusionner des régions',
        step4: "Ajouter ou supprimer des trous à l'intérieur des sphéroïdes",
      },
    },
    apiReference: {
      title: 'Référence API',
      paragraph1:
        "SpheroSeg offre une API RESTful pour un accès programmatique aux fonctionnalités de la plateforme. C'est idéal pour l'intégration avec vos flux de travail existants ou le traitement par lots.",
      endpoint1Desc: 'Récupère une liste de tous vos projets',
      endpoint2Desc: "Récupère toutes les images d'un projet spécifique",
      endpoint3Desc: 'Lance la segmentation pour une image spécifique',
      contactPrefix:
        "Pour la documentation complète de l'API et les détails d'authentification, veuillez nous contacter à",
    },
    backToHome: "Retour à l'accueil",
    backToTop: 'Retour en haut',
  },
  hero: {
    platformTag: 'Plateforme avancée de segmentation de sphéroïdes',
    title: "Analyse cellulaire alimentée par l'IA pour la recherche biomédicale",
    subtitle:
      "Améliorez votre analyse d'images cellulaires microscopiques avec notre plateforme de segmentation de sphéroïdes de pointe. Conçue pour les chercheurs recherchant précision et efficacité.",
    getStartedButton: 'Commencer',
    learnMoreButton: 'En savoir plus',
    imageAlt1: 'Image de microscopie de sphéroïde',
    imageAlt2: 'Image de microscopie de sphéroïde avec analyse',
    welcomeTitle: 'Bienvenue sur SpheroSeg',
    welcomeSubtitle: "Plateforme avancée pour la segmentation et l'analyse de sphéroïdes cellulaires",
    welcomeDescription:
      "Notre plateforme combine des algorithmes d'intelligence artificielle de pointe avec une interface intuitive pour une détection et une analyse précises des sphéroïdes cellulaires dans des images microscopiques.",
    featuresTitle: 'Fonctionnalités puissantes',
    featuresSubtitle: 'Outils avancés pour la recherche biomédicale',
    featureAiSegmentation: 'Segmentation avancée',
    featureAiSegmentationDesc:
      'Détection précise des sphéroïdes avec analyse des limites pour des mesures cellulaires précises.',
    featureEditing: "Analyse alimentée par l'IA",
    featureEditingDesc:
      "Exploitez les algorithmes d'apprentissage profond pour la détection et la classification automatisées des cellules.",
    featureAnalytics: 'Téléchargement facile',
    featureAnalyticsDesc: 'Glissez et déposez vos images de microscopie pour un traitement et une analyse immédiats.',
    featureExport: 'Aperçus statistiques',
    featureExportDesc: 'Métriques et visualisations complètes pour extraire des modèles de données significatifs.',
    ctaTitle: "Prêt à transformer votre flux de travail d'analyse cellulaire ?",
    ctaSubtitle:
      'Rejoignez les chercheurs de premier plan qui utilisent déjà notre plateforme pour accélérer leurs découvertes.',
    ctaButton: 'Créer un compte',
  },
  navbar: {
    home: 'Accueil',
    features: 'Fonctionnalités',
    documentation: 'Documentation',
    terms: 'Conditions',
    privacy: 'Confidentialité',
    login: 'Se connecter',
    requestAccess: "Demander l'accès",
    openMobileMenu: 'Ouvrir le menu mobile',
    closeMobileMenu: 'Fermer le menu mobile',
  },
  navigation: {
    home: 'Accueil',
    projects: 'Projets',
    settings: 'Paramètres',
    profile: 'Profil',
    dashboard: 'Tableau de bord',
    back: 'Retour',
  },
  dashboard: {
    manageProjects: 'Gérez et organisez vos projets de recherche',
    viewMode: {
      grid: 'Vue grille',
      list: 'Vue liste',
    },
    sort: {
      name: 'Nom',
      updatedAt: 'Dernière mise à jour',
      segmentationStatus: 'Statut',
    },
    search: 'Rechercher des projets...',
    searchImagesPlaceholder: 'Rechercher des images...',
    noProjects: 'Aucun projet trouvé',
    noImagesDescription: 'Aucune image ne correspond à vos critères de recherche',
    createFirst: 'Créez votre premier projet pour commencer',
    createNew: 'Créer un nouveau projet',
    lastChange: 'Dernière modification',
    statsOverview: 'Aperçu des statistiques',
    totalProjects: 'Total des projets',
    activeProjects: 'Projets actifs',
    totalImages: 'Total des images',
    totalAnalyses: 'Total des analyses',
    lastUpdated: 'Dernière mise à jour',
    noProjectsDescription: "Vous n'avez pas encore créé de projets. Créez votre premier projet pour commencer.",
    searchProjectsPlaceholder: 'Rechercher des projets par nom...',
    sortBy: 'Trier par',
    name: 'Nom',
    completed: 'Terminé',
    processing: 'En traitement',
    pending: 'En attente',
    failed: 'Échoué',
    selectImagesButton: 'Sélectionner des images',
  },
  projects: {
    title: 'Projets',
    description: 'Gérez vos projets de recherche',
    createNew: 'Créer un nouveau projet',
    createProject: 'Créer un projet',
    createProjectDesc: 'Créez un nouveau projet pour commencer à travailler avec des images et la segmentation.',
    projectName: 'Nom du projet',
    projectDescription: 'Description du projet',
    projectNamePlaceholder: 'Entrez le nom du projet',
    projectDescriptionPlaceholder: 'Entrez la description du projet',
    projectCreated: 'Projet créé avec succès',
    projectCreationFailed: 'Échec de la création du projet',
    projectDeleted: 'Projet supprimé avec succès',
    deleteSuccess: 'Projet supprimé avec succès',
    deleteFailed: 'Échec de la suppression du projet',
    deleting: 'Suppression du projet...',
    notFound: 'Projet introuvable. Il a peut-être déjà été supprimé.',
    missingId: 'Impossible de supprimer le projet : identifiant de projet manquant',
    projectDeletionFailed: 'Échec de la suppression du projet',
    confirmDelete: 'Êtes-vous sûr de vouloir supprimer ce projet ?',
    confirmDeleteDescription:
      'Cette action ne peut pas être annulée. Toutes les données associées à ce projet seront définitivement supprimées.',
    delete: 'Supprimer',
    deleteProject: 'Supprimer le projet',
    deleteProjectDescription:
      'Cette action ne peut pas être annulée. Cela supprimera définitivement le projet et toutes les données associées.',
    deleteWarning: 'Vous êtes sur le point de supprimer le projet suivant :',
    typeToConfirm: 'Tapez le nom du projet pour confirmer',
    confirmDeleteError: 'Veuillez taper exactement le nom du projet pour confirmer',
    editProject: 'Modifier le projet',
    viewProject: 'Voir le projet',
    projectUpdated: 'Projet mis à jour avec succès',
    projectUpdateFailed: 'Échec de la mise à jour du projet',
    noProjects: 'Aucun projet trouvé',
    createFirstProject: 'Créez votre premier projet pour commencer',
    searchProjects: 'Rechercher des projets...',
    filterProjects: 'Filtrer les projets',
    sortProjects: 'Trier les projets',
    projectNameRequired: 'Le nom du projet est requis',
    loginRequired: 'Vous devez être connecté pour créer un projet',
    createdAt: 'Créé',
    updatedAt: 'Dernière mise à jour',
    imageCount: 'Images',
    status: 'Statut',
    actions: 'Actions',
    loading: 'Chargement des projets...',
    error: 'Erreur lors du chargement des projets',
    retry: 'Réessayer',
    duplicating: 'Duplication du projet...',
    duplicate: 'Dupliquer',
    duplicateSuccess: 'Projet dupliqué avec succès',
    duplicateFailed: 'Échec de la duplication du projet',
    duplicateTitle: 'Dupliquer le projet',
    duplicateProject: 'Dupliquer le projet',
    duplicateProjectDescription:
      'Créez une copie de ce projet incluant toutes les images. Vous pouvez personnaliser les options ci-dessous.',
    duplicateCancelled: 'Duplication du projet annulée',
    duplicatingProject: 'Duplication du projet',
    duplicatingProjectDescription: 'Votre projet est en cours de duplication. Cela peut prendre quelques instants.',
    duplicateProgress: 'Progression de la duplication',
    duplicationComplete: 'Duplication du projet terminée',
    duplicationTaskFetchError: 'Erreur lors de la récupération des données de la tâche',
    duplicationCancelError: "Erreur lors de l'annulation de la duplication",
    duplicateProgressDescription:
      'Votre projet est en cours de duplication. Ce processus peut prendre du temps pour les grands projets.',
    duplicationPending: 'En attente',
    duplicationProcessing: 'En traitement',
    duplicationCompleted: 'Terminé',
    duplicationFailed: 'Échoué',
    duplicationCancelled: 'Annulé',
    duplicationCancellationFailed: "Échec de l'annulation de la duplication",
    duplicationSuccessMessage: 'Projet dupliqué avec succès ! Vous pouvez maintenant accéder au nouveau projet.',
    copySegmentations: 'Copier les résultats de segmentation',
    resetImageStatus: 'Réinitialiser le statut de traitement des images',
    newProjectTitle: 'Nouveau titre du projet',
    itemsProcessed: 'éléments traités',
    items: 'éléments',
    unknownProject: 'Projet inconnu',
    activeTasks: 'Actif',
    allTasks: 'Tout',
    noActiveDuplications: 'Aucune duplication active',
    noDuplications: 'Aucune tâche de duplication trouvée',
    untitledProject: 'Projet sans titre',
    exportProject: 'Exporter le projet',
    share: 'Partager',
    export: 'Exporter',
    archived: 'Archivé',
    completed: 'Terminé',
    draft: 'Brouillon',
    active: 'Actif',
  },
  projectToolbar: {
    selectImages: 'Sélectionner des images',
    cancelSelection: 'Annuler la sélection',
    export: 'Exporter',
    uploadImages: 'Télécharger des images',
  },
  statsOverview: {
    title: 'Aperçu du tableau de bord',
    totalProjects: 'Total des projets',
    totalImages: 'Total des images',
    completedSegmentations: 'Segmentations terminées',
    storageUsed: 'Stockage utilisé',
    recentActivity: 'Activité récente',
    moreStats: 'Voir les statistiques détaillées',
    completion: "taux d'achèvement",
    vsLastMonth: 'vs. mois dernier',
    thisMonth: 'Ce mois',
    lastMonth: 'Mois dernier',
    projectsCreated: 'Projets créés',
    imagesUploaded: 'Images téléchargées',
    fetchError: 'Échec du chargement des statistiques',
    storageLimit: 'Limite de stockage',
    activityTitle: 'Activité récente',
    noActivity: 'Aucune activité récente',
    hide: 'Masquer',
    activityTypes: {
      project_created: 'Projet créé',
      image_uploaded: 'Image téléchargée',
      segmentation_completed: 'Segmentation terminée',
    },
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FJFI ČVUT à Prague',
    description: "Plateforme avancée pour la segmentation et l'analyse de sphéroïdes",
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: 'FJFI ČVUT à Prague',
    resourcesTitle: 'Ressources',
    documentationLink: 'Documentation',
    featuresLink: 'Fonctionnalités',
    tutorialsLink: 'Tutoriels',
    researchLink: 'Recherche',
    legalTitle: 'Informations légales',
    termsLink: "Conditions d'utilisation",
    privacyLink: 'Politique de confidentialité',
    contactUsLink: 'Nous contacter',
    informationTitle: 'Information',
    contactTitle: 'Contact',
    copyrightNotice: 'SpheroSeg. Tous droits réservés.',
    madeWith: 'Fait avec',
    by: 'par',
    requestAccessLink: "Demander l'accès",
    githubRepository: 'Dépôt GitHub',
    contactEmail: 'Email de contact',
  },
  features: {
    tag: 'Fonctionnalités',
    title: 'Découvrez les capacités de notre plateforme',
    subtitle: 'Outils avancés pour la recherche biomédicale',
    cards: {
      segmentation: {
        title: 'Segmentation avancée',
        description: 'Détection précise des sphéroïdes avec analyse des limites pour des mesures cellulaires précises',
      },
      aiAnalysis: {
        title: "Analyse alimentée par l'IA",
        description:
          "Exploitez les algorithmes d'apprentissage profond pour la détection et la classification automatisées des cellules",
      },
      uploads: {
        title: 'Téléchargement facile',
        description: 'Glissez et déposez vos images de microscopie pour un traitement et une analyse immédiats',
      },
      insights: {
        title: 'Aperçus statistiques',
        description: 'Métriques et visualisations complètes pour extraire des modèles de données significatifs',
      },
      collaboration: {
        title: "Collaboration d'équipe",
        description: 'Partagez des projets et des résultats avec des collègues pour une recherche plus efficace',
      },
      pipeline: {
        title: 'Pipeline automatisé',
        description: 'Rationalisez votre flux de travail avec nos outils de traitement par lots',
      },
    },
  },
  index: {
    about: {
      tag: 'À propos de la plateforme',
      title: "Qu'est-ce que SpheroSeg ?",
      imageAlt: 'Exemple de segmentation de sphéroïde',
      paragraph1:
        "SpheroSeg est une plateforme avancée spécialement conçue pour la segmentation et l'analyse de sphéroïdes cellulaires dans des images microscopiques.",
      paragraph2:
        "Notre outil combine des algorithmes d'intelligence artificielle de pointe avec une interface intuitive pour fournir aux chercheurs une détection précise des limites des sphéroïdes et des capacités analytiques.",
      paragraph3:
        "La plateforme a été développée par Michal Průšek de la FJFI ČVUT à Prague sous la supervision d'Adam Novozámský de l'UTIA CAS, en collaboration avec des chercheurs du département de biochimie et de microbiologie de l'UCT Prague.",
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: 'Prêt à transformer votre recherche ?',
      subtitle:
        "Commencez à utiliser SpheroSeg aujourd'hui et découvrez de nouvelles possibilités dans l'analyse des sphéroïdes cellulaires",
      boxTitle: 'Créez un compte gratuit',
      boxText:
        'Accédez à toutes les fonctionnalités de la plateforme et commencez à analyser vos images de microscopie',
      button: 'Créer un compte',
    },
  },
  tools: {
    zoomIn: 'Zoom avant',
    zoomOut: 'Zoom arrière',
    resetView: 'Réinitialiser la vue',
    createPolygon: 'Créer un nouveau polygone',
    exitPolygonCreation: 'Quitter le mode création de polygone',
    splitPolygon: 'Diviser le polygone en deux',
    exitSlicingMode: 'Quitter le mode division',
    addPoints: 'Ajouter des points au polygone',
    exitPointAddingMode: 'Quitter le mode ajout de points',
    undo: 'Annuler',
    redo: 'Rétablir',
    save: 'Enregistrer',
    resegment: 'Resegmenter',
    title: 'Outils',
  },
  settings: {
    title: 'Paramètres',
    pageTitle: 'Paramètres',
    profile: 'Profil',
    account: 'Compte',
    appearance: 'Apparence',
    profileSettings: 'Paramètres du profil',
    accountSettings: 'Paramètres du compte',
    securitySettings: 'Paramètres de sécurité',
    preferenceSettings: 'Paramètres de préférence',
    selectLanguage: 'Sélectionner la langue',
    selectTheme: 'Sélectionner le thème',
    updateProfile: 'Mettre à jour le profil',
    changePassword: 'Changer le mot de passe',
    deleteAccount: 'Supprimer le compte',
    savedChanges: 'Modifications enregistrées avec succès',
    saveChanges: 'Enregistrer les modifications',
    profileUpdated: 'Profil mis à jour avec succès',
    languageSettings: 'Paramètres de langue',
    themeSettings: 'Paramètres du thème',
    privacySettings: 'Paramètres de confidentialité',
    exportData: 'Exporter les données',
    importData: 'Importer les données',
    uploadAvatar: 'Télécharger une photo de profil',
    removeAvatar: 'Supprimer la photo de profil',
    twoFactorAuth: 'Authentification à deux facteurs',
    emailNotifications: 'Notifications par e-mail',
    pushNotifications: 'Notifications push',
    weeklyDigest: 'Résumé hebdomadaire',
    monthlyReport: 'Rapport mensuel',
    displaySettings: "Paramètres d'affichage",
    accessibilitySettings: "Paramètres d'accessibilité",
    advancedSettings: 'Paramètres avancés',
    useBrowserLanguage: 'Utiliser la langue du navigateur',
    language: 'Langue',
    theme: 'Thème',
    light: 'Clair',
    dark: 'Sombre',
    system: 'Système',
    languageUpdated: 'Langue mise à jour avec succès',
    themeUpdated: 'Thème mis à jour avec succès',
    toggleTheme: 'Basculer le thème',
    languageDescription: 'Choisissez votre langue préférée',
    themeDescription: 'Choisissez votre thème préféré',
    profileLoadError: 'Échec du chargement du profil',
    appearanceDescription: "Personnalisez l'apparence de l'application",
    personal: 'Informations personnelles',
    fullName: 'Nom complet',
    organization: 'Organisation',
    department: 'Département',
    publicProfile: 'Profil public',
    makeProfileVisible: 'Rendre mon profil visible aux autres chercheurs',
    passwordSettings: 'Paramètres du mot de passe',
    currentPassword: 'Mot de passe actuel',
    newPassword: 'Nouveau mot de passe',
    confirmNewPassword: 'Confirmer le nouveau mot de passe',
    dangerZone: 'Zone dangereuse',
    deleteAccountWarning:
      "Une fois que vous supprimez votre compte, il n'y a pas de retour en arrière. Toutes vos données seront définitivement supprimées.",
    savingChanges: 'Enregistrement des modifications...',
    savePreferences: 'Enregistrer les préférences',
    usernameTaken: "Ce nom d'utilisateur est déjà pris",
    deleteAccountDescription: 'Cette action est irréversible. Toutes vos données seront définitivement supprimées.',
    confirmUsername: 'Confirmez votre e-mail',
    password: 'Mot de passe',
    enterPassword: 'Entrez votre mot de passe',
    passwordChangeError: 'Erreur lors du changement de mot de passe',
    passwordChangeSuccess: 'Mot de passe modifié avec succès',
    passwordsDoNotMatch: 'Les mots de passe ne correspondent pas',
    accountDeleteSuccess: 'Compte supprimé avec succès',
    accountDeleteError: 'Erreur lors de la suppression du compte',
    passwordChanged: 'Mot de passe modifié',
    changingPassword: 'Changement du mot de passe...',
    confirmPasswordLabel: 'Confirmer le mot de passe',
    changePasswordDescription: 'Changez votre mot de passe pour sécuriser votre compte',
    dangerZoneDescription: 'Ces actions sont irréversibles et supprimeront définitivement vos données',
    deletingAccount: 'Suppression du compte...',
    deleteAccountError: 'Erreur lors de la suppression du compte',
  },
  accessibility: {
    skipToContent: 'Aller au contenu principal',
  },
  profile: {
    title: 'Titre',
    about: 'À propos',
    activity: 'Activité',
    projects: 'Projets',
    recentProjects: 'Projets récents',
    recentAnalyses: 'Analyses récentes',
    accountDetails: 'Détails du compte',
    accountType: 'Type de compte',
    joinDate: "Date d'inscription",
    lastActive: 'Dernière activité',
    projectsCreated: 'Projets créés',
    imagesUploaded: 'Images téléchargées',
    segmentationsCompleted: 'Segmentations terminées',
    pageTitle: 'Profil utilisateur',
    editProfile: 'Modifier le profil',
    joined: 'Inscrit',
    statistics: 'Statistiques',
    images: 'Images',
    analyses: 'Analyses',
    storageUsed: 'Stockage utilisé',
    recentActivity: 'Activité récente',
    noRecentActivity: 'Aucune activité récente',
    fetchError: 'Échec du chargement des données du profil',
    aboutMe: 'À propos de moi',
    noBio: 'Aucune biographie fournie',
    avatarHelp: "Cliquez sur l'icône de l'appareil photo pour télécharger une photo de profil",
    avatarImageOnly: 'Veuillez sélectionner un fichier image',
    avatarTooLarge: "L'image doit être inférieure à 5 Mo",
    avatarUpdated: 'Photo de profil mise à jour',
    avatarUploadError: 'Échec du téléchargement de la photo de profil',
    avatarRemoved: 'Photo de profil supprimée',
    avatarRemoveError: 'Échec de la suppression de la photo de profil',
    cropAvatarDescription: 'Ajustez la zone de recadrage pour définir votre photo de profil',
    description: 'Mettez à jour vos informations personnelles et votre photo de profil',
    saveButton: 'Enregistrer le profil',
    username: "Nom d'utilisateur",
    usernamePlaceholder: "Entrez votre nom d'utilisateur",
    fullName: 'Nom complet',
    fullNamePlaceholder: 'Entrez votre nom complet',
    titlePlaceholder: 'ex. Chercheur, Professeur',
    organization: 'Organisation',
    organizationPlaceholder: 'Entrez votre organisation ou institution',
    bio: 'Biographie',
    bioPlaceholder: 'Parlez-nous de vous',
    bioDescription: 'Une brève description de vous qui sera visible sur votre profil',
    location: 'Localisation',
    locationPlaceholder: 'ex. Prague, République tchèque',
    uploadAvatar: 'Télécharger une photo de profil',
    removeAvatar: 'Supprimer la photo de profil',
    cropAvatar: 'Recadrer la photo de profil',
    activityDescription: 'Activité du système',
    email: 'E-mail',
    notProvided: 'Non fourni',
  },
  termsPage: {
    title: "Conditions d'utilisation",
    acceptance: {
      title: '1. Acceptation des conditions',
      paragraph1:
        "En accédant ou en utilisant SpheroSeg, vous acceptez d'être lié par ces conditions d'utilisation et toutes les lois et réglementations applicables. Si vous n'acceptez pas l'une de ces conditions, il vous est interdit d'utiliser ce service.",
    },
    useLicense: {
      title: "2. Licence d'utilisation",
      paragraph1:
        "L'autorisation est accordée d'utiliser temporairement SpheroSeg à des fins personnelles, non commerciales ou de recherche académique uniquement. Il s'agit de l'octroi d'une licence, et non d'un transfert de titre.",
    },
    dataUsage: {
      title: '3. Utilisation des données',
      paragraph1:
        'Toutes les données téléchargées sur SpheroSeg restent votre propriété. Nous ne revendiquons pas la propriété de votre contenu, mais nous avons besoin de certaines autorisations pour fournir le service.',
    },
    limitations: {
      title: '4. Limitations',
      paragraph1:
        "En aucun cas, SpheroSeg ne sera responsable des dommages résultant de l'utilisation ou de l'incapacité d'utiliser la plateforme, même si nous avons été informés de la possibilité de tels dommages.",
    },
    revisions: {
      title: '5. Révisions et erreurs',
      paragraph1:
        'Les documents apparaissant sur SpheroSeg peuvent inclure des erreurs techniques, typographiques ou photographiques. Nous ne garantissons pas que les documents sont exacts, complets ou à jour.',
    },
    governingLaw: {
      title: '6. Droit applicable',
      paragraph1:
        'Ces conditions seront régies et interprétées conformément aux lois du pays dans lequel le service est hébergé, et vous vous soumettez irrévocablement à la juridiction exclusive des tribunaux de ce lieu.',
    },
    lastUpdated: 'Dernière mise à jour : 7 janvier 2025',
  },
  privacyPage: {
    title: 'Politique de confidentialité',
    introduction: {
      title: '1. Introduction',
      paragraph1:
        'Cette politique de confidentialité explique comment SpheroSeg ("nous", "notre") collecte, utilise et partage vos informations lorsque vous utilisez notre plateforme de segmentation et d\'analyse de sphéroïdes.',
    },
    dataCollection: {
      title: '2. Informations que nous collectons',
      paragraph1: 'Nous collectons des informations que vous nous fournissez directement, notamment :',
      list: [
        'Informations de compte (e-mail, nom, institution)',
        'Images téléchargées et données pour la segmentation',
        "Métadonnées de projet et résultats d'analyse",
        "Données d'utilisation et journaux d'activité",
      ],
    },
    dataUsage: {
      title: '3. Comment nous utilisons vos informations',
      paragraph1: 'Nous utilisons les informations collectées pour :',
      list: [
        'Fournir et maintenir nos services',
        "Traiter vos demandes de segmentation d'images",
        'Améliorer nos algorithmes et services',
        'Communiquer avec vous au sujet de votre compte',
        'Assurer la sécurité et prévenir les abus',
      ],
    },
    dataStorage: {
      title: '4. Stockage et sécurité des données',
      paragraph1:
        "Nous mettons en œuvre des mesures techniques et organisationnelles appropriées pour protéger vos informations personnelles contre l'accès, la modification, la divulgation ou la destruction non autorisés.",
      paragraph2:
        'Vos données sont stockées sur des serveurs sécurisés et sont supprimées conformément à notre politique de conservation des données.',
    },
    dataSharing: {
      title: '5. Partage de données',
      paragraph1:
        "Nous ne vendons, n'échangeons ou ne transférons pas vos informations personnelles à des tiers sans votre consentement, sauf dans les cas décrits dans cette politique ou requis par la loi.",
    },
    userRights: {
      title: '6. Vos droits',
      paragraph1: 'Vous avez le droit de :',
      list: [
        'Accéder à vos informations personnelles',
        'Corriger les informations inexactes',
        'Demander la suppression de vos données',
        'Exporter vos données',
        'Vous opposer au traitement de vos données',
      ],
    },
    cookies: {
      title: '7. Cookies et technologies de suivi',
      paragraph1:
        "Nous utilisons des cookies et des technologies similaires pour améliorer votre expérience, analyser l'utilisation du site et personnaliser le contenu.",
    },
    changes: {
      title: '8. Modifications de cette politique',
      paragraph1:
        'Nous pouvons mettre à jour notre politique de confidentialité de temps en temps. Nous vous informerons de tout changement en publiant la nouvelle politique de confidentialité sur cette page.',
    },
    contact: {
      title: '9. Nous contacter',
      paragraph1: 'Si vous avez des questions sur cette politique de confidentialité, veuillez nous contacter à :',
      email: '<EMAIL>',
    },
    lastUpdated: 'Dernière mise à jour : 7 janvier 2025',
  },
  shortcuts: {
    button: 'Raccourcis',
    editMode: 'Passer en mode édition',
    sliceMode: 'Passer en mode division',
    addPointMode: 'Passer en mode ajout de points',
    holdShift: 'Maintenez Shift pour ajouter automatiquement des points (en mode édition)',
    undo: 'Annuler',
    redo: 'Rétablir',
    deletePolygon: 'Supprimer le polygone sélectionné',
    cancel: "Annuler l'opération en cours",
    zoomIn: 'Zoom avant',
    zoomOut: 'Zoom arrière',
    resetView: 'Réinitialiser la vue',
    title: 'Raccourcis clavier',
    viewMode: 'Mode visualisation',
    editVerticesMode: 'Mode édition des sommets',
    addPointsMode: 'Mode ajout de points',
    createPolygonMode: 'Mode création de polygone',
    save: 'Enregistrer',
    description:
      "Ces raccourcis fonctionnent dans l'éditeur de segmentation pour un travail plus rapide et plus confortable.",
  },
  imageProcessor: {
    segmentationStarted: 'Le processus de segmentation a commencé...',
    startSegmentationTooltip: 'Démarrer la segmentation',
    processingTooltip: 'En traitement...',
    savingTooltip: 'Enregistrement...',
    completedTooltip: 'Segmentation terminée',
    retryTooltip: 'Réessayer la segmentation',
  },
  uploader: {
    dragDrop: 'Glissez et déposez des images ici ou cliquez pour sélectionner des fichiers',
    dropFiles: 'Déposez les fichiers ici...',
    segmentAfterUploadLabel: 'Segmenter les images immédiatement après le téléchargement',
    filesToUpload: 'Fichiers à télécharger',
    uploadBtn: 'Télécharger',
    uploadError: "Une erreur s'est produite lors du téléchargement. Veuillez réessayer.",
    clickToUpload: 'Cliquez pour parcourir les fichiers',
    selectProjectLabel: 'Sélectionner un projet',
    selectProjectPlaceholder: 'Sélectionnez un projet...',
    noProjectsFound: "Aucun projet trouvé. Créez-en un nouveau d'abord.",
    imageOnly: '(Fichiers image uniquement)',
    uploadingImages: 'Téléchargement des images...',
    uploadComplete: 'Téléchargement terminé',
    uploadFailed: 'Échec du téléchargement',
    processingImages: 'Traitement des images...',
    dragAndDropFiles: 'Glissez et déposez des fichiers ici',
    or: 'ou',
    clickToSelect: 'Cliquez pour sélectionner des fichiers',
  },
  images: {
    uploadImages: 'Télécharger des images',
    dragDrop: 'Glissez et déposez des images ici',
    clickToSelect: 'ou cliquez pour sélectionner des fichiers',
    acceptedFormats: 'Formats pris en charge : JPEG, PNG, TIFF, BMP (max 10 Mo)',
    uploadProgress: 'Progression du téléchargement',
    uploadingTo: 'Téléchargement vers',
    currentProject: 'Projet actuel',
    autoSegment: 'Segmenter automatiquement les images après le téléchargement',
    uploadCompleted: 'Téléchargement terminé',
    uploadFailed: 'Échec du téléchargement',
    imagesUploaded: 'Images téléchargées avec succès',
    imagesFailed: "Échec du téléchargement de l'image",
    viewAnalyses: 'Voir les analyses',
    noAnalysesYet: "Pas encore d'analyses",
    runAnalysis: "Lancer l'analyse",
    viewResults: 'Voir les résultats',
    dropImagesHere: 'Déposez les images ici...',
    selectProjectFirst: "Veuillez d'abord sélectionner un projet",
    projectRequired: 'Vous devez sélectionner un projet avant de télécharger des images',
    imageOnly: '(Fichiers image uniquement)',
    dropFiles: 'Déposez les fichiers ici...',
    filesToUpload: 'Fichiers à télécharger ({{count}})',
    uploadBtn: 'Télécharger {{count}} images',
    uploadError: "Une erreur s'est produite lors du téléchargement. Veuillez réessayer.",
    noProjectsToUpload: "Aucun projet disponible. Créez d'abord un projet.",
    notFound: 'Projet "{{projectName}}" introuvable. Il a peut-être été supprimé.',
    errors: {
      imageOrProjectNotFound: 'Image ou projet introuvable.',
      failedToDeleteImage: "Échec de la suppression de l'image",
      imageOrProjectNotFoundForNavigation: 'Image ou projet introuvable pour la navigation, ou UUID manquant.',
      imageNotFoundForClearingSegmentation: 'Image introuvable pour effacer la segmentation ou UUID manquant.',
      failedToClearSegmentation: "Échec de l'effacement de la segmentation",
    },
    success: {
      localImageDeleted: 'Image locale supprimée avec succès',
      imageDeleted: 'Image supprimée avec succès',
      segmentationCleared: 'Segmentation effacée avec succès.',
    },
    info: {
      clearingSegmentation: "Effacement de la segmentation pour l'image {{imageName}}...",
      selectAtLeastOneImage: 'Veuillez sélectionner au moins une image.',
    },
  },
  export: {
    formatDescriptions: {
      COCO: "Format JSON Common Objects in Context (COCO) pour la détection d'objets",
      YOLO: "Format texte You Only Look Once (YOLO) pour la détection d'objets",
      MASK: 'Images de masque binaire pour chaque objet segmenté',
      POLYGONS: 'Coordonnées de polygone au format JSON',
      DATUMARO: 'Format Datumaro - représentation unifiée de jeu de données',
      CVAT_MASKS: 'Format XML CVAT avec annotations de polygone',
      CVAT_YAML: "Format YAML CVAT pour l'échange d'annotations",
    },
    exportCompleted: 'Export terminé',
    exportFailed: "Échec de l'export",
    title: 'Exporter les données de segmentation',
    spheroidMetrics: 'Métriques des sphéroïdes',
    visualization: 'Visualisation',
    cocoFormat: 'Format COCO',
    close: 'Fermer',
    metricsExported: 'Métriques exportées avec succès',
    options: {
      includeMetadata: 'Inclure les métadonnées',
      includeSegmentation: 'Inclure la segmentation',
      selectExportFormat: "Sélectionner le format d'export",
      includeObjectMetrics: "Inclure les métriques d'objet",
      selectMetricsFormat: 'Sélectionner le format des métriques',
      metricsFormatDescription: {
        EXCEL: 'Fichier Excel (.xlsx)',
        CSV: 'Fichier CSV (.csv)',
      },
      includeImages: 'Inclure les images originales',
      exportMetricsOnly: 'Exporter uniquement les métriques',
      metricsRequireSegmentation: "L'export des métriques nécessite une segmentation terminée",
    },
    formats: {
      COCO: 'COCO JSON',
      YOLO: 'YOLO TXT',
      MASK: 'Masque (TIFF)',
      POLYGONS: 'Polygones (JSON)',
      DATUMARO: 'Datumaro',
      CVAT_MASKS: 'CVAT Masques (XML)',
      CVAT_YAML: 'CVAT YAML',
    },
    metricsFormats: {
      EXCEL: 'Excel (.xlsx)',
      CSV: 'CSV (.csv)',
    },
    selectImagesForExport: "Sélectionner des images pour l'export",
    selectImagesToExport: 'Sélectionner des images à exporter',
    noImagesAvailable: 'Aucune image disponible',
    backToProject: 'Retour au projet',
    exportImages: 'Exporter les images',
    maskExportError: "Erreur lors de l'export du masque",
    maskExportStarted: 'Export du masque démarré',
    metricsRequireSegmentation: 'Les métriques nécessitent que la segmentation soit terminée',
    noImageSelectedError: "Aucune image sélectionnée pour l'export",
  },
  metrics: {
    area: 'Surface',
    perimeter: 'Périmètre',
    circularity: 'Circularité',
    sphericity: 'Sphéricité',
    solidity: 'Solidité',
    compactness: 'Compacité',
    convexity: 'Convexité',
    visualization: 'Visualisation des métriques',
    visualizationHelp: 'Représentation visuelle des métriques pour tous les sphéroïdes de cette image',
    barChart: 'Graphique à barres',
    pieChart: 'Graphique circulaire',
    comparisonChart: 'Graphique de comparaison',
    keyMetricsComparison: 'Comparaison des métriques clés',
    areaDistribution: 'Distribution de la surface',
    shapeMetricsComparison: 'Comparaison des métriques de forme',
    noPolygonsFound: "Aucun polygone trouvé pour l'analyse",
  },
  imageStatus: {
    completed: 'Traité',
    processing: 'En traitement',
    pending: 'En attente',
    failed: 'Échoué',
    noImage: 'Aucune image',
    untitledImage: 'Image sans titre',
  },
  projectActions: {
    deleteTooltip: 'Supprimer le projet',
    deleteConfirmTitle: 'Êtes-vous sûr ?',
    deleteConfirmDesc:
      'Êtes-vous sûr de vouloir supprimer le projet "{{projectName}}" ? Cette action ne peut pas être annulée.',
    deleteSuccess: 'Le projet "{{projectName}}" a été supprimé avec succès.',
    deleteError: 'Échec de la suppression du projet.',
    duplicateSuccess: 'Le projet "{{projectName}}" a été dupliqué avec succès.',
    duplicateError: 'Échec de la duplication du projet.',
    makePrivateTooltip: 'Marquer comme privé',
    makePublicTooltip: 'Marquer comme public',
    shareTooltip: 'Partager le projet',
    downloadTooltip: 'Télécharger le projet',
    notFound: 'Projet "{{projectName}}" introuvable. Il a peut-être déjà été supprimé.',
  },
  editor: {
    backButtonTooltip: "Retour à la vue d'ensemble du projet",
    exportButtonTooltip: 'Exporter les données de segmentation actuelles',
    saveTooltip: 'Enregistrer les modifications',
    image: 'Image',
    previousImage: 'Image précédente',
    nextImage: 'Image suivante',
    resegmentButton: 'Resegmenter',
    resegmentButtonTooltip: 'Relancer la segmentation sur cette image',
    exportMaskButton: 'Exporter le masque',
    exportMaskButtonTooltip: 'Exporter le masque de segmentation pour cette image',
    backButton: 'Retour',
    exportButton: 'Exporter',
    saveButton: 'Enregistrer',
    loadingProject: 'Chargement du projet...',
    loadingImage: "Chargement de l'image...",
    sliceErrorInvalidPolygon: 'Impossible de diviser : polygone invalide sélectionné.',
    sliceWarningInvalidResult: 'La division a créé des polygones trop petits et invalides.',
    sliceWarningInvalidIntersections:
      'Division invalide : la ligne de coupe doit intersecter le polygone en exactement deux points.',
    sliceSuccess: 'Polygone divisé avec succès.',
    noPolygonToSlice: 'Aucun polygone disponible pour la division.',
    savingTooltip: 'Enregistrement...',
  },
  segmentationPage: {
    noImageSelected: 'Aucune image sélectionnée pour la resegmentation.',
    resegmentationStarted: 'Démarrage de la resegmentation avec le réseau de neurones ResUNet...',
    resegmentationQueued: "La resegmentation a été mise en file d'attente.",
    resegmentationCompleted: 'Resegmentation terminée avec succès.',
    resegmentationFailed: 'La resegmentation a échoué.',
    resegmentationTimeout: "Délai d'attente de la resegmentation dépassé. Vérifiez le statut de la file d'attente.",
    resegmentationError: 'Échec du démarrage de la resegmentation.',
    resegmentTooltip: 'Resegmenter',
  },
  share: {
    accepted: 'Accepté',
    alreadyShared: 'Déjà partagé avec cet utilisateur',
    canEdit: 'Peut modifier',
    copyToClipboard: 'Copier dans le presse-papiers',
    edit: 'Modifier',
    email: 'E-mail',
    failedToCopy: 'Échec de la copie du lien',
    failedToGenerateLink: 'Échec de la génération du lien de partage',
    failedToLoadShares: 'Échec du chargement des utilisateurs partagés',
    failedToRemove: 'Échec de la suppression du partage',
    failedToShare: 'Échec du partage du projet',
    generateLink: 'Générer un lien',
    generateNewLink: 'Générer un nouveau lien',
    generating: 'Génération...',
    invalidEmail: 'Adresse e-mail invalide',
    invalidEmailOrPermission: 'E-mail ou permission invalide',
    invite: 'Inviter',
    inviteByEmail: 'Inviter par e-mail',
    inviteByLink: 'Inviter par lien',
    linkCopied: 'Lien copié dans le presse-papiers',
    linkGenerated: 'Lien de partage généré',
    linkPermissions: 'Permissions du lien',
    noPermission: 'Aucune permission',
    noShares: 'Aucun utilisateur partagé',
    pendingAcceptance: 'Acceptation en attente',
    permissions: 'Permissions',
    projectNotFound: 'Projet introuvable',
    removeShare: 'Supprimer le partage',
    selectAccessLevel: "Sélectionner le niveau d'accès",
    selectPermission: 'Veuillez sélectionner un type de permission',
    shareDescription: "Partagez ce projet avec d'autres utilisateurs",
    sharedWith: 'Partagé avec',
    shareLinkDescription: 'Toute personne disposant de ce lien peut accéder au projet',
    shareProject: 'Partager le projet',
    shareProjectTitle: 'Partager le projet "{{projectName}}"',
    sharing: 'Partage...',
    sharedSuccess: 'Le projet "{{projectName}}" a été partagé avec {{email}}',
    removedSuccess: 'Le partage avec {{email}} a été supprimé',
    status: 'Statut',
    userEmail: "E-mail de l'utilisateur",
    view: 'Voir',
    viewOnly: 'Lecture seule',
  },
  invitation: {
    title: 'Invitation au projet',
    processing: "Traitement de l'invitation...",
    successTitle: 'Invitation acceptée !',
    successMessage: 'Vous avez maintenant accès à "{{projectName}}" partagé par {{ownerName}}.',
    redirecting: 'Redirection vers le projet...',
    errorTitle: "Impossible d'accepter l'invitation",
    loginRequired: 'Connexion requise',
    loginMessage: 'Veuillez vous connecter pour accepter cette invitation au projet.',
    signIn: 'Se connecter',
    createAccount: 'Créer un compte',
    goToDashboard: 'Aller au tableau de bord',
    invalidLink: "Lien d'invitation invalide",
    expired: "Ce lien d'invitation a expiré ou est invalide",
    notForYou: "Cette invitation n'est pas destinée à votre compte",
    genericError: "Échec de l'acceptation de l'invitation. Veuillez réessayer.",
    acceptedSuccess: 'Invitation acceptée avec succès',
  },
  about: {
    title: 'À propos de SpheroSeg',
    mission: {
      title: 'Notre mission',
      description:
        "SpheroSeg est une plateforme avancée spécialement conçue pour la segmentation et l'analyse de sphéroïdes cellulaires dans des images microscopiques. Nous combinons des algorithmes d'intelligence artificielle de pointe avec une interface intuitive pour fournir aux chercheurs une détection précise des limites des sphéroïdes et des capacités analytiques.",
      vision:
        "Notre vision est d'accélérer la découverte scientifique en rendant l'analyse d'images avancée accessible aux chercheurs du monde entier, leur permettant de se concentrer sur leur recherche plutôt que sur les défis techniques.",
    },
    technology: {
      title: 'Notre technologie',
      description:
        "Construit sur des modèles d'apprentissage profond et des techniques de vision par ordinateur de pointe, SpheroSeg offre une précision inégalée dans la segmentation des sphéroïdes.",
      feature1: {
        title: "Segmentation alimentée par l'IA",
        description:
          "Des modèles d'apprentissage profond avancés formés sur diverses images de sphéroïdes garantissent des résultats de segmentation précis et fiables.",
      },
      feature2: {
        title: 'Traitement en temps réel',
        description:
          "Des algorithmes optimisés fournissent des temps de traitement rapides, vous permettant d'analyser efficacement de grands ensembles de données.",
      },
      feature3: {
        title: 'Analyse complète',
        description:
          'Extrayez des métriques détaillées incluant la surface, le périmètre, la circularité, et plus pour chaque sphéroïde segmenté.',
      },
    },
    team: {
      title: 'Notre équipe',
      description:
        "SpheroSeg a été développé par une équipe dédiée de chercheurs et d'ingénieurs passionnés par l'avancement de la recherche biomédicale",
      member1: {
        name: 'Michal Průšek',
        role: 'Développeur principal, FJFI ČVUT Prague',
      },
      member2: {
        name: 'Adam Novozámský',
        role: 'Superviseur, UTIA CAS',
      },
      member3: {
        name: 'Équipe de recherche',
        role: 'Département de biochimie et microbiologie, UCT Prague',
      },
    },
    contact: {
      title: 'Nous contacter',
      description: "Des questions ou besoin d'aide ? Nous sommes là pour vous aider !",
      email: 'Contacter par e-mail',
      github: 'Voir sur GitHub',
      twitter: 'Suivre sur Twitter',
    },
  },
};
