import fs from 'fs';
import path from 'path';

// Read the English file to get proper structure
const enPath = path.join(process.cwd(), 'src/translations/en.ts');
const enContent = fs.readFileSync(enPath, 'utf-8');

// Parse English to get structure
const enMatch = enContent.match(/export\s+default\s+(\{[\s\S]*\});?\s*$/);
if (!enMatch) {
  console.error('Could not parse English translation file');
  process.exit(1);
}

let enTranslations: any;
try {
  enTranslations = new Function(`return ${enMatch[1]}`)();
} catch (error) {
  console.error('Error parsing English translations:', error);
  process.exit(1);
}

// Complete Chinese translations with all keys
const zhTranslations = {
  segmentation: {
    contextMenu: {
      editPolygon: '编辑多边形',
      splitPolygon: '分割多边形',
      deletePolygon: '删除多边形',
      confirmDeleteTitle: '您确定要删除多边形吗？',
      confirmDeleteMessage: '此操作不可逆。多边形将从分割中永久删除。',
      duplicateVertex: '复制顶点',
      deleteVertex: '删除顶点',
    },
    title: '分割编辑器',
    resolution: '{width}x{height}',
    batch: {
      mixed: '分割：{{successCount}} 张图片成功排队，{{failCount}} 张失败',
      allSuccess: '分割：所有 {{count}} 张图片成功排队',
      allFailed: '分割：所有 {{count}} 张图片失败',
    },
    queue: {
      title: '分割队列',
      summary: '总共 {{total}} 个任务（{{running}} 个处理中，{{queued}} 个排队中）',
      noRunningTasks: '没有运行中的任务',
      noQueuedTasks: '没有排队的任务',
      task: '任务',
      statusRunning: '分割：{{count}} 个运行中{{queued}}',
      statusQueued: '，{{count}} 个排队中',
      statusOnlyQueued: '分割：{{count}} 个排队中',
      statusOnlyQueued_one: '分割：1 个排队中',
      statusOnlyQueued_other: '分割：{{count}} 个排队中',
      processing: '处理中',
      queued: '排队中',
      statusProcessing: '分割：{{count}} 个处理中',
      statusReady: '就绪',
      tasksTotal: '总共 {{total}} 个任务（{{running}} 个处理中，{{queued}} 个排队中）',
    },
    selectPolygonForEdit: '选择要编辑的多边形',
    selectPolygonForSlice: '选择要切片的多边形',
    selectPolygonForAddPoints: '选择要添加点的多边形',
    clickToAddPoint: '点击添加点',
    clickToCompletePolygon: '点击第一个点以完成多边形',
    clickToAddFirstSlicePoint: '点击添加第一个切片点',
    clickToAddSecondSlicePoint: '点击添加第二个切片点',
    polygonCreationMode: '多边形创建模式',
    polygonEditMode: '多边形编辑模式',
    polygonSliceMode: '多边形切片模式',
    polygonAddPointsMode: '添加点模式',
    viewMode: '查看模式',
    totalPolygons: '总多边形数',
    totalVertices: '总顶点数',
    vertices: '顶点',
    zoom: '缩放',
    mode: '模式',
    selected: '已选择',
    none: '无',
    polygons: '多边形',
    imageNotFound: '未找到图片',
    returnToProject: '返回项目',
    backToProject: '返回项目',
    previousImage: '上一张图片',
    nextImage: '下一张图片',
    toggleShortcuts: '切换快捷键',
    modes: {
      view: '查看模式',
      edit: '编辑模式',
      create: '创建模式',
      slice: '切片模式',
      addPoints: '添加点模式',
      deletePolygon: '删除多边形模式',
      createPolygon: '创建多边形模式',
      editVertices: '编辑顶点模式',
      editMode: '编辑模式',
      slicingMode: '切片模式',
      pointAddingMode: '点添加模式',
    },
    status: {
      processing: '处理中',
      queued: '排队中',
      completed: '已完成',
      failed: '失败',
      pending: '待处理',
      withoutSegmentation: '无分割',
    },
    autoSave: {
      enabled: '自动保存：已启用',
      disabled: '自动保存：已禁用',
      idle: '自动保存：空闲',
      pending: '待处理...',
      saving: '正在保存...',
      success: '已保存',
      error: '错误',
    },
    loading: '正在加载分割...',
    polygon: '多边形',
    unsavedChanges: '未保存的更改',
    noData: '没有可用的分割数据',
    noPolygons: '未找到多边形',
    regions: '分割',
    position: '位置',
    processingImage: '正在处理图片...',
    helpTips: {
      view: {
        pan: '按住鼠标左键并拖动以平移',
        selectPolygon: '单击多边形以选择它',
        zoom: '使用鼠标滚轮进行缩放',
      },
    },
    imageNotFoundDescription: '请求的图片无法找到。',
    invalidImageDimensions: '无效的图片尺寸',
    noDataToSave: '没有要保存的数据',
    polygonDuplicated: '多边形已复制',
    polygonNotFound: '未找到多边形',
    polygonDeleted: '多边形已删除',
    invalidPolygonData: '无效的多边形数据',
    notEnoughPointsForPolygon: '多边形需要至少3个点',
    polygonCreated: '多边形已创建',
    saveSuccess: '分割已保存',
    saveError: '保存分割时出错',
    loadError: '加载分割时出错',
    invalidSlicePoints: '无效的切片点',
    sliceError: '切片多边形时出错',
    sliceSuccess: '多边形切片成功',
    vertexAdded: '顶点已添加',
    vertexDeleted: '顶点已删除',
    vertexDuplicated: '顶点已复制',
    invalidVertexOperation: '无效的顶点操作',
    cellsUpdated: '细胞已更新',
    cellUpdateError: '更新细胞时出错',
    clearConfirmTitle: '清除所有分割？',
    clearConfirmMessage: '这将删除所有多边形。此操作无法撤消。',
    cleared: '分割已清除',
    error: {
      exception: '分割错误：{{error}}',
      failed: '分割失败',
    },
    tools: {
      select: '选择工具',
      create: '创建工具',
      edit: '编辑工具',
      slice: '切片工具',
    },
    modeTips: {
      view: '点击并拖动以平移，滚动以缩放',
      create: '点击以添加顶点，关闭以完成',
      edit: '拖动顶点以调整形状',
      slice: '点击两个点以切割多边形',
    },
    segmentationLoading: '正在加载分割...',
    segmentationPolygon: '分割多边形',
    selectPolygonFirst: '请先选择一个多边形',
    addFirstPoint: '点击以添加第一个点',
    clickToClose: '点击第一个点以关闭多边形',
    minPoints: '至少需要 {{count}} 个点',
    shortcuts: {
      title: '键盘快捷键',
      toggleShortcuts: '切换快捷键 (H)',
      viewMode: '查看模式 (V)',
      createMode: '创建模式 (C)',
      editMode: '编辑模式 (E)',
      sliceMode: '切片模式 (S)',
      deletePolygon: '删除多边形 (Delete)',
      save: '保存 (Ctrl+S)',
      undo: '撤销 (Ctrl+Z)',
      redo: '重做 (Ctrl+Y)',
      nextImage: '下一张图片 (→)',
      previousImage: '上一张图片 (←)',
    },
    resegment: {
      button: '重新分割',
      title: '重新分割图片',
      confirm: '您确定要重新分割这张图片吗？当前的分割将被替换。',
      inProgress: '正在重新分割...',
      success: '重新分割成功',
      failed: '重新分割失败',
      noImagesSelected: '未选择图片',
      triggeringResegmentation: '正在触发 {{count}} 张图片的重新分割...',
      deleteConfirmation: '您确定要删除 {{count}} 张图片吗？此操作无法撤消。',
    },
  },
  errors: {
    somethingWentWrong: '出现了问题',
    componentError: '此组件发生错误',
    errorDetails: '错误详情',
    tryAgain: '重试',
    reloadPage: '重新加载页面',
    goBack: '返回',
    notFound: '页面未找到',
    pageNotFoundMessage: '您请求的页面无法找到',
    returnToHome: '返回主页',
    unauthorized: '未授权访问',
    forbidden: '访问被禁止',
    serverError: '服务器错误',
    networkError: '网络错误',
    timeoutError: '请求超时',
    validationError: '验证错误',
    unknownError: '未知错误',
    goHome: '前往主页',
    fetchSegmentationFailed: '获取分割失败',
    fetchImageFailed: '获取图片失败',
    saveSegmentationFailed: '保存分割失败',
    missingPermissions: '权限不足',
    invalidInput: '无效输入',
    resourceNotFound: '资源未找到',
  },
  noImages: {
    title: '还没有图片',
    description: '上传图片以开始分析。',
    uploadButton: '上传图片',
  },
  project: {
    detail: {
      noImagesSelected: '未选择图片',
      triggeringResegmentation: '正在触发 {{count}} 张图片的重新分割...',
      deleteConfirmation: '您确定要删除 {{count}} 张图片吗？此操作无法撤消。',
      deletingImages: '正在删除 {{count}} 张图片...',
      deleteSuccess: '成功删除 {{count}} 张图片',
      deleteFailed: '删除 {{count}} 张图片失败',
      preparingExport: '正在准备导出 {{count}} 张图片...',
    },
    segmentation: {
      processingInBatches: '正在以 {{batches}} 批次开始 {{count}} 张图片的分割...',
      batchQueued: '批次 {{current}}/{{total}} 成功排队',
      batchQueuedFallback: '批次 {{current}}/{{total}} 成功排队（备用端点）',
      batchError: '处理批次 {{current}}/{{total}} 时出错',
      partialSuccess: '分割：{{success}} 张图片成功排队，{{failed}} 张失败',
      allSuccess: '分割：所有 {{count}} 张图片成功排队',
      allFailed: '分割：所有 {{count}} 张图片失败',
      startedImages: '已开始 {{count}} 张图片的分割',
      queuedLocallyWarning: '{{count}} 张图片的分割已本地排队。服务器连接失败。',
    },
    loading: '正在加载项目...',
    notFound: '项目未找到',
    error: '加载项目时出错',
    empty: '此项目为空',
    noImagesText: '在此项目中未找到图片',
    addImages: '添加图片以开始',
    noImages: {
      title: '还没有图片',
      description: '该项目还没有任何图片。上传图片以开始使用。',
      uploadButton: '上传图片',
    },
    deleteProject: '删除项目',
    deleteConfirmation: '您确定要删除项目 "{{projectName}}" 吗？此操作无法撤消。',
    resegmentImage: '重新分割图片',
    deleteImage: '删除图片',
  },
  editor: {
    error: '错误',
    success: '成功',
    edit: '编辑',
    create: '创建',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    close: '关闭',
    confirm: '确认',
    undo: '撤销',
    redo: '重做',
    tools: {
      select: '选择',
      pan: '平移',
      zoom: '缩放',
      draw: '绘制',
      erase: '擦除',
      measure: '测量',
    },
    confirmDelete: '您确定要删除这个吗？',
    unsavedChanges: '您有未保存的更改。您确定要离开吗？',
    autoSave: {
      enabled: '自动保存已启用',
      disabled: '自动保存已禁用',
      saving: '正在自动保存...',
      saved: '自动保存完成',
      failed: '自动保存失败',
    },
    backButtonTooltip: '返回项目概览',
    exportButtonTooltip: '导出当前分割数据',
    saveTooltip: '保存更改',
    image: '图片',
    previousImage: '上一张图片',
    nextImage: '下一张图片',
    resegmentButton: '重新分割',
    resegmentButtonTooltip: '在此图片上再次运行分割',
    exportMaskButton: '导出遮罩',
    exportMaskButtonTooltip: '为此图片导出分割遮罩',
    backButton: '返回',
    exportButton: '导出',
    saveButton: '保存',
    loadingProject: '正在加载项目...',
    loadingImage: '正在加载图片...',
    sliceErrorInvalidPolygon: '无法切片：选择的多边形无效。',
    sliceWarningInvalidResult: '切片创建的多边形太小且无效。',
    sliceWarningInvalidIntersections: '无效切片：切片线必须在恰好两个点与多边形相交。',
    sliceSuccess: '多边形切片成功。',
    noPolygonToSlice: '没有可切片的多边形。',
    savingTooltip: '正在保存...',
  },
  common: {
    appName: 'Spheroid Segmentation',
    appNameShort: 'SpheroSeg',
    loading: '正在加载...',
    loadingAccount: '正在加载您的账户...',
    loadingApplication: '正在加载应用程序...',
    selectAll: '全选',
    deselectAll: '取消全选',
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    create: '创建',
    search: '搜索',
    error: '错误',
    success: '成功',
    reset: '重置',
    clear: '清除',
    close: '关闭',
    back: '返回',
    signIn: '登录',
    signUp: '注册',
    signOut: '登出',
    signingIn: '正在登录...',
    settings: '设置',
    profile: '个人资料',
    dashboard: '仪表板',
    project: '项目',
    projects: '项目',
    newProject: '新项目',
    upload: '上传',
    download: '下载',
    removeAll: '全部删除',
    uploadImages: '上传图片',
    recentAnalyses: '最近的分析',
    noProjects: '未找到项目',
    noImages: '未找到图片',
    createYourFirst: '创建您的第一个项目以开始使用',
    tryAgain: '重试',
    email: '电子邮件',
    password: '密码',
    confirmPassword: '确认密码',
    firstName: '名字',
    lastName: '姓氏',
    username: '用户名',
    name: '名称',
    description: '描述',
    date: '日期',
    status: '状态',
    image: '图片',
    projectName: '项目名称',
    projectDescription: '项目描述',
    language: '语言',
    theme: '主题',
    light: '浅色',
    dark: '深色',
    system: '系统',
    welcome: '欢迎使用球体分割平台',
    account: '账户',
    passwordConfirm: '确认密码',
    manageAccount: '管理账户',
    changePassword: '更改密码',
    deleteAccount: '删除账户',
    requestAccess: '请求访问',
    accessRequest: '访问请求',
    createAccount: '创建账户',
    signInToAccount: '登录账户',
    termsOfService: '服务条款',
    privacyPolicy: '隐私政策',
    termsOfServiceLink: '服务条款',
    privacyPolicyLink: '隐私政策',
    optional: '可选',
    saveChanges: '保存更改',
    saving: '正在保存',
    notSpecified: '未指定',
    enable: '启用',
    disable: '禁用',
    backToHome: '返回主页',
    and: '和',
    lastChange: '最后更改',
    sort: '排序',
    emailPlaceholder: '输入您的电子邮件',
    passwordPlaceholder: '输入您的密码',
    export: '导出',
    selectImages: '选择图片',
    noImagesDescription: '上传图片以开始您的项目',
    yes: '是',
    no: '否',
    images: '图片',
    files: '文件',
    validationFailed: '验证失败',
    cropAvatar: '裁剪头像',
    profileTitle: '个人资料',
    profileDescription: '更新其他用户可见的个人资料信息',
    profileUsername: '用户名',
    profileUsernamePlaceholder: '输入您的用户名',
    profileFullName: '全名',
    profileFullNamePlaceholder: '输入您的全名',
    profileTitlePlaceholder: '例如：研究员、教授',
    profileOrganization: '组织',
    profileOrganizationPlaceholder: '输入您的组织或机构',
    profileBio: '简介',
    profileBioPlaceholder: '写一份关于您自己的简短介绍',
    profileBioDescription: '简要描述您的研究兴趣和专业知识',
    profileLocation: '位置',
    profileLocationPlaceholder: '例如：中国北京',
    profileSaveButton: '保存个人资料',
    actions: '操作',
    view: '查看',
    share: '分享',
    projectNamePlaceholder: '输入项目名称',
    projectDescPlaceholder: '输入项目描述',
    creatingProject: '正在创建项目...',
    createSuccess: '项目创建成功',
    unauthorized: '您无权执行此操作',
    forbidden: '访问被禁止',
    maxFileSize: '最大文件大小：{{size}}MB',
    accepted: '已接受',
    processing: '处理中...',
    uploading: '正在上传...',
    uploadComplete: '上传完成',
    uploadFailed: '上传失败',
    deletePolygon: '删除多边形',
    pleaseLogin: '请登录以继续',
    retry: '重试',
    segmentation: '分割',
    copiedToClipboard: '已复制到剪贴板！',
    failedToCopy: '复制到剪贴板失败',
    confirm: '确认',
  },
  auth: {
    signIn: '登录',
    signUp: '注册',
    signOut: '登出',
    email: '电子邮件',
    password: '密码',
    forgotPassword: '忘记密码？',
    rememberMe: '记住我',
    noAccount: '没有账户？',
    alreadyHaveAccount: '已有账户？',
    createAccount: '创建账户',
    or: '或',
    signingIn: '正在登录...',
    signingUp: '正在注册...',
    signInError: '登录错误',
    signUpError: '注册错误',
    accountLocked: '您的账户已被锁定。请联系支持。',
    fillAllFields: '请填写所有必填字段',
    serverError: '服务器错误。请稍后再试。',
    signInFailed: '登录失败。请检查您的凭据。',
    alreadyHaveAccess: '已有访问权限？',
    alreadyLoggedInMessage: '您已登录',
    alreadyLoggedInTitle: '已登录',
    backToSignIn: '返回登录',
    creatingAccount: '正在创建账户...',
    dontHaveAccount: '没有账户？',
    emailAddressLabel: '电子邮件地址',
    emailAlreadyExists: '电子邮件已存在',
    emailHasPendingRequest: '此电子邮件有待处理的请求',
    emailPlaceholder: '输入您的电子邮件',
    enterEmail: '输入您的电子邮件地址',
    enterEmailForReset: '输入您的电子邮件以重置密码',
    enterInfoCreateAccount: '输入您的信息以创建账户',
    firstNamePlaceholder: '名字',
    forgotPasswordLink: '忘记密码？',
    forgotPasswordTitle: '重置您的密码',
    invalidCredentials: '无效的凭据',
    invalidTokenAction: '无效的令牌或操作',
    lastNamePlaceholder: '姓氏',
    needAnAccount: '需要一个账户？',
    newPasswordLabel: '新密码',
    confirmPasswordLabel: '确认密码',
    requestAccessTitle: '申请访问权限',
    requestSent: '请求已发送',
    requestSentMessage: '您的访问请求已提交。我们会尽快与您联系。',
    resendEmail: '重新发送电子邮件',
    resetPasswordEmailSent: '重置密码电子邮件已发送',
    resetPasswordInstructions: '请检查您的电子邮件以获取重置密码的说明。',
    signUpFailed: '注册失败。请稍后再试。',
    signUpSuccess: '注册成功！',
    signUpSuccessMessage: '您的账户已创建。请检查您的电子邮件以验证您的账户。',
    submitRequest: '提交请求',
    welcomeBack: '欢迎回来',
    signInToYourAccount: '登录您的账户',
    emailVerificationRequired: '需要电子邮件验证',
    emailVerificationSent: '验证电子邮件已发送到您的电子邮件地址。',
    resendVerificationEmail: '重新发送验证电子邮件',
    checkYourEmail: '检查您的电子邮件',
    invalidEmail: '无效的电子邮件',
    passwordChanged: '密码已更改',
    emailLabel: '电子邮件',
    nameLabel: '姓名',
    institutionLabel: '机构',
    reasonLabel: '原因',
    weWillContact: '我们会与您联系',
    agreeToTerms: '同意条款',
  },
  projects: {
    title: '项目',
    createProject: '创建项目',
    noProjects: '未找到项目',
    loading: '正在加载项目...',
    error: '加载项目时出错',
    deleteSuccess: '项目删除成功',
    deleteError: '删除项目时出错',
    createProjectDialog: {
      duplicateNameError: '具有此名称的项目已存在',
    },
  },
  settings: {
    title: '设置',
    pageTitle: '设置',
    profile: '个人资料',
    account: '账户',
    appearance: '外观',
    changingPassword: '正在更改密码...',
    generalTitle: '常规设置',
    generalDescription: '管理您的基本账户设置',
    securityTitle: '安全',
    securityDescription: '管理您的密码和安全设置',
    notificationsTitle: '通知',
    notificationsDescription: '配置您希望如何接收通知',
    privacyTitle: '隐私',
    privacyDescription: '控制您的数据和隐私设置',
    language: '语言',
    theme: '主题',
    changePasswordTitle: '更改密码',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmNewPassword: '确认新密码',
    dangerZone: '危险区域',
    dangerZoneDescription: '这些操作是不可逆的。请谨慎操作。',
    deleteAccountTitle: '删除账户',
    deleteAccountDescription: '此操作不可逆。您的所有数据将被永久删除。',
    confirmUsername: '确认您的电子邮件',
    confirmDelete: '确认删除',
    accountDeleted: '账户已删除',
    accountDeleteError: '删除账户时出错',
    profileUpdated: '个人资料已更新',
    profileUpdateError: '更新个人资料时出错',
    passwordUpdated: '密码已更新',
    passwordUpdateError: '更新密码时出错',
    oldPassword: '旧密码',
    selectLanguage: '选择语言',
    uploadAvatar: '上传头像',
    removeAvatar: '删除头像',
    fullName: '全名',
    organization: '组织',
  },
  profile: {
    title: '个人资料',
    editProfile: '编辑个人资料',
    joined: '加入时间',
    statistics: '统计',
    recentActivity: '最近活动',
    totalProjects: '项目总数',
    totalImages: '图片总数',
    storageUsed: '已用存储',
    projects: '项目',
    images: '图片',
    analyses: '分析',
    noRecentActivity: '没有最近的活动',
    aboutMe: '关于我',
    noBio: '没有可用的简介',
    fetchError: '加载个人资料时出错',
    updateError: '更新个人资料时出错',
    updateSuccess: '个人资料更新成功',
    avatarHelp: '上传个人头像',
    avatarImageOnly: '仅限图片文件',
    avatarTooLarge: '图片太大。最大大小为 5MB',
    avatarUpdated: '头像更新成功',
    avatarUploadError: '上传头像时出错',
    avatarRemoved: '头像删除成功',
    avatarRemoveError: '删除头像时出错',
    cropAvatarDescription: '调整您的个人头像',
    saveButton: '保存更改',
    username: '用户名',
    usernamePlaceholder: '输入您的用户名',
    fullName: '全名',
    fullNamePlaceholder: '输入您的全名',
    organization: '组织',
    organizationPlaceholder: '输入您的组织',
    bio: '简介',
    bioPlaceholder: '输入您的简介',
    bioDescription: '简要描述您的研究兴趣和专业知识',
    location: '位置',
    locationPlaceholder: '输入您的位置',
    activityDescription: '您的最近活动',
    imagesUploaded: '上传的图片',
    segmentationsCompleted: '完成的分割',
    pageTitle: '用户个人资料',
    avatar: '头像',
    avatarAlt: '用户头像',
    cropError: '裁剪图片时出错',
    darkTheme: '深色',
    dropzoneText: '将图片拖放到此处或点击选择',
    language: '语言',
    lightTheme: '浅色',
    noImageToUpload: '没有要上传的图片',
    personalInfo: '个人信息',
    preferences: '偏好设置',
    professional: '专业',
    selectAvatar: '选择头像',
    systemTheme: '系统',
    theme: '主题',
    title: '标题',
    titlePlaceholder: '输入您的标题',
  },
};

// Complete remaining sections...
const remainingSections: any = {
  projectsPage: {
    title: '项目',
    description: '管理研究项目',
    createNew: '创建新项目',
    createProject: '创建项目',
    createProjectDesc: '开始新的研究项目',
    projectName: '项目名称',
    projectDescription: '项目描述',
    projectNamePlaceholder: '输入项目名称',
    projectDescriptionPlaceholder: '输入项目描述',
    projectCreated: '项目创建成功',
    projectCreationFailed: '项目创建失败',
    projectDeleted: '项目删除成功',
    projectDeletionFailed: '项目删除失败',
    confirmDelete: '您确定要删除这个项目吗？',
    confirmDeleteDescription: '此操作无法撤消。与此项目相关的所有数据将被永久删除。',
    deleteProject: '删除项目',
    editProject: '编辑项目',
    viewProject: '查看项目',
    projectUpdated: '项目更新成功',
    projectUpdateFailed: '项目更新失败',
    noProjects: '未找到项目',
    createFirstProject: '创建您的第一个项目以开始',
    searchProjects: '搜索项目...',
    filterProjects: '筛选项目',
    sortProjects: '排序项目',
    projectNameRequired: '项目名称是必需的',
    loginRequired: '您必须登录才能创建项目',
    createdAt: '创建于',
    updatedAt: '最后更新',
    imageCount: '图片',
    status: '状态',
    actions: '操作',
    loading: '正在加载项目...',
    error: '加载项目时出错',
    retry: '重试',
    copySegmentations: '复制分割结果',
    resetImageStatus: '重置图片处理状态',
    newProjectTitle: '新项目标题',
    itemsProcessed: '已处理项目',
    items: '项目',
    unknownProject: '未知项目',
    activeTasks: '活跃',
    allTasks: '全部',
    deleteProjectDescription: '此操作将永久删除项目和所有相关数据。',
    deleteWarning: '此操作无法撤消。与此项目相关的所有数据将被永久删除。',
    untitledProject: '无标题项目',
    typeToConfirm: '输入项目名称以确认',
    deleteConfirm: '您确定要删除这个项目吗？',
    confirmDeleteError: '请准确输入项目名称以确认',
    exportProject: '导出项目',
    archived: '已归档',
    completed: '已完成',
    draft: '草稿',
    active: '活跃',
    createDate: '创建日期',
    lastModified: '最后修改',
    projectDescPlaceholder: '输入项目描述',
    creatingProject: '正在创建项目...',
    noImages: {
      title: '还没有图片',
      description: '此项目还没有任何图片。上传图片以开始分割。',
      uploadButton: '上传图片',
    },
    searching: '正在搜索项目...',
    noResults: '未找到匹配的项目',
    deleting: '正在删除...',
  },
  requestAccess: {
    and: '和',
    title: '申请访问球体分割平台',
    description: '填写以下表格以申请访问我们的平台。我们将审核您的请求并尽快与您联系。',
    emailLabel: '您的电子邮件地址',
    nameLabel: '您的姓名',
    institutionLabel: '机构/公司',
    reasonLabel: '访问原因',
    submitRequest: '提交请求',
    requestReceived: '请求已收到',
    thankYou: '感谢您的关注',
    weWillContact: '我们将审核您的请求并尽快与您联系',
    submitSuccess: '请求提交成功！',
    emailPlaceholder: '输入您的电子邮件地址',
    namePlaceholder: '输入您的全名',
    institutionPlaceholder: '输入您的机构或公司名称',
    reasonPlaceholder: '请描述您计划如何使用该平台',
    fillRequired: '请填写所有必填字段',
    submittingRequest: '正在提交请求...',
    submitError: '提交请求失败',
    alreadyPending: '此电子邮件的访问请求已在待处理中',
    agreeToTerms: '提交此请求即表示您同意我们的',
  },
  requestAccessForm: {
    title: '申请访问球体分割平台',
    description: '填写以下表格以申请访问我们的平台。我们将审核您的请求并尽快与您联系。',
    emailLabel: '您的电子邮件地址',
    nameLabel: '您的姓名',
    institutionLabel: '机构/公司',
    reasonLabel: '访问原因',
    submitButton: '提交请求',
    signInPrompt: '已有账户？',
    signInLink: '登录',
    thankYouTitle: '感谢您的关注',
    weWillContact: '我们将审核您的请求并尽快与您联系',
    agreeToTerms: '提交此请求即表示您同意我们的',
    and: '和',
  },
  documentation: {
    tag: '用户指南',
    title: 'SpheroSeg 文档',
    subtitle: '学习如何有效使用球体分割平台。',
    sidebar: {
      title: '章节',
      introduction: '简介',
      gettingStarted: '入门',
      uploadingImages: '上传图片',
      segmentationProcess: '分割过程',
      apiReference: 'API 参考',
    },
    introduction: {
      title: '简介',
      imageAlt: '球体分析工作流程示意图',
      whatIs: {
        title: '什么是 SpheroSeg？',
        paragraph1:
          'SpheroSeg 是一个专为显微图像中细胞球体的分割和分析而设计的尖端平台。我们的工具为研究人员提供精确的检测和分析功能。',
        paragraph2: '它利用基于深度学习的先进 AI 算法，以高精度和一致性自动识别和分割图像中的球体。',
        paragraph3: '本文档将指导您了解使用平台的各个方面，从入门到高级功能和 API 集成。',
      },
    },
    gettingStarted: {
      title: '入门',
      accountCreation: {
        title: '账户创建',
        paragraph1: '要使用 SpheroSeg，您需要创建一个账户。这使我们能够安全地存储您的项目和图片。',
        step1Prefix: '访问',
        step1Link: '注册页面',
        step2: '输入您的机构电子邮件地址并创建密码',
        step3: '使用您的姓名和机构完成您的个人资料',
        step4: '通过发送到您收件箱的链接验证您的电子邮件地址',
      },
      creatingProject: {
        title: '创建您的第一个项目',
        paragraph1: '项目帮助您组织工作。每个项目可以包含多个图片及其相应的分割结果。',
        step1: '在您的仪表板上，点击"新项目"',
        step2: '输入项目名称和描述',
        step3: '选择项目类型（默认：球体分析）',
        step4: '点击"创建项目"继续',
      },
    },
    uploadingImages: {
      title: '上传图片',
      paragraph1: 'SpheroSeg 支持显微镜中常用的各种图片格式，包括 TIFF、PNG 和 JPEG。',
      methods: {
        title: '上传方法',
        paragraph1: '有几种上传图片的方法：',
        step1: '将文件直接拖放到上传区域',
        step2: '点击上传区域从计算机浏览和选择文件',
        step3: '一次批量上传多个图片',
      },
      note: {
        prefix: '注意：',
        text: '为获得最佳结果，请确保您的显微镜图片在球体和背景之间具有良好的对比度。',
      },
    },
    segmentationProcess: {
      title: '分割过程',
      paragraph1: '分割过程识别图片中球体的边界，允许对其形态进行精确分析。',
      automatic: {
        title: '自动分割',
        paragraph1: '我们的 AI 驱动的自动分割可以高精度地检测球体边界：',
        step1: '从您的项目中选择一张图片',
        step2: '点击"自动分割"以启动过程',
        step3: '系统将处理图片并显示检测到的边界',
        step4: '在分割编辑器中查看结果',
      },
      manual: {
        title: '手动调整',
        paragraph1: '有时自动分割可能需要优化。我们的编辑器提供以下工具：',
        step1: '沿边界添加或删除顶点',
        step2: '调整顶点位置以获得更准确的边界',
        step3: '分割或合并区域',
        step4: '在球体内添加或删除孔',
      },
    },
    apiReference: {
      title: 'API 参考',
      paragraph1: 'SpheroSeg 提供 RESTful API 以编程方式访问平台功能。这非常适合与您现有的工作流程或批处理集成。',
      endpoint1Desc: '检索您的所有项目列表',
      endpoint2Desc: '检索特定项目中的所有图片',
      endpoint3Desc: '为特定图片启动分割',
      contactPrefix: '有关完整的 API 文档和身份验证详细信息，请联系我们：',
    },
    backToHome: '返回主页',
    backToTop: '返回顶部',
  },
  hero: {
    platformTag: '先进的球体分割平台',
    title: '用于生物医学研究的 AI 驱动细胞分析',
    subtitle: '使用我们尖端的球体分割平台提升您的显微细胞图像分析。专为寻求精确和效率的研究人员设计。',
    getStartedButton: '开始使用',
    learnMoreButton: '了解更多',
    imageAlt1: '球体显微镜图像',
    imageAlt2: '带分析的球体显微镜图像',
    welcomeTitle: '欢迎来到 SpheroSeg',
    welcomeSubtitle: '用于细胞球体分割和分析的先进平台',
    welcomeDescription: '我们的平台将尖端的人工智能算法与直观的界面相结合，用于显微图像中细胞球体的精确检测和分析。',
    featuresTitle: '强大的功能',
    featuresSubtitle: '用于生物医学研究的先进工具',
    featureAiSegmentation: '先进分割',
    featureAiSegmentationDesc: '精确的球体检测与边界分析，实现准确的细胞测量。',
    featureEditing: 'AI 驱动分析',
    featureEditingDesc: '利用深度学习算法进行自动检测和细胞分类。',
    featureAnalytics: '轻松上传',
    featureAnalyticsDesc: '拖放您的显微镜图像以立即处理和分析。',
    featureExport: '统计洞察',
    featureExportDesc: '全面的指标和可视化以提取有意义的数据模式。',
    ctaTitle: '准备好改变您的细胞分析工作流程了吗？',
    ctaSubtitle: '加入已经使用我们平台加速发现的领先研究人员。',
    ctaButton: '创建账户',
  },
  navbar: {
    home: '主页',
    features: '功能',
    documentation: '文档',
    terms: '条款',
    privacy: '隐私',
    login: '登录',
    requestAccess: '请求访问',
  },
  navigation: {
    home: '主页',
    projects: '项目',
    settings: '设置',
    profile: '个人资料',
    dashboard: '仪表板',
    back: '返回',
  },
  dashboard: {
    title: '仪表板',
    manageProjects: '管理和组织您的研究项目',
    viewMode: {
      grid: '网格视图',
      list: '列表视图',
    },
    sort: {
      name: '名称',
      updatedAt: '最后更新',
      segmentationStatus: '状态',
    },
    search: '搜索项目...',
    searchImagesPlaceholder: '搜索图片...',
    noProjects: '未找到项目',
    noImagesDescription: '没有符合您搜索条件的图片',
    createFirst: '创建您的第一个项目以开始',
    createNew: '创建新项目',
    lastChange: '最后更改',
    statsOverview: '统计概览',
    totalProjects: '项目总数',
    activeProjects: '活跃项目',
    totalImages: '图片总数',
    totalAnalyses: '分析总数',
    lastUpdated: '最后更新',
    noProjectsDescription: '您还没有创建任何项目。创建您的第一个项目以开始。',
    searchProjectsPlaceholder: '按名称搜索项目...',
    sortBy: '排序方式',
    name: '名称',
    completed: '已完成',
    processing: '处理中',
    pending: '待处理',
    failed: '失败',
    selectImagesButton: '选择图片',
  },
  // ... Continue with remaining sections ...
};

// Merge all sections
Object.assign(zhTranslations, remainingSections);

// Add all remaining sections from English that are missing
for (const key in enTranslations) {
  if (!(key in zhTranslations)) {
    zhTranslations[key] = translateSection(enTranslations[key]);
  }
}

// Helper function to translate remaining sections
function translateSection(section: any): any {
  if (typeof section === 'string') {
    // Return placeholder translation for now
    return `[需要翻译] ${section}`;
  } else if (Array.isArray(section)) {
    return section.map((item) => translateSection(item));
  } else if (typeof section === 'object' && section !== null) {
    const result: any = {};
    for (const key in section) {
      result[key] = translateSection(section[key]);
    }
    return result;
  }
  return section;
}

// Generate properly formatted output
const output = `// Chinese translations
export default ${JSON.stringify(zhTranslations, null, 2)
  .replace(/"/g, "'")
  .replace(/: '/g, ": '")
  .replace(/',$/gm, "',")
  .replace(/\[\n\s+'/g, "['")
  .replace(/'\n\s+\]/g, "']")
  .replace(/,\n\s+'/g, ", '")};
`;

// Write the file
const zhPath = path.join(process.cwd(), 'src/translations/zh.ts');
fs.writeFileSync(zhPath, output, 'utf-8');

console.log('Chinese translation file has been fixed and completed!');
console.log('Note: Some sections still have [需要翻译] placeholders that need proper translations.');
