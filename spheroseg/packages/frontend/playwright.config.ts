import { defineConfig, devices } from '@playwright/test';
import { createCachedConfig, testCache } from './playwright-cache.config';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// require('dotenv').config();

// Clear cache if requested via environment variable
if (process.env.CLEAR_TEST_CACHE) {
  console.log('Clearing test cache...');
  testCache.clearAllCache();
}

// Show cache statistics
if (process.env.SHOW_CACHE_STATS) {
  const stats = testCache.getStats();
  console.log('Test Cache Statistics:');
  console.log(`- Total cached: ${stats.total}`);
  console.log(`- Passed: ${stats.passed}`);
  console.log(`- Failed: ${stats.failed}`);
  console.log(`- Time saved: ${(stats.totalDuration / 1000).toFixed(2)}s`);
}

/**
 * See https://playwright.dev/docs/test-configuration.
 */
const baseConfig = defineConfig({
  testDir: './e2e',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Global timeout for each test */
  timeout: 600 * 1000, // 10 minutes per test (increased for upload tests)
  /* Global timeout for each assertion */
  expect: {
    timeout: 60 * 1000, // 60 seconds for assertions (increased for upload flows)
  },
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['junit', { outputFile: 'playwright-report/results.xml' }],
    ['list'],
    ['./test-cache-reporter.ts'],
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    /* Take screenshot on failure */
    screenshot: 'only-on-failure',

    /* Video on failure */
    video: 'retain-on-failure',
    
    /* Timeout for each action like click, fill, etc. */
    actionTimeout: 60 * 1000, // 60 seconds (increased for file uploads)
    
    /* Navigation timeout */
    navigationTimeout: 120 * 1000, // 120 seconds (increased for upload processing)
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* Test against mobile viewports. */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },

    /* Test against branded browsers. */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
});

// Export cached configuration if caching is enabled
export default process.env.DISABLE_TEST_CACHE ? baseConfig : createCachedConfig(baseConfig);