# Frontend Environment Variables Example
# Copy this file to .env and update with your values

# API Configuration
VITE_API_URL=http://localhost:5001
VITE_API_BASE_URL=/api
VITE_ASSETS_URL=http://localhost:8080

# Logging Configuration
# Log Levels: DEBUG, INFO, WARN, ERROR, NONE
# Production default: INFO, Development default: DEBUG
VITE_LOG_LEVEL=INFO
VITE_ENABLE_CONSOLE_LOGS=true
VITE_ENABLE_LOG_SHIPPING=false
VITE_MAX_MEMORY_LOGS=1000
VITE_LOG_SERVER_ENDPOINT=/api/logs
VITE_LOG_SHIP_INTERVAL=30000