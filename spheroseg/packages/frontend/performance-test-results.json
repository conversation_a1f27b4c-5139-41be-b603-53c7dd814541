
> @spheroseg/frontend@1.0.0 test:e2e
> playwright test --grep performance --reporter=json

Initializing test cache...
Cache stats: 4 tests cached (1 passed, 3 failed)
{
  "config": {
    "configFile": "/home/<USER>/spheroseg/spheroseg/packages/frontend/playwright.config.ts",
    "rootDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e",
    "forbidOnly": false,
    "fullyParallel": true,
    "globalSetup": null,
    "globalTeardown": null,
    "globalTimeout": 0,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 0,
    "metadata": {
      "actualWorkers": 2
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 2
        },
        "id": "chromium",
        "name": "chromium",
        "testDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 300000
      },
      {
        "outputDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 2
        },
        "id": "firefox",
        "name": "firefox",
        "testDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 300000
      },
      {
        "outputDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 2
        },
        "id": "webkit",
        "name": "webkit",
        "testDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 300000
      },
      {
        "outputDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 2
        },
        "id": "Mobile Chrome",
        "name": "Mobile Chrome",
        "testDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 300000
      },
      {
        "outputDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 2
        },
        "id": "Mobile Safari",
        "name": "Mobile Safari",
        "testDir": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 300000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.52.0",
    "workers": 2,
    "webServer": {
      "command": "npm run dev",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000
    }
  },
  "suites": [
    {
      "title": "performance/performance-benchmarks.spec.ts",
      "file": "performance/performance-benchmarks.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Performance Benchmarks",
          "file": "performance/performance-benchmarks.spec.ts",
          "line": 86,
          "column": 6,
          "specs": [
            {
              "title": "home page load performance",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 6384,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Home page load time: 1012.56ms\n"
                        },
                        {
                          "text": "Navigation timing: {\n  domContentLoaded: \u001b[33m0\u001b[39m,\n  loadComplete: \u001b[33m0\u001b[39m,\n  domInteractive: \u001b[33m58.5\u001b[39m,\n  responseTime: \u001b[33m5.299999237060547\u001b[39m\n}\n"
                        },
                        {
                          "text": "Web Vitals: { FCP: \u001b[33m0\u001b[39m, LCP: \u001b[33m0\u001b[39m, CLS: \u001b[33m0\u001b[39m, FID: \u001b[33m0\u001b[39m, TTFB: \u001b[33m4.59999942779541\u001b[39m }\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:39:24.332Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-6241cc015a28cdc1e221",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 113,
              "column": 3
            },
            {
              "title": "sign-in page performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 1,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 32184,
                      "error": {
                        "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:149:16",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 16,
                          "line": 149
                        },
                        "snippet": "  147 |     \n  148 |     // Measure form interaction performance\n> 149 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  150 |     await page.fill('input[name=\"password\"]', 'password123');\n  151 |     \n  152 |     const interactionStart = performance.now();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 16,
                            "line": 149
                          },
                          "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n\n  147 |     \n  148 |     // Measure form interaction performance\n> 149 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  150 |     await page.fill('input[name=\"password\"]', 'password123');\n  151 |     \n  152 |     const interactionStart = performance.now();\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:149:16"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "Sign-in page load time: 945.30ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:39:24.294Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-chromium/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 16,
                        "line": 149
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-1605edc0b3a8c1ea15bd",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 138,
              "column": 3
            },
            {
              "title": "image lazy loading performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 451,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:165:24",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 24,
                          "line": 165
                        },
                        "snippet": "  163 |     // Check that images are lazy loaded\n  164 |     const lazyImages = await page.$$eval('img[loading=\"lazy\"]', imgs => imgs.length);\n> 165 |     expect(lazyImages).toBeGreaterThan(0);\n      |                        ^\n  166 |     \n  167 |     // Measure time to load visible images\n  168 |     const imageLoadStart = performance.now();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 24,
                            "line": 165
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n  163 |     // Check that images are lazy loaded\n  164 |     const lazyImages = await page.$$eval('img[loading=\"lazy\"]', imgs => imgs.length);\n> 165 |     expect(lazyImages).toBeGreaterThan(0);\n      |                        ^\n  166 |     \n  167 |     // Measure time to load visible images\n  168 |     const imageLoadStart = performance.now();\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:165:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:39:30.840Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-chromium/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 24,
                        "line": 165
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-43c3e01c2ac074ce5fa2",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 160,
              "column": 3
            },
            {
              "title": "route transition performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 2,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 30663,
                      "error": {
                        "message": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:194:14",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 14,
                          "line": 194
                        },
                        "snippet": "  192 |       await Promise.all([\n  193 |         page.waitForURL(transition.to),\n> 194 |         page.click(`a[href=\"${transition.to}\"]`),\n      |              ^\n  195 |       ]);\n  196 |       const transitionTime = performance.now() - transitionStart;\n  197 |       "
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 14,
                            "line": 194
                          },
                          "message": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n\n\n  192 |       await Promise.all([\n  193 |         page.waitForURL(transition.to),\n> 194 |         page.click(`a[href=\"${transition.to}\"]`),\n      |              ^\n  195 |       ]);\n  196 |       const transitionTime = performance.now() - transitionStart;\n  197 |       \n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:194:14"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:39:31.782Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-chromium/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 14,
                        "line": 194
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-46f2fc2ccc65a5f67513",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 176,
              "column": 3
            },
            {
              "title": "memory usage monitoring",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 4056,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Memory increase: 0.00MB\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:39:57.080Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-d9dff022fb17d080ea81",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 203,
              "column": 3
            },
            {
              "title": "bundle size impact",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 1073,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Total JavaScript bundle size: 0.10MB\n"
                        },
                        {
                          "text": "Individual bundles: [\n  {\n    name: \u001b[32m'@radix-ui_react-dropdown-menu.js?v=e2db31af'\u001b[39m,\n    size: \u001b[32m'47.25KB'\u001b[39m\n  },\n  { name: \u001b[32m'chunk-Q3ATRNQA.js?v=e2db31af'\u001b[39m, size: \u001b[32m'9.67KB'\u001b[39m },\n  { name: \u001b[32m'chunk-EOLSW7PA.js?v=e2db31af'\u001b[39m, size: \u001b[32m'0.67KB'\u001b[39m },\n  { name: \u001b[32m'chunk-6WBQ3PW6.js?v=e2db31af'\u001b[39m, size: \u001b[32m'1.88KB'\u001b[39m },\n  { name: \u001b[32m'chunk-U4NFJIXU.js?v=e2db31af'\u001b[39m, size: \u001b[32m'3.72KB'\u001b[39m },\n  { name: \u001b[32m'chunk-5OTOAP5L.js?v=e2db31af'\u001b[39m, size: \u001b[32m'37.34KB'\u001b[39m }\n]\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:01.204Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-79bf29c30b72a96596ac",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 241,
              "column": 3
            },
            {
              "title": "API response time benchmarks",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 490,
                      "error": {
                        "message": "TypeError: response.timing is not a function",
                        "stack": "TypeError: response.timing is not a function\n    at Page.<anonymous> (/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:279:33)",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 33,
                          "line": 279
                        },
                        "snippet": "  277 |       const url = response.url();\n  278 |       if (url.includes('/api/')) {\n> 279 |         const timing = response.timing();\n      |                                 ^\n  280 |         apiCalls.push({\n  281 |           url,\n  282 |           status: response.status(),"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 33,
                            "line": 279
                          },
                          "message": "TypeError: response.timing is not a function\n\n  277 |       const url = response.url();\n  278 |       if (url.includes('/api/')) {\n> 279 |         const timing = response.timing();\n      |                                 ^\n  280 |         apiCalls.push({\n  281 |           url,\n  282 |           status: response.status(),\n    at Page.<anonymous> (/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:279:33)"
                        },
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 16,
                            "line": 289
                          },
                          "message": "Error: page.fill: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n\n  287 |     \n  288 |     // Trigger API call\n> 289 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  290 |     await page.fill('input[name=\"password\"]', 'password123');\n  291 |     await page.click('button[type=\"submit\"]');\n  292 |     \n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:289:16"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:02.281Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-chromium/video.webm"
                        },
                        {
                          "name": "_error-context-1",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 33,
                        "line": 279
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-3aeba6dd0a87f3571bb6",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 271,
              "column": 3
            },
            {
              "title": "rendering performance under load",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 4,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 5873,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m162\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m162\u001b[39m\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:361:35",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 35,
                          "line": 361
                        },
                        "snippet": "  359 |     \n  360 |     expect(avgFPS).toBeGreaterThan(30); // Should maintain at least 30fps\n> 361 |     expect(renderingMetrics.jank).toBeLessThan(10); // Minimal jank\n      |                                   ^\n  362 |   });\n  363 | });\n  364 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 35,
                            "line": 361
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m162\u001b[39m\n\n  359 |     \n  360 |     expect(avgFPS).toBeGreaterThan(30); // Should maintain at least 30fps\n> 361 |     expect(renderingMetrics.jank).toBeLessThan(10); // Minimal jank\n      |                                   ^\n  362 |   });\n  363 | });\n  364 |\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:361:35"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "Average FPS: 61.70\n"
                        },
                        {
                          "text": "Jank frames: 162\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:03.085Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-chromium/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-chromium/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 35,
                        "line": 361
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-ef9b44a6774e0384deaa",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 306,
              "column": 3
            },
            {
              "title": "home page load performance",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 6687,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Home page load time: 1051.22ms\n"
                        },
                        {
                          "text": "Navigation timing: {\n  domContentLoaded: \u001b[33m0\u001b[39m,\n  loadComplete: \u001b[33m1\u001b[39m,\n  domInteractive: \u001b[33m224\u001b[39m,\n  responseTime: \u001b[33m4\u001b[39m\n}\n"
                        },
                        {
                          "text": "Web Vitals: { FCP: \u001b[33m0\u001b[39m, LCP: \u001b[33m0\u001b[39m, CLS: \u001b[33m0\u001b[39m, FID: \u001b[33m0\u001b[39m, TTFB: \u001b[33m4\u001b[39m }\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:09.571Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-fcf099ff8012a18a34bc",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 113,
              "column": 3
            },
            {
              "title": "sign-in page performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 7,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 31692,
                      "error": {
                        "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:149:16",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 16,
                          "line": 149
                        },
                        "snippet": "  147 |     \n  148 |     // Measure form interaction performance\n> 149 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  150 |     await page.fill('input[name=\"password\"]', 'password123');\n  151 |     \n  152 |     const interactionStart = performance.now();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 16,
                            "line": 149
                          },
                          "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n\n  147 |     \n  148 |     // Measure form interaction performance\n> 149 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  150 |     await page.fill('input[name=\"password\"]', 'password123');\n  151 |     \n  152 |     const interactionStart = performance.now();\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:149:16"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "Sign-in page load time: 1021.10ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:14.872Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-firefox/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-firefox/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-firefox/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 16,
                        "line": 149
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-f219b573988a4caebce6",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 138,
              "column": 3
            },
            {
              "title": "image lazy loading performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 576,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:165:24",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 24,
                          "line": 165
                        },
                        "snippet": "  163 |     // Check that images are lazy loaded\n  164 |     const lazyImages = await page.$$eval('img[loading=\"lazy\"]', imgs => imgs.length);\n> 165 |     expect(lazyImages).toBeGreaterThan(0);\n      |                        ^\n  166 |     \n  167 |     // Measure time to load visible images\n  168 |     const imageLoadStart = performance.now();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 24,
                            "line": 165
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n  163 |     // Check that images are lazy loaded\n  164 |     const lazyImages = await page.$$eval('img[loading=\"lazy\"]', imgs => imgs.length);\n> 165 |     expect(lazyImages).toBeGreaterThan(0);\n      |                        ^\n  166 |     \n  167 |     // Measure time to load visible images\n  168 |     const imageLoadStart = performance.now();\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:165:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:16.531Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-firefox/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-firefox/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-firefox/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 24,
                        "line": 165
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-9ab3a9fa55643b63b454",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 160,
              "column": 3
            },
            {
              "title": "route transition performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 8,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 31090,
                      "error": {
                        "message": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:194:14",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 14,
                          "line": 194
                        },
                        "snippet": "  192 |       await Promise.all([\n  193 |         page.waitForURL(transition.to),\n> 194 |         page.click(`a[href=\"${transition.to}\"]`),\n      |              ^\n  195 |       ]);\n  196 |       const transitionTime = performance.now() - transitionStart;\n  197 |       "
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 14,
                            "line": 194
                          },
                          "message": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n\n\n  192 |       await Promise.all([\n  193 |         page.waitForURL(transition.to),\n> 194 |         page.click(`a[href=\"${transition.to}\"]`),\n      |              ^\n  195 |       ]);\n  196 |       const transitionTime = performance.now() - transitionStart;\n  197 |       \n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:194:14"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:17.955Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-firefox/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-firefox/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-firefox/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 14,
                        "line": 194
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-650dd945dff3aace0843",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 176,
              "column": 3
            },
            {
              "title": "memory usage monitoring",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 9,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 1378,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:47.724Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-8cb53de529ad704d120e",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 203,
              "column": 3
            },
            {
              "title": "bundle size impact",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 9,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 1238,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Total JavaScript bundle size: 0.10MB\n"
                        },
                        {
                          "text": "Individual bundles: [\n  {\n    name: \u001b[32m'@radix-ui_react-dropdown-menu.js?v=e2db31af'\u001b[39m,\n    size: \u001b[32m'47.25KB'\u001b[39m\n  },\n  { name: \u001b[32m'chunk-Q3ATRNQA.js?v=e2db31af'\u001b[39m, size: \u001b[32m'9.67KB'\u001b[39m },\n  { name: \u001b[32m'chunk-EOLSW7PA.js?v=e2db31af'\u001b[39m, size: \u001b[32m'0.67KB'\u001b[39m },\n  { name: \u001b[32m'chunk-5OTOAP5L.js?v=e2db31af'\u001b[39m, size: \u001b[32m'37.34KB'\u001b[39m },\n  { name: \u001b[32m'chunk-6WBQ3PW6.js?v=e2db31af'\u001b[39m, size: \u001b[32m'1.88KB'\u001b[39m },\n  { name: \u001b[32m'chunk-U4NFJIXU.js?v=e2db31af'\u001b[39m, size: \u001b[32m'3.72KB'\u001b[39m }\n]\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:49.382Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-f59ae6cfbe208bfbf8d1",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 241,
              "column": 3
            },
            {
              "title": "API response time benchmarks",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 10,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 31146,
                      "error": {
                        "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:289:16",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 16,
                          "line": 289
                        },
                        "snippet": "  287 |     \n  288 |     // Trigger API call\n> 289 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  290 |     await page.fill('input[name=\"password\"]', 'password123');\n  291 |     await page.click('button[type=\"submit\"]');\n  292 |     "
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 16,
                            "line": 289
                          },
                          "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n\n  287 |     \n  288 |     // Trigger API call\n> 289 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  290 |     await page.fill('input[name=\"password\"]', 'password123');\n  291 |     await page.click('button[type=\"submit\"]');\n  292 |     \n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:289:16"
                        },
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 33,
                            "line": 279
                          },
                          "message": "TypeError: response.timing is not a function\n\n  277 |       const url = response.url();\n  278 |       if (url.includes('/api/')) {\n> 279 |         const timing = response.timing();\n      |                                 ^\n  280 |         apiCalls.push({\n  281 |           url,\n  282 |           status: response.status(),\n    at Page.<anonymous> (/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:279:33)"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:50.371Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-firefox/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-firefox/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-firefox/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 16,
                        "line": 289
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-be9abc21ff776418d7ce",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 271,
              "column": 3
            },
            {
              "title": "rendering performance under load",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 9,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 5610,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m176\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m176\u001b[39m\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:361:35",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 35,
                          "line": 361
                        },
                        "snippet": "  359 |     \n  360 |     expect(avgFPS).toBeGreaterThan(30); // Should maintain at least 30fps\n> 361 |     expect(renderingMetrics.jank).toBeLessThan(10); // Minimal jank\n      |                                   ^\n  362 |   });\n  363 | });\n  364 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 35,
                            "line": 361
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m176\u001b[39m\n\n  359 |     \n  360 |     expect(avgFPS).toBeGreaterThan(30); // Should maintain at least 30fps\n> 361 |     expect(renderingMetrics.jank).toBeLessThan(10); // Minimal jank\n      |                                   ^\n  362 |   });\n  363 | });\n  364 |\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:361:35"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "Average FPS: 60.38\n"
                        },
                        {
                          "text": "Jank frames: 176\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:50.628Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-firefox/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-firefox/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-firefox/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 35,
                        "line": 361
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-9cb6b9526790e19005da",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 306,
              "column": 3
            },
            {
              "title": "home page load performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 12,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:09.393Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-f303e--home-page-load-performance-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-c0d80cef4e3e75ff50e8",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 113,
              "column": 3
            },
            {
              "title": "sign-in page performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 13,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:09.988Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-5ec311469d0175035c80",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 138,
              "column": 3
            },
            {
              "title": "image lazy loading performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 14,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 4,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:10.588Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-2568389cb5d8f9f2fb50",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 160,
              "column": 3
            },
            {
              "title": "route transition performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 15,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:11.181Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-6f7b99b058f94ac6a675",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 176,
              "column": 3
            },
            {
              "title": "memory usage monitoring",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 16,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:11.775Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-341a6-rks-memory-usage-monitoring-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-9d69484a3e35934a08dc",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 203,
              "column": 3
            },
            {
              "title": "bundle size impact",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 17,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:12.373Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-947db-nchmarks-bundle-size-impact-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-ccc5493e1d50f182cd5b",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 241,
              "column": 3
            },
            {
              "title": "API response time benchmarks",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 18,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:12.966Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-70cebc8779964bf35abc",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 271,
              "column": 3
            },
            {
              "title": "rendering performance under load",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 19,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:13.554Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-e72431e40aa7f9ac1987",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 306,
              "column": 3
            },
            {
              "title": "home page load performance",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 21,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 6379,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Home page load time: 850.47ms\n"
                        },
                        {
                          "text": "Navigation timing: {\n  domContentLoaded: \u001b[33m0\u001b[39m,\n  loadComplete: \u001b[33m0\u001b[39m,\n  domInteractive: \u001b[33m58\u001b[39m,\n  responseTime: \u001b[33m1.6000003814697266\u001b[39m\n}\n"
                        },
                        {
                          "text": "Web Vitals: { FCP: \u001b[33m0\u001b[39m, LCP: \u001b[33m0\u001b[39m, CLS: \u001b[33m0\u001b[39m, FID: \u001b[33m0\u001b[39m, TTFB: \u001b[33m1.3000001907348633\u001b[39m }\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:14.745Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-7ff4e21e5e6ba14eee22",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 113,
              "column": 3
            },
            {
              "title": "sign-in page performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 21,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 31560,
                      "error": {
                        "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:149:16",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 16,
                          "line": 149
                        },
                        "snippet": "  147 |     \n  148 |     // Measure form interaction performance\n> 149 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  150 |     await page.fill('input[name=\"password\"]', 'password123');\n  151 |     \n  152 |     const interactionStart = performance.now();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 16,
                            "line": 149
                          },
                          "message": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n\n  147 |     \n  148 |     // Measure form interaction performance\n> 149 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  150 |     await page.fill('input[name=\"password\"]', 'password123');\n  151 |     \n  152 |     const interactionStart = performance.now();\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:149:16"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "Sign-in page load time: 761.41ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:21.194Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-Mobile-Chrome/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-Mobile-Chrome/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-Mobile-Chrome/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 16,
                        "line": 149
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-f6569b744ca0d5e9ccee",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 138,
              "column": 3
            },
            {
              "title": "image lazy loading performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 22,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 971,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:165:24",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 24,
                          "line": 165
                        },
                        "snippet": "  163 |     // Check that images are lazy loaded\n  164 |     const lazyImages = await page.$$eval('img[loading=\"lazy\"]', imgs => imgs.length);\n> 165 |     expect(lazyImages).toBeGreaterThan(0);\n      |                        ^\n  166 |     \n  167 |     // Measure time to load visible images\n  168 |     const imageLoadStart = performance.now();"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 24,
                            "line": 165
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n  163 |     // Check that images are lazy loaded\n  164 |     const lazyImages = await page.$$eval('img[loading=\"lazy\"]', imgs => imgs.length);\n> 165 |     expect(lazyImages).toBeGreaterThan(0);\n      |                        ^\n  166 |     \n  167 |     // Measure time to load visible images\n  168 |     const imageLoadStart = performance.now();\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:165:24"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:22.675Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-Mobile-Chrome/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-Mobile-Chrome/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-Mobile-Chrome/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 24,
                        "line": 165
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-6e2da8f2c7a1df046ecd",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 160,
              "column": 3
            },
            {
              "title": "route transition performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 23,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 30698,
                      "error": {
                        "message": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n",
                        "stack": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:194:14",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 14,
                          "line": 194
                        },
                        "snippet": "  192 |       await Promise.all([\n  193 |         page.waitForURL(transition.to),\n> 194 |         page.click(`a[href=\"${transition.to}\"]`),\n      |              ^\n  195 |       ]);\n  196 |       const transitionTime = performance.now() - transitionStart;\n  197 |       "
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 14,
                            "line": 194
                          },
                          "message": "TimeoutError: page.click: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('a[href=\"/about\"]')\u001b[22m\n\n\n  192 |       await Promise.all([\n  193 |         page.waitForURL(transition.to),\n> 194 |         page.click(`a[href=\"${transition.to}\"]`),\n      |              ^\n  195 |       ]);\n  196 |       const transitionTime = performance.now() - transitionStart;\n  197 |       \n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:194:14"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:24.206Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-Mobile-Chrome/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-Mobile-Chrome/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-Mobile-Chrome/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 14,
                        "line": 194
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-251b35c81f25a4971b66",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 176,
              "column": 3
            },
            {
              "title": "memory usage monitoring",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 24,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 4124,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Memory increase: 0.00MB\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:53.267Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-a9c3e7885f5e92d5d648",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 203,
              "column": 3
            },
            {
              "title": "bundle size impact",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 25,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 1463,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "Total JavaScript bundle size: 0.10MB\n"
                        },
                        {
                          "text": "Individual bundles: [\n  {\n    name: \u001b[32m'@radix-ui_react-dropdown-menu.js?v=e2db31af'\u001b[39m,\n    size: \u001b[32m'47.25KB'\u001b[39m\n  },\n  { name: \u001b[32m'chunk-EOLSW7PA.js?v=e2db31af'\u001b[39m, size: \u001b[32m'0.67KB'\u001b[39m },\n  { name: \u001b[32m'chunk-6WBQ3PW6.js?v=e2db31af'\u001b[39m, size: \u001b[32m'1.88KB'\u001b[39m },\n  { name: \u001b[32m'chunk-U4NFJIXU.js?v=e2db31af'\u001b[39m, size: \u001b[32m'3.72KB'\u001b[39m },\n  { name: \u001b[32m'chunk-Q3ATRNQA.js?v=e2db31af'\u001b[39m, size: \u001b[32m'9.67KB'\u001b[39m },\n  { name: \u001b[32m'chunk-5OTOAP5L.js?v=e2db31af'\u001b[39m, size: \u001b[32m'37.34KB'\u001b[39m }\n]\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:55.500Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-25c2c4c4041815fd0706",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 241,
              "column": 3
            },
            {
              "title": "API response time benchmarks",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 25,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 491,
                      "error": {
                        "message": "TypeError: response.timing is not a function",
                        "stack": "TypeError: response.timing is not a function\n    at Page.<anonymous> (/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:279:33)",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 33,
                          "line": 279
                        },
                        "snippet": "  277 |       const url = response.url();\n  278 |       if (url.includes('/api/')) {\n> 279 |         const timing = response.timing();\n      |                                 ^\n  280 |         apiCalls.push({\n  281 |           url,\n  282 |           status: response.status(),"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 33,
                            "line": 279
                          },
                          "message": "TypeError: response.timing is not a function\n\n  277 |       const url = response.url();\n  278 |       if (url.includes('/api/')) {\n> 279 |         const timing = response.timing();\n      |                                 ^\n  280 |         apiCalls.push({\n  281 |           url,\n  282 |           status: response.status(),\n    at Page.<anonymous> (/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:279:33)"
                        },
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 16,
                            "line": 289
                          },
                          "message": "Error: page.fill: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"email\"]')\u001b[22m\n\n\n  287 |     \n  288 |     // Trigger API call\n> 289 |     await page.fill('input[name=\"email\"]', '<EMAIL>');\n      |                ^\n  290 |     await page.fill('input[name=\"password\"]', 'password123');\n  291 |     await page.click('button[type=\"submit\"]');\n  292 |     \n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:289:16"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:57.038Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-Mobile-Chrome/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-Mobile-Chrome/video.webm"
                        },
                        {
                          "name": "_error-context-1",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-Mobile-Chrome/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 33,
                        "line": 279
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-fa48dc8e9d9952a81598",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 271,
              "column": 3
            },
            {
              "title": "rendering performance under load",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 24,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 5612,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m165\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m165\u001b[39m\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:361:35",
                        "location": {
                          "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                          "column": 35,
                          "line": 361
                        },
                        "snippet": "  359 |     \n  360 |     expect(avgFPS).toBeGreaterThan(30); // Should maintain at least 30fps\n> 361 |     expect(renderingMetrics.jank).toBeLessThan(10); // Minimal jank\n      |                                   ^\n  362 |   });\n  363 | });\n  364 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                            "column": 35,
                            "line": 361
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m10\u001b[39m\nReceived:   \u001b[31m165\u001b[39m\n\n  359 |     \n  360 |     expect(avgFPS).toBeGreaterThan(30); // Should maintain at least 30fps\n> 361 |     expect(renderingMetrics.jank).toBeLessThan(10); // Minimal jank\n      |                                   ^\n  362 |   });\n  363 | });\n  364 |\n    at /home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts:361:35"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "Average FPS: 60.32\n"
                        },
                        {
                          "text": "Jank frames: 165\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:57.463Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-Mobile-Chrome/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-Mobile-Chrome/video.webm"
                        },
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-Mobile-Chrome/error-context.md"
                        }
                      ],
                      "errorLocation": {
                        "file": "/home/<USER>/spheroseg/spheroseg/packages/frontend/e2e/performance/performance-benchmarks.spec.ts",
                        "column": 35,
                        "line": 361
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-51880fe612617333f2a0",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 306,
              "column": 3
            },
            {
              "title": "home page load performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 27,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:03.589Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-f303e--home-page-load-performance-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-927267fe765d5f8a280d",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 113,
              "column": 3
            },
            {
              "title": "sign-in page performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 28,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:04.401Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-30e0e-ks-sign-in-page-performance-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-860604f1fefa67b980f2",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 138,
              "column": 3
            },
            {
              "title": "image lazy loading performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 29,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:04.992Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-18634-ge-lazy-loading-performance-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-b9dd2a7b5ac446e78924",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 160,
              "column": 3
            },
            {
              "title": "route transition performance",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 30,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:05.577Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-a7461-oute-transition-performance-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-71f59fa88ee47127c066",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 176,
              "column": 3
            },
            {
              "title": "memory usage monitoring",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 31,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:06.175Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-341a6-rks-memory-usage-monitoring-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-366908819153f035fd1e",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 203,
              "column": 3
            },
            {
              "title": "bundle size impact",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 32,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:06.753Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-947db-nchmarks-bundle-size-impact-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-47ef6c1a5793da48848b",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 241,
              "column": 3
            },
            {
              "title": "API response time benchmarks",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 33,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:07.340Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-6b721-PI-response-time-benchmarks-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-aab19de9f93698a9b037",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 271,
              "column": 3
            },
            {
              "title": "rendering performance under load",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 34,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:07.915Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-735d2-ring-performance-under-load-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-de787902dd522d2cdd9d",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 306,
              "column": 3
            }
          ]
        },
        {
          "title": "Performance Regression Tests",
          "file": "performance/performance-benchmarks.spec.ts",
          "line": 365,
          "column": 6,
          "specs": [
            {
              "title": "compare performance against baseline",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 5,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 10943,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "home performance: {\n  FCP: \u001b[32m'420ms (baseline: 1200ms)'\u001b[39m,\n  LCP: \u001b[32m'536ms (baseline: 2000ms)'\u001b[39m,\n  CLS: \u001b[32m'0.000 (baseline: 0.05)'\u001b[39m\n}\n"
                        },
                        {
                          "text": "about performance: {\n  FCP: \u001b[32m'244ms (baseline: 1000ms)'\u001b[39m,\n  LCP: \u001b[32m'244ms (baseline: 1800ms)'\u001b[39m,\n  CLS: \u001b[32m'0.000 (baseline: 0.03)'\u001b[39m\n}\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:03.322Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-04dae9d04231830413a6",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 366,
              "column": 3
            },
            {
              "title": "compare performance against baseline",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [
                    {
                      "workerIndex": 11,
                      "parallelIndex": 1,
                      "status": "passed",
                      "duration": 11208,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "home performance: {\n  FCP: \u001b[32m'0ms (baseline: 1200ms)'\u001b[39m,\n  LCP: \u001b[32m'580ms (baseline: 2000ms)'\u001b[39m,\n  CLS: \u001b[32m'0.000 (baseline: 0.05)'\u001b[39m\n}\n"
                        },
                        {
                          "text": "about performance: {\n  FCP: \u001b[32m'0ms (baseline: 1000ms)'\u001b[39m,\n  LCP: \u001b[32m'353ms (baseline: 1800ms)'\u001b[39m,\n  CLS: \u001b[32m'0.000 (baseline: 0.03)'\u001b[39m\n}\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:40:57.101Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-9f3f801b87f49f429a24",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 366,
              "column": 3
            },
            {
              "title": "compare performance against baseline",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 20,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:14.140Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-e66dd-erformance-against-baseline-webkit/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-f353c01b338019d34088",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 366,
              "column": 3
            },
            {
              "title": "compare performance against baseline",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Chrome",
                  "projectName": "Mobile Chrome",
                  "results": [
                    {
                      "workerIndex": 26,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 11002,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "home performance: {\n  FCP: \u001b[32m'348ms (baseline: 1200ms)'\u001b[39m,\n  LCP: \u001b[32m'348ms (baseline: 2000ms)'\u001b[39m,\n  CLS: \u001b[32m'0.000 (baseline: 0.05)'\u001b[39m\n}\n"
                        },
                        {
                          "text": "about performance: {\n  FCP: \u001b[32m'308ms (baseline: 1000ms)'\u001b[39m,\n  LCP: \u001b[32m'308ms (baseline: 1800ms)'\u001b[39m,\n  CLS: \u001b[32m'0.000 (baseline: 0.03)'\u001b[39m\n}\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:41:58.142Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-0d6909fb151a24a3af64",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 366,
              "column": 3
            },
            {
              "title": "compare performance against baseline",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 300000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "Mobile Safari",
                  "projectName": "Mobile Safari",
                  "results": [
                    {
                      "workerIndex": 35,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 3,
                      "error": {
                        "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝",
                        "stack": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                      },
                      "errors": [
                        {
                          "message": "Error: browserType.launch: \n╔══════════════════════════════════════════════════════╗\n║ Host system is missing dependencies to run browsers. ║\n║ Missing libraries:                                   ║\n║     libgtk-4.so.1                                    ║\n║     libgraphene-1.0.so.0                             ║\n║     libwoff2dec.so.1.0.2                             ║\n║     libvpx.so.9                                      ║\n║     libevent-2.1.so.7                                ║\n║     libgstallocators-1.0.so.0                        ║\n║     libgstapp-1.0.so.0                               ║\n║     libgstpbutils-1.0.so.0                           ║\n║     libgstaudio-1.0.so.0                             ║\n║     libgsttag-1.0.so.0                               ║\n║     libgstvideo-1.0.so.0                             ║\n║     libgstgl-1.0.so.0                                ║\n║     libgstcodecparsers-1.0.so.0                      ║\n║     libgstfft-1.0.so.0                               ║\n║     libflite.so.1                                    ║\n║     libflite_usenglish.so.1                          ║\n║     libflite_cmu_grapheme_lang.so.1                  ║\n║     libflite_cmu_grapheme_lex.so.1                   ║\n║     libflite_cmu_indic_lang.so.1                     ║\n║     libflite_cmu_indic_lex.so.1                      ║\n║     libflite_cmulex.so.1                             ║\n║     libflite_cmu_time_awb.so.1                       ║\n║     libflite_cmu_us_awb.so.1                         ║\n║     libflite_cmu_us_kal16.so.1                       ║\n║     libflite_cmu_us_kal.so.1                         ║\n║     libflite_cmu_us_rms.so.1                         ║\n║     libflite_cmu_us_slt.so.1                         ║\n║     libwebpdemux.so.2                                ║\n║     libavif.so.16                                    ║\n║     libharfbuzz-icu.so.0                             ║\n║     libwebpmux.so.3                                  ║\n║     libenchant-2.so.2                                ║\n║     libsecret-1.so.0                                 ║\n║     libhyphen.so.0                                   ║\n║     libmanette-0.2.so.0                              ║\n║     libx264.so                                       ║\n╚══════════════════════════════════════════════════════╝"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-14T08:42:08.496Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "_error-context-0",
                          "contentType": "text/markdown",
                          "path": "/home/<USER>/spheroseg/spheroseg/packages/frontend/test-results/performance-performance-be-e66dd-erformance-against-baseline-Mobile-Safari/error-context.md"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "ff9eaa55db4a79537a8d-b4f59ecd94d78de7df86",
              "file": "performance/performance-benchmarks.spec.ts",
              "line": 366,
              "column": 3
            }
          ]
        }
      ]
    }
  ],
  "errors": [],
  "stats": {
    "startTime": "2025-07-14T08:39:23.660Z",
    "duration": 165592.239,
    "expected": 12,
    "skipped": 0,
    "unexpected": 33,
    "flaky": 0
  }
}
npm error Lifecycle script `test:e2e` failed with error:
npm error code 1
npm error path /home/<USER>/spheroseg/spheroseg/packages/frontend
npm error workspace @spheroseg/frontend@1.0.0
npm error location /home/<USER>/spheroseg/spheroseg/packages/frontend
npm error command failed
npm error command sh -c playwright test --grep performance --reporter=json
