# Optimized Frontend Dockerfile with multi-stage build
# Reduces image size and improves build performance

# Stage 1: Dependencies
FROM node:18-alpine AS deps
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY packages/frontend/package*.json ./packages/frontend/
COPY packages/shared/package*.json ./packages/shared/
COPY packages/types/package*.json ./packages/types/

# Install dependencies with cache mount
RUN --mount=type=cache,target=/root/.npm \
    npm ci

# Stage 2: Build
FROM node:18-alpine AS builder
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/packages ./packages

# Copy source code
COPY packages/frontend ./packages/frontend
COPY packages/shared ./packages/shared
COPY packages/types ./packages/types
COPY turbo.json ./
COPY tsconfig.json ./

# Set build arguments
ARG NODE_ENV=production
ARG VITE_API_URL
ARG VITE_API_BASE_URL=/api
ARG VITE_ASSETS_URL

ENV NODE_ENV=$NODE_ENV
ENV VITE_API_URL=$VITE_API_URL
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_ASSETS_URL=$VITE_ASSETS_URL

# Build the application with optimizations
RUN npm run build:frontend -- --mode production

# Analyze bundle size (optional, remove for faster builds)
# RUN cd packages/frontend && npm run analyze:bundle

# Stage 3: Production server
FROM nginx:alpine AS runtime

# Install runtime dependencies
RUN apk add --no-cache \
    tini \
    && rm -rf /var/cache/apk/*

# Copy custom nginx config
COPY packages/frontend/nginx.optimized.conf /etc/nginx/conf.d/default.conf

# Copy built files from builder stage
COPY --from=builder /app/packages/frontend/dist /usr/share/nginx/html

# Add security headers and optimizations
RUN echo 'server_tokens off;' > /etc/nginx/conf.d/security.conf

# Create nginx cache directory
RUN mkdir -p /var/cache/nginx/client_temp \
    && chmod -R 755 /var/cache/nginx

# Use non-root user
RUN chown -R nginx:nginx /usr/share/nginx/html \
    && chown -R nginx:nginx /var/cache/nginx \
    && chown -R nginx:nginx /var/log/nginx

USER nginx

# Expose port
EXPOSE 80

# Use tini for proper signal handling
ENTRYPOINT ["/sbin/tini", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:80 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]