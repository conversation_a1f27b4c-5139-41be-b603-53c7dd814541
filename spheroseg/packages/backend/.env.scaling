# Backend Environment Variables for Horizontal Scaling
# This configuration uses the ML load balancer for improved throughput

# Node Environment
NODE_ENV=production

# Server Configuration
PORT=5001
HOST=0.0.0.0
APP_URL=http://localhost:5001
ALLOWED_ORIGINS=http://localhost:3000,http://localhost

# Database Configuration
DB_HOST=db
DB_PORT=5432
DB_NAME=spheroseg
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false
DB_MAX_CONNECTIONS=20  # Increased for higher load

# Authentication
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=1d
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d
BCRYPT_SALT_ROUNDS=10
SESSION_TIMEOUT=3600
TOKEN_SECURITY_MODE=standard

# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_DIR=./logs

# ML Service with Load Balancer
ML_SERVICE_URL=http://ml-load-balancer:5003  # Use HAProxy load balancer
ML_MAX_RETRIES=3
ML_RETRY_DELAY=5000
ML_MAX_CONCURRENT_TASKS=10  # Increased for multiple ML instances
ML_HEALTH_CHECK_INTERVAL=30000  # More frequent health checks
ML_QUEUE_UPDATE_INTERVAL=2000  # Faster queue updates

# ML Metrics (if using Prometheus)
ML_METRICS_URL=http://ml-load-balancer:8405/metrics
ENABLE_ML_LOAD_BALANCING=true

# Storage
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=50000000
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/tiff,image/tif,image/bmp
DEFAULT_USER_STORAGE_LIMIT=10737418240
MAX_TOTAL_STORAGE_BYTES=107374182400
STORAGE_WARNING_THRESHOLD=0.8
TEMP_FILE_MAX_AGE_HOURS=24
CLEANUP_SCHEDULE_HOURS=6
ENABLE_ORPHANED_FILE_CLEANUP=true
THUMBNAIL_QUALITY=80
THUMBNAIL_MAX_WIDTH=300
THUMBNAIL_MAX_HEIGHT=300

# Monitoring
METRICS_ENABLED=true
REQUEST_TIMEOUT_MS=60000  # Increased for potentially longer processing
METRICS_PREFIX=spheroseg_

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_LEVEL=info
MEMORY_WARNING_THRESHOLD_MB=800
MEMORY_CRITICAL_THRESHOLD_MB=900

# Security
RATE_LIMIT_REQUESTS=200  # Increased for higher throughput
RATE_LIMIT_WINDOW=60
ENABLE_HELMET=true
ENABLE_RATE_LIMIT=true
CSRF_ENABLED=true
USE_REDIS_RATE_LIMIT=true  # Use Redis for distributed rate limiting
IP_WHITELIST=

# Redis (for caching and rate limiting)
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
ENABLE_REDIS_CACHE=true
REDIS_CACHE_TTL=300

# Email (optional)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=
EMAIL_PASS=

# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
RABBITMQ_QUEUE=segmentation_tasks
RABBITMQ_PREFETCH_COUNT=4  # Match ML service prefetch

# Connection Pool Settings
CONNECTION_POOL_SIZE=25
CONNECTION_POOL_TIMEOUT=30000

# WebSocket Configuration
WEBSOCKET_PING_INTERVAL=30000
WEBSOCKET_PING_TIMEOUT=5000
WEBSOCKET_RECONNECT_INTERVAL=1000

# Cleanup and Maintenance
ENABLE_AUTO_CLEANUP=true
CLEANUP_INTERVAL_HOURS=1
MAX_PROCESSING_TIME_MINUTES=30
STUCK_TASK_THRESHOLD_MINUTES=60