import { IResolvers } from '@graphql-tools/utils';

const segmentationResolvers: IResolvers = {
  Query: {
    // Segmentation query resolvers will be implemented here
  },
  Mutation: {
    // Segmentation mutation resolvers will be implemented here
  },
  Subscription: {
    // Segmentation subscription resolvers will be implemented here
  },
  SegmentationResult: {
    // SegmentationResult field resolvers will be implemented here
  }
};

export default segmentationResolvers;