# Segmentation-related types and operations

type SegmentationResult implements Node {
  id: ID!
  image: Image!
  status: SegmentationStatus!
  createdAt: DateTime!
  updatedAt: DateTime!
  completedAt: DateTime
  
  # Result data
  polygons: [Polygon!]!
  cellCount: Int!
  parameters: JSONObject
  processingTime: Float
  processedBy: String
  
  # Error info
  error: String
  errorDetails: JSONObject
  
  # Features
  features: SegmentationFeatures
}

type Polygon {
  id: Int!
  points: [[Float!]!]!
  class: String!
  confidence: Float!
  area: Float
  perimeter: Float
  centroid: [Float!]
  boundingBox: BoundingBox
  metadata: JSONObject
}

type BoundingBox {
  x: Float!
  y: Float!
  width: Float!
  height: Float!
}

type SegmentationFeatures {
  totalCells: Int!
  averageCellArea: Float!
  cellDensity: Float!
  coveragePercentage: Float!
  sizeDistribution: [SizeDistribution!]!
  classDistribution: [ClassDistribution!]!
}

type SizeDistribution {
  range: String!
  count: Int!
  percentage: Float!
}

type ClassDistribution {
  class: String!
  count: Int!
  percentage: Float!
}

type SegmentationTask {
  id: ID!
  taskId: String!
  image: Image!
  status: TaskStatus!
  priority: Int!
  createdAt: DateTime!
  startedAt: DateTime
  completedAt: DateTime
  attempts: Int!
  lastError: String
  estimatedTime: Int
  position: Int
}

enum TaskStatus {
  QUEUED
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

type SegmentationQueue {
  totalTasks: Int!
  queuedTasks: Int!
  processingTasks: Int!
  completedTasks: Int!
  failedTasks: Int!
  averageWaitTime: Float!
  averageProcessingTime: Float!
  estimatedTimeRemaining: Int!
  mlServiceStatus: MLServiceStatus!
}

type MLServiceStatus {
  healthy: Boolean!
  instances: [MLInstance!]!
  totalCapacity: Int!
  currentLoad: Int!
  loadPercentage: Float!
}

type MLInstance {
  id: String!
  status: String!
  activeTasks: Int!
  cpuUsage: Float!
  memoryUsage: Float!
  uptime: Int!
}

# Input types
input SegmentationInput {
  imageIds: [ID!]!
  parameters: SegmentationParameters
  priority: Int = 0
}

input SegmentationParameters {
  threshold: Float
  minCellSize: Int
  maxCellSize: Int
  enhanceContrast: Boolean
  denoise: Boolean
  customParameters: JSONObject
}

input UpdatePolygonInput {
  points: [[Float!]!]
  class: String
  metadata: JSONObject
}

# Extend root types
extend type Query {
  # Segmentation results
  segmentation(imageId: ID!): SegmentationResult @auth
  segmentations(projectId: ID!, pagination: PaginationInput): [SegmentationResult!]! @auth
  
  # Queue status
  segmentationQueue: SegmentationQueue! @auth
  segmentationTask(taskId: String!): SegmentationTask @auth
  segmentationTasks(status: TaskStatus, pagination: PaginationInput): [SegmentationTask!]! @auth
  
  # ML service status
  mlServiceStatus: MLServiceStatus! @auth
}

extend type Mutation {
  # Start segmentation
  startSegmentation(input: SegmentationInput!): [SegmentationTask!]! @auth @requiresApproval
  startProjectSegmentation(projectId: ID!, parameters: SegmentationParameters): [SegmentationTask!]! @auth
  
  # Cancel segmentation
  cancelSegmentation(taskId: String!): MutationResponse! @auth
  cancelSegmentations(taskIds: [String!]!): MutationResponse! @auth
  
  # Retry failed segmentation
  retrySegmentation(imageId: ID!): SegmentationTask! @auth
  retryFailedSegmentations(projectId: ID!): [SegmentationTask!]! @auth
  
  # Manual polygon editing
  addPolygon(segmentationId: ID!, polygon: UpdatePolygonInput!): Polygon! @auth
  updatePolygon(segmentationId: ID!, polygonId: Int!, input: UpdatePolygonInput!): Polygon! @auth
  deletePolygon(segmentationId: ID!, polygonId: Int!): MutationResponse! @auth
  
  # Delete segmentation results
  deleteSegmentation(imageId: ID!): MutationResponse! @auth
  deleteSegmentations(imageIds: [ID!]!): MutationResponse! @auth
}

extend type Subscription {
  # Segmentation progress
  segmentationProgress(taskId: String!): SegmentationTask! @auth
  segmentationCompleted(projectId: ID!): SegmentationResult! @auth
  
  # Queue updates
  queueStatusUpdated: SegmentationQueue! @auth
  
  # ML service updates
  mlServiceStatusUpdated: MLServiceStatus! @auth
}