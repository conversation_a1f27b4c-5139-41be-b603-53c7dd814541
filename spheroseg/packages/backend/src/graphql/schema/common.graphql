# Common types and interfaces used across the schema

scalar DateTime
scalar JSON
scalar JSONObject
scalar Upload

# Pagination input
input PaginationInput {
  offset: Int = 0
  limit: Int = 20
  sortBy: String
  sortOrder: SortOrder = DESC
}

enum SortOrder {
  ASC
  DESC
}

# Generic response interfaces
interface Response {
  success: Boolean!
  message: String
}

interface PaginatedResponse {
  items: [Node!]!
  total: Int!
  offset: Int!
  limit: Int!
  hasMore: Boolean!
}

interface Node {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
}

# Error types
type Error {
  code: String!
  message: String!
  field: String
}

type MutationResponse implements Response {
  success: Boolean!
  message: String
  errors: [Error!]
}

# File upload types
type FileUploadResponse {
  url: String!
  filename: String!
  size: Int!
  mimeType: String!
}

# Directives
directive @auth on FIELD_DEFINITION
directive @requiresApproval on FIELD_DEFINITION
directive @requiresAdmin on FIELD_DEFINITION
directive @rateLimit(
  max: Int!
  window: String!
) on FIELD_DEFINITION

# Root types
type Query {
  _empty: String
}

type Mutation {
  _empty: String
}

type Subscription {
  _empty: String
}