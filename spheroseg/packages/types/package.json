{"name": "@spheroseg/types", "version": "1.0.0", "description": "Shared types for the SpheroSeg application", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts", "lint:fix": "npx eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "clean": "rimraf dist node_modules .turbo", "test": "jest --passWithNoTests", "test:coverage": "jest --coverage --passWithNoTests"}, "devDependencies": {"@types/estree": "^1.0.8", "@types/jest": "^29.5.14", "@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.1", "jest": "^29.7.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "ts-jest": "^29.1.2", "typescript": "^5.8.3"}}