#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run consolidation checks on staged files
echo "🔍 Running consolidation checks..."

# Get list of staged TypeScript/JavaScript files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|tsx|js|jsx)$' | grep -E '^packages/(frontend|backend|shared)/')

if [ -z "$STAGED_FILES" ]; then
  echo "No relevant files to check"
  exit 0
fi

# Run consolidation check
npm run check:consolidation -- --packages frontend,backend,shared

# Check the exit code
if [ $? -ne 0 ]; then
  echo "❌ Consolidation check failed. Please fix the issues before committing."
  exit 1
fi

echo "✅ Consolidation check passed"