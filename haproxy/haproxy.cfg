global
    log stdout local0
    maxconn 4096
    user haproxy
    group haproxy
    daemon
    
    # Performance tuning
    tune.ssl.default-dh-param 2048
    nbthread 4
    cpu-map 1-4 0-3

defaults
    log     global
    mode    http
    option  httplog
    option  dontlognull
    option  http-server-close
    option  forwardfor       except 127.0.0.0/8
    option  redispatch
    retries 3
    timeout http-request    10s
    timeout queue           1m
    timeout connect         10s
    timeout client          1h  # Long timeout for ML processing
    timeout server          1h  # Long timeout for ML processing
    timeout http-keep-alive 10s
    timeout check           10s
    maxconn                 3000

# Statistics page
stats enable
stats uri /stats
stats realm HAProxy\ Statistics
stats auth admin:spheroseg123

# ML Service Load Balancing
frontend ml_frontend
    bind *:5003
    option httplog
    
    # Health check endpoint passthrough
    acl health_check path /health
    use_backend ml_health if health_check
    
    # Default to ML backend
    default_backend ml_backend

# Health check backend (direct, no queueing)
backend ml_health
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Dynamic server discovery would go here in production
    server ml1 ml:5002 check inter 5s fall 3 rise 2
    server ml2 ml:5002 check inter 5s fall 3 rise 2
    server ml3 ml:5002 check inter 5s fall 3 rise 2

# Main ML processing backend
backend ml_backend
    balance leastconn  # Use least connections for better distribution
    option httpchk GET /health
    http-check expect status 200
    
    # Retry policy
    retry-on all-retryable-errors
    option redispatch
    
    # Queueing configuration
    timeout queue 5m  # Max time in queue
    
    # Server configuration with health checks
    server ml1 ml:5002 check inter 10s fall 3 rise 2 maxconn 10 weight 100
    server ml2 ml:5002 check inter 10s fall 3 rise 2 maxconn 10 weight 100
    server ml3 ml:5002 check inter 10s fall 3 rise 2 maxconn 10 weight 100

# Prometheus metrics endpoint
frontend prometheus_frontend
    bind *:8405
    http-request use-service prometheus-exporter
    no log