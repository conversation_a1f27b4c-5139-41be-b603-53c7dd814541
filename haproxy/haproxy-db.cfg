global
    log stdout local0
    maxconn 1000
    user haproxy
    group haproxy
    daemon

defaults
    log     global
    mode    tcp
    option  tcplog
    option  dontlognull
    option  redispatch
    retries 3
    timeout connect     5s
    timeout client      30m
    timeout server      30m
    timeout check       5s
    maxconn             900

# Statistics
stats enable
stats uri /stats
stats realm PostgreSQL\ Load\ Balancer\ Statistics
stats auth admin:spheroseg123

# Read-only backend (port 5435) - load balanced across replicas
listen postgres_read
    bind *:5435
    mode tcp
    option tcpka
    option tcplog
    option pgsql-check user postgres
    balance leastconn
    default-server inter 3s fall 3 rise 2 on-marked-down shutdown-sessions
    
    # Replicas only for read queries
    server replica1 db-replica1:5432 check port 5432 maxconn 300 weight 100
    server replica2 db-replica2:5432 check port 5432 maxconn 300 weight 100
    # Master as backup for reads if all replicas are down
    server master db:5432 check port 5432 maxconn 300 weight 50 backup

# Read-write backend (port 5436) - master only
listen postgres_write
    bind *:5436
    mode tcp
    option tcpka
    option tcplog
    option pgsql-check user postgres
    balance first
    default-server inter 3s fall 3 rise 2 on-marked-down shutdown-sessions
    
    # Only master for write queries
    server master db:5432 check port 5432 maxconn 300

# Health check endpoint
listen health_check
    bind *:8080
    mode http
    monitor-uri /health
    stats enable