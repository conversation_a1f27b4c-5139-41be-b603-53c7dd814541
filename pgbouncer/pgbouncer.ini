[databases]
# Main database with connection pooling
spheroseg = host=db port=5432 dbname=spheroseg
spheroseg_read = host=db-load-balancer port=5435 dbname=spheroseg
spheroseg_write = host=db-load-balancer port=5436 dbname=spheroseg

# Template for dynamic database connections
* = host=db port=5432

[pgbouncer]
# Connection settings
listen_addr = *
listen_port = 6432
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
auth_query = SELECT usename, passwd FROM pg_shadow WHERE usename=$1
admin_users = postgres
stats_users = postgres, pgbouncer_stats

# Pool settings
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 25
min_pool_size = 5
reserve_pool_size = 5
reserve_pool_timeout = 3
max_db_connections = 100
max_user_connections = 100

# Timeouts
server_idle_timeout = 600
server_lifetime = 3600
server_connect_timeout = 15
server_login_retry = 15
query_timeout = 0
query_wait_timeout = 120
client_idle_timeout = 0
client_login_timeout = 60
autodb_idle_timeout = 3600

# Low-level tuning
pkt_buf = 4096
max_packet_size = 2147483647
listen_backlog = 128
sbuf_loopcnt = 5
tcp_defer_accept = 0
tcp_socket_buffer = 0
tcp_keepalive = 1
tcp_keepcnt = 0
tcp_keepidle = 0
tcp_keepintvl = 0

# DNS
dns_max_ttl = 15
dns_nxdomain_ttl = 15
dns_zone_check_period = 0

# Logging
log_connections = 1
log_disconnections = 1
log_pooler_errors = 1
log_stats = 1
stats_period = 60
verbose = 0
syslog = 0

# Console access
ignore_startup_parameters = extra_float_digits

# TLS settings
;client_tls_sslmode = disable
;client_tls_ca_file = 
;client_tls_cert_file = 
;client_tls_key_file = 

# Dangerous timeouts
;server_reset_query = DISCARD ALL
server_reset_query = 
server_reset_query_always = 0

# Misc
conffile = /etc/pgbouncer/pgbouncer.ini
service_name = pgbouncer
job_name = pgbouncer
%include /etc/pgbouncer/pgbouncer-other.ini