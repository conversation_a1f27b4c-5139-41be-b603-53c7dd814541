version: '3.8'

services:
  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: spheroseg-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/alerts:/etc/prometheus/alerts:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - spheroseg-network
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=prometheus"

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: spheroseg-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=spheroseg
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - spheroseg-network
    depends_on:
      - prometheus
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=grafana"

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: spheroseg-node-exporter
    command:
      - '--path.rootfs=/host'
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/host:ro
    ports:
      - "9100:9100"
    networks:
      - spheroseg-network
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=node-exporter"

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: spheroseg-postgres-exporter
    environment:
      DATA_SOURCE_NAME: "**************************************/spheroseg?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - spheroseg-network
    depends_on:
      - db
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=postgres-exporter"

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: spheroseg-redis-exporter
    environment:
      REDIS_ADDR: "redis:6379"
    ports:
      - "9121:9121"
    networks:
      - spheroseg-network
    depends_on:
      - redis
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=redis-exporter"

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: spheroseg-cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - spheroseg-network
    privileged: true
    devices:
      - /dev/kmsg
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=cadvisor"

  # HAProxy Exporter (if HAProxy stats are enabled)
  haproxy-exporter:
    image: prom/haproxy-exporter:latest
    container_name: spheroseg-haproxy-exporter
    command:
      - '--haproxy.scrape-uri=http://haproxy:8404/stats;csv'
    ports:
      - "9101:9101"
    networks:
      - spheroseg-network
    depends_on:
      - haproxy
    labels:
      - "com.spheroseg.service=monitoring"
      - "com.spheroseg.component=haproxy-exporter"

volumes:
  prometheus_data:
  grafana_data:

networks:
  spheroseg-network:
    external: true