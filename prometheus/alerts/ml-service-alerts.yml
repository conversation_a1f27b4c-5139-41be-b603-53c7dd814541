groups:
  - name: ml_service_alerts
    interval: 30s
    rules:
      # Alert when ML service instances are down
      - alert: MLServiceDown
        expr: up{job="ml-service"} == 0
        for: 2m
        labels:
          severity: critical
          service: ml-service
        annotations:
          summary: "ML Service instance {{ $labels.instance }} is down"
          description: "ML Service instance {{ $labels.instance }} has been down for more than 2 minutes."

      # Alert when too few ML service instances are running
      - alert: MLServiceInstancesLow
        expr: count(up{job="ml-service"} == 1) < 2
        for: 5m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "Too few ML service instances running"
          description: "Only {{ $value }} ML service instance(s) are running. Expected at least 2."

      # Alert on high ML service CPU usage
      - alert: MLServiceHighCPU
        expr: rate(process_cpu_seconds_total{job="ml-service"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "High CPU usage on ML service {{ $labels.instance }}"
          description: "ML service {{ $labels.instance }} CPU usage is {{ $value }}%"

      # Alert on high ML service memory usage
      - alert: MLServiceHighMemory
        expr: (process_resident_memory_bytes{job="ml-service"} / 1024 / 1024) > 1800
        for: 5m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "High memory usage on ML service {{ $labels.instance }}"
          description: "ML service {{ $labels.instance }} is using {{ $value }}MB of memory"

      # Alert on ML task processing errors
      - alert: MLTasksFailureRate
        expr: rate(ml_tasks_processed_total{status="error"}[5m]) > 0.1
        for: 10m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "High ML task failure rate"
          description: "ML task failure rate is {{ $value }} per second"

      # Alert on ML task processing timeouts
      - alert: MLTasksTimeout
        expr: rate(ml_tasks_processed_total{status="timeout"}[5m]) > 0.05
        for: 10m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "ML tasks timing out"
          description: "ML task timeout rate is {{ $value }} per second"

      # Alert on ML queue size
      - alert: MLQueueBacklog
        expr: avg(ml_queue_size) > 50
        for: 10m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "Large ML processing queue backlog"
          description: "ML queue has {{ $value }} tasks waiting"

      # Alert on ML processing time
      - alert: MLProcessingTimeSlow
        expr: histogram_quantile(0.95, rate(ml_task_duration_seconds_bucket[5m])) > 120
        for: 10m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "ML processing time is slow"
          description: "95th percentile ML processing time is {{ $value }} seconds"

      # Alert on unbalanced load distribution
      - alert: MLLoadImbalance
        expr: |
          (max(ml_active_tasks) - min(ml_active_tasks)) > 5
        for: 10m
        labels:
          severity: warning
          service: ml-service
        annotations:
          summary: "ML service load is imbalanced"
          description: "Load difference between ML instances is {{ $value }} tasks"

  - name: haproxy_alerts
    interval: 30s
    rules:
      # Alert when HAProxy is down
      - alert: HAProxyDown
        expr: up{job="haproxy"} == 0
        for: 2m
        labels:
          severity: critical
          service: haproxy
        annotations:
          summary: "HAProxy load balancer is down"
          description: "HAProxy has been down for more than 2 minutes."

      # Alert on HAProxy backend errors
      - alert: HAProxyBackendErrors
        expr: rate(haproxy_backend_http_responses_total{code="5xx"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: haproxy
        annotations:
          summary: "High error rate from HAProxy backend"
          description: "HAProxy backend error rate is {{ $value }} per second"

      # Alert on all ML backends down
      - alert: HAProxyNoHealthyBackends
        expr: haproxy_backend_up{backend="ml_backend"} == 0
        for: 2m
        labels:
          severity: critical
          service: haproxy
        annotations:
          summary: "No healthy ML backends available"
          description: "HAProxy has no healthy ML service backends available"