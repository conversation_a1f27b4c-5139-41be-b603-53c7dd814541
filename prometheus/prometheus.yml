global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'spheroseg-monitor'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - 'alertmanager:9093'

# Load rules once and periodically evaluate them
rule_files:
  - 'alerts/*.yml'

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Backend Node.js service
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:5001']
    metrics_path: '/api/metrics'
    scrape_interval: 10s

  # ML Service instances (via service discovery)
  - job_name: 'ml-service'
    dns_sd_configs:
      - names:
          - 'ml'
        type: 'A'
        port: 5002
    metrics_path: '/metrics'
    scrape_interval: 30s

  # HAProxy load balancer
  - job_name: 'haproxy'
    static_configs:
      - targets: ['haproxy:8404']
    metrics_path: '/metrics'

  # PostgreSQL exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # PostgreSQL replica exporter
  - job_name: 'postgres-replica'
    static_configs:
      - targets: ['postgres-replica-exporter:9188']

  # Redis exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # Docker containers metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metric_relabel_configs:
      # Only keep container metrics
      - source_labels: [__name__]
        regex: 'container_.*'
        action: keep

  # PgBouncer metrics
  - job_name: 'pgbouncer'
    static_configs:
      - targets: ['pgbouncer:9127']

  # NGINX metrics (if nginx-prometheus-exporter is used)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # GraphQL specific metrics from backend
  - job_name: 'graphql'
    static_configs:
      - targets: ['backend:5001']
    metrics_path: '/api/graphql/metrics'
    scrape_interval: 10s

  # WebSocket metrics from backend
  - job_name: 'websocket'
    static_configs:
      - targets: ['backend:5001']
    metrics_path: '/api/websocket/metrics'
    scrape_interval: 10s

# Service discovery for dynamically scaled services
  - job_name: 'ml-scaled'
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        filters:
          - name: label
            values: 
              - "com.spheroseg.service=ml"
    relabel_configs:
      - source_labels: [__meta_docker_container_name]
        target_label: instance
      - source_labels: [__meta_docker_container_label_com_spheroseg_instance]
        target_label: ml_instance