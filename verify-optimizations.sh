#!/bin/bash

# Quick verification of all optimization files

echo "🔍 Verifying Performance Optimization Files"
echo "=========================================="
echo ""

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check file
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✓${NC} $1"
        return 0
    else
        echo -e "${RED}✗${NC} $1 (MISSING)"
        return 1
    fi
}

echo "📁 Backend Optimizations:"
check_file "spheroseg/packages/backend/src/services/userStatsServiceOptimized.ts"
check_file "spheroseg/packages/backend/src/utils/fileOperationsAsync.ts"
check_file "spheroseg/packages/backend/src/middleware/performanceMonitoring.ts"
check_file "spheroseg/packages/backend/src/middleware/enhancedCache.ts"
echo ""

echo "📁 Frontend Optimizations:"
check_file "spheroseg/packages/frontend/src/components/project/ImageDisplayOptimized.tsx"
check_file "spheroseg/packages/frontend/src/components/project/ProjectCardOptimized.tsx"
check_file "spheroseg/packages/frontend/src/components/analytics/AnalyticsDashboardOptimized.tsx"
check_file "spheroseg/packages/frontend/src/components/ui/VirtualList.tsx"
check_file "spheroseg/packages/frontend/src/components/project/VirtualImageGrid.tsx"
check_file "spheroseg/packages/frontend/src/utils/requestDeduplication.ts"
echo ""

echo "📁 Documentation:"
check_file "PERFORMANCE_ANALYSIS.md"
check_file "PERFORMANCE_IMPROVEMENTS_SUMMARY.md"
check_file "TEST_PERFORMANCE_OPTIMIZATIONS.md"
echo ""

# Check if Redis is configured
echo "🔧 Infrastructure Checks:"
if docker-compose ps | grep -q "redis.*Up"; then
    echo -e "${GREEN}✓${NC} Redis container running"
else
    echo -e "${RED}✗${NC} Redis container not running"
fi

# Check package.json for performance test scripts
if grep -q "performance" packages/backend/package.json 2>/dev/null; then
    echo -e "${GREEN}✓${NC} Performance test scripts in backend package.json"
else
    echo -e "${RED}✗${NC} No performance test scripts found"
fi

echo ""
echo "✅ Verification complete!"