# NGINX Configuration with CDN Support
# This configuration is optimized for use with a CDN

# Upstream definitions
upstream backend {
    server backend:5001;
    keepalive 32;
}

upstream frontend {
    server frontend-prod:80;
    keepalive 16;
}

upstream assets {
    server assets:80;
    keepalive 16;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=upload_limit:10m rate=2r/s;

server {
    listen 80;
    server_name _;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # CDN-specific headers
    add_header X-CDN-Origin "true" always;
    
    # Enable gzip but let CDN handle most compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Client body size
    client_max_body_size 200M;
    
    # Timeouts optimized for CDN
    proxy_connect_timeout 10s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # Main app (no caching at origin)
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # No cache for HTML
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        expires -1;
    }
    
    # Static assets with aggressive caching for CDN
    location /assets/ {
        proxy_pass http://assets/assets/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        
        # CDN cache headers
        add_header Cache-Control "public, max-age=31536000, immutable" always;
        add_header X-Content-Type-Options "nosniff" always;
        
        # ETag support
        etag on;
        
        # CORS for CDN
        if ($http_origin ~* (https?://[^/]*\.cloudfront\.net|https?://[^/]*\.cloudflare\.com)) {
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Methods' 'GET, HEAD, OPTIONS' always;
            add_header 'Access-Control-Max-Age' '86400' always;
        }
        
        # Handle OPTIONS requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Methods' 'GET, HEAD, OPTIONS';
            add_header 'Access-Control-Max-Age' '86400';
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' '0';
            return 204;
        }
    }
    
    # Frontend assets (Vite build output) with CDN optimization
    location ~ ^/(js|css|fonts|images)/ {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        
        # Aggressive caching for versioned assets
        add_header Cache-Control "public, max-age=31536000, immutable" always;
        etag on;
        
        # Brotli/Gzip pre-compressed file support
        gzip_static on;
        brotli_static on;
    }
    
    # API routes (no CDN caching)
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Indicate CDN request to backend
        proxy_set_header X-CDN-Request $http_x_cdn_request;
        
        # No caching for API
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header X-Content-Type-Options "nosniff" always;
    }
    
    # GraphQL endpoint
    location /graphql {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # No caching
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }
    
    # Upload endpoint with rate limiting
    location /api/projects/*/images/upload {
        limit_req zone=upload_limit burst=5 nodelay;
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Upload specific settings
        client_body_temp_path /tmp/uploads;
        client_body_in_file_only on;
        client_body_buffer_size 128K;
        client_max_body_size 200M;
        
        # No caching
        add_header Cache-Control "no-cache" always;
    }
    
    # User uploads with CDN-friendly headers
    location /uploads/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-CDN-Request "true";
        
        # CDN cache headers for uploads
        add_header Cache-Control "public, max-age=2592000" always;
        add_header X-Content-Type-Options "nosniff" always;
        
        # ETag support
        etag on;
        
        # CORS for images
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
        
        # Security: Prevent script execution
        add_header Content-Security-Policy "default-src 'none'; img-src 'self'; style-src 'unsafe-inline'" always;
    }
    
    # Health check endpoint for CDN
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
        add_header Cache-Control "no-cache" always;
    }
    
    # CDN origin validation endpoint
    location /.well-known/cdn-origin {
        return 200 '{"origin": "spheroseg", "version": "1.0"}';
        add_header Content-Type application/json;
        add_header Cache-Control "public, max-age=3600" always;
    }
    
    # WebSocket support
    location /socket.io/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific
        proxy_buffering off;
        proxy_cache off;
        
        # No caching for WebSockets
        add_header Cache-Control "no-cache" always;
    }
    
    # Monitoring endpoints (internal only)
    location /nginx-status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow **********/12;  # Docker networks
        deny all;
    }
}

# HTTPS configuration (when using SSL termination at origin)
server {
    listen 443 ssl http2;
    server_name _;
    
    # SSL configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (only if not handled by CDN)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Include all location blocks from above
    include /etc/nginx/conf.d/locations/*.conf;
}