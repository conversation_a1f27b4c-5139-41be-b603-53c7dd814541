#!/bin/bash

# Performance Testing Suite Runner
# Runs integration and E2E tests for performance optimizations

set -e

echo "🧪 Performance Optimization Test Suite"
echo "====================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
PASSED=0
FAILED=0
SKIPPED=0

# Check if services are running
check_services() {
    echo -e "${BLUE}📋 Checking Required Services...${NC}"
    
    # Check Docker services
    if docker-compose ps | grep -q "spheroseg-backend.*Up"; then
        echo -e "${GREEN}✓ Backend service running${NC}"
    else
        echo -e "${RED}✗ Backend service not running${NC}"
        echo "Please start services with: docker-compose --profile dev up -d"
        exit 1
    fi
    
    if docker-compose ps | grep -q "spheroseg-db.*Up"; then
        echo -e "${GREEN}✓ Database service running${NC}"
    else
        echo -e "${RED}✗ Database service not running${NC}"
        exit 1
    fi
    
    if docker-compose ps | grep -q "spheroseg-redis.*Up"; then
        echo -e "${GREEN}✓ Redis service running${NC}"
    else
        echo -e "${YELLOW}⚠ Redis service not running (tests will skip cache tests)${NC}"
    fi
    
    echo ""
}

# Run integration tests
run_integration_tests() {
    echo -e "${BLUE}🔧 Running Integration Tests...${NC}"
    
    cd spheroseg/packages/backend
    
    # Create test database if needed
    docker-compose exec -T db psql -U postgres -c "CREATE DATABASE spheroseg_test;" 2>/dev/null || true
    
    # Run migrations on test database
    echo "Running test database migrations..."
    TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/spheroseg_test \
        docker-compose exec -T backend npm run db:migrate:test 2>/dev/null || true
    
    # Run integration tests
    if npm run test -- src/__tests__/integration/performance.integration.test.ts --coverage; then
        echo -e "${GREEN}✓ Integration tests passed${NC}"
        ((PASSED++))
    else
        echo -e "${RED}✗ Integration tests failed${NC}"
        ((FAILED++))
    fi
    
    cd ../../..
    echo ""
}

# Run E2E tests
run_e2e_tests() {
    echo -e "${BLUE}🌐 Running E2E Tests...${NC}"
    
    # Check if Playwright is installed
    if ! command -v playwright &> /dev/null; then
        echo "Installing Playwright..."
        npm install -D @playwright/test
        npx playwright install chromium
    fi
    
    # Run E2E tests
    if npx playwright test e2e/performance/performance-optimizations.spec.ts \
        --reporter=html \
        --project=chromium; then
        echo -e "${GREEN}✓ E2E tests passed${NC}"
        ((PASSED++))
    else
        echo -e "${RED}✗ E2E tests failed${NC}"
        ((FAILED++))
    fi
    
    echo ""
}

# Run performance benchmarks
run_benchmarks() {
    echo -e "${BLUE}📊 Running Performance Benchmarks...${NC}"
    
    # API Performance Test
    echo "Testing API response times..."
    TOKEN=$(curl -s -X POST http://localhost:5001/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"testuser123"}' | \
        grep -o '"token":"[^"]*' | cut -d'"' -f4)
    
    if [ ! -z "$TOKEN" ]; then
        # Measure user stats endpoint
        TIMES=()
        for i in {1..10}; do
            TIME=$(curl -w "%{time_total}" -o /dev/null -s \
                -H "Authorization: Bearer $TOKEN" \
                http://localhost:5001/api/user/stats)
            TIMES+=($TIME)
        done
        
        # Calculate average
        AVG=$(echo "${TIMES[@]}" | awk '{sum=0; for(i=1;i<=NF;i++)sum+=$i; print sum/NF}')
        AVG_MS=$(echo "$AVG * 1000" | bc)
        
        echo -e "Average API response time: ${AVG_MS}ms"
        
        if (( $(echo "$AVG < 0.1" | bc -l) )); then
            echo -e "${GREEN}✓ API performance meets target (<100ms)${NC}"
            ((PASSED++))
        else
            echo -e "${RED}✗ API performance below target (>100ms)${NC}"
            ((FAILED++))
        fi
    else
        echo -e "${YELLOW}⚠ Could not authenticate for benchmark tests${NC}"
        ((SKIPPED++))
    fi
    
    echo ""
}

# Generate coverage report
generate_coverage_report() {
    echo -e "${BLUE}📈 Generating Coverage Report...${NC}"
    
    # Merge coverage reports if available
    if [ -d "spheroseg/packages/backend/coverage" ]; then
        echo "Backend coverage report available at: spheroseg/packages/backend/coverage/index.html"
    fi
    
    if [ -d "playwright-report" ]; then
        echo "E2E test report available at: playwright-report/index.html"
    fi
    
    echo ""
}

# Performance metrics summary
show_performance_summary() {
    echo -e "${BLUE}📊 Performance Metrics Summary${NC}"
    echo "=============================="
    
    # Get current metrics from API
    METRICS=$(curl -s http://localhost:5001/api/performance/metrics 2>/dev/null || echo "{}")
    
    if [ "$METRICS" != "{}" ]; then
        echo "$METRICS" | jq -r '
            "API Performance:",
            "  Total Endpoints: \(.api.totalEndpoints)",
            "  Total Calls: \(.api.totalCalls)",
            "  Avg Response Time: \(.api.avgResponseTime)ms",
            "",
            "Database Performance:",
            "  Total Queries: \(.database.totalQueries)",
            "  Avg Query Time: \(.database.avgQueryTime)ms",
            "",
            "Memory Usage:",
            "  Heap Used: \(.memory.current.heapUsed / 1024 / 1024 | floor)MB",
            "  Heap Total: \(.memory.current.heapTotal / 1024 / 1024 | floor)MB",
            "  Usage: \(.memory.current.heapUsagePercent | floor)%"
        ' 2>/dev/null || echo "Could not parse metrics"
    else
        echo "Performance metrics not available"
    fi
    
    echo ""
}

# Main execution
main() {
    echo "Starting performance test suite..."
    START_TIME=$(date +%s)
    
    # Check services
    check_services
    
    # Run tests based on arguments
    if [[ "$*" == *"--integration"* ]]; then
        run_integration_tests
    fi
    
    if [[ "$*" == *"--e2e"* ]]; then
        run_e2e_tests
    fi
    
    if [[ "$*" == *"--benchmark"* ]] || [[ "$*" == *"--pup"* ]]; then
        run_benchmarks
    fi
    
    # If no specific tests requested, run all
    if [ $# -eq 0 ]; then
        run_integration_tests
        run_e2e_tests
        run_benchmarks
    fi
    
    # Generate reports
    generate_coverage_report
    
    # Show performance summary
    show_performance_summary
    
    # Calculate total time
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    # Final summary
    echo -e "${BLUE}📋 Test Summary${NC}"
    echo "==============="
    echo -e "Tests Passed: ${GREEN}$PASSED${NC}"
    echo -e "Tests Failed: ${RED}$FAILED${NC}"
    echo -e "Tests Skipped: ${YELLOW}$SKIPPED${NC}"
    echo -e "Total Duration: ${DURATION}s"
    echo ""
    
    # Exit code
    if [ $FAILED -eq 0 ]; then
        echo -e "${GREEN}✅ All performance tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed. Please check the logs.${NC}"
        exit 1
    fi
}

# Run main with all arguments
main "$@"