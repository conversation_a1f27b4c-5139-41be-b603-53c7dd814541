/**
 * E2E tests for performance optimizations
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testuser123'
};

const PERFORMANCE_THRESHOLDS = {
  pageLoad: 3000, // 3 seconds
  apiResponse: 1000, // 1 second
  imageGridRender: 2000, // 2 seconds
  virtualScrollFPS: 30 // 30 FPS minimum
};

test.describe('Performance Optimizations E2E Tests', () => {
  let page: Page;

  test.beforeEach(async ({ page: p }) => {
    page = p;
    
    // Enable performance monitoring
    await page.evaluateOnNewDocument(() => {
      window.__PERF_METRICS = {
        apiCalls: [],
        renderTimes: [],
        memoryUsage: []
      };

      // Override fetch to track API performance
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const startTime = performance.now();
        try {
          const response = await originalFetch(...args);
          const duration = performance.now() - startTime;
          window.__PERF_METRICS.apiCalls.push({
            url: args[0],
            duration,
            status: response.status
          });
          return response;
        } catch (error) {
          const duration = performance.now() - startTime;
          window.__PERF_METRICS.apiCalls.push({
            url: args[0],
            duration,
            error: true
          });
          throw error;
        }
      };

      // Track memory usage
      if (performance.memory) {
        setInterval(() => {
          window.__PERF_METRICS.memoryUsage.push({
            timestamp: Date.now(),
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize
          });
        }, 1000);
      }
    });

    // Login
    await page.goto('/login');
    await page.fill('input[name="email"]', TEST_USER.email);
    await page.fill('input[name="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test.describe('Page Load Performance', () => {
    test('should load dashboard within performance threshold', async () => {
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      await page.waitForSelector('[data-testid="user-stats"]', { state: 'visible' });
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad);
      
      // Check that optimized API calls were made
      const metrics = await page.evaluate(() => window.__PERF_METRICS);
      const statsCall = metrics.apiCalls.find(call => 
        call.url.includes('/api/user') && call.url.includes('/stats')
      );
      
      expect(statsCall).toBeDefined();
      expect(statsCall.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.apiResponse);
    });

    test('should cache user stats on subsequent loads', async () => {
      // First load
      await page.goto('/dashboard');
      await page.waitForSelector('[data-testid="user-stats"]');
      
      const firstMetrics = await page.evaluate(() => window.__PERF_METRICS);
      const firstStatsCall = firstMetrics.apiCalls.find(call => 
        call.url.includes('/api/user') && call.url.includes('/stats')
      );
      
      // Navigate away and back
      await page.goto('/projects');
      await page.goto('/dashboard');
      await page.waitForSelector('[data-testid="user-stats"]');
      
      const secondMetrics = await page.evaluate(() => window.__PERF_METRICS);
      const cachedStatsCall = secondMetrics.apiCalls.filter(call => 
        call.url.includes('/api/user') && call.url.includes('/stats')
      ).pop();
      
      // Second call should be faster (cached)
      expect(cachedStatsCall.duration).toBeLessThan(firstStatsCall.duration * 0.5);
    });
  });

  test.describe('Virtual Scrolling Performance', () => {
    test('should handle large image grids efficiently', async () => {
      // Navigate to a project with many images
      await page.goto('/projects');
      await page.click('[data-testid="project-card"]:first-child');
      await page.waitForSelector('[data-testid="image-grid"]');
      
      // Check initial DOM node count
      const initialNodes = await page.evaluate(() => 
        document.querySelectorAll('[data-testid="image-card"]').length
      );
      
      // Should only render visible items (virtual scrolling)
      expect(initialNodes).toBeLessThan(50); // Assuming viewport fits < 50 items
      
      // Scroll down
      await page.evaluate(() => window.scrollBy(0, 5000));
      await page.waitForTimeout(500); // Wait for virtual scroll update
      
      // Check DOM nodes again
      const scrolledNodes = await page.evaluate(() => 
        document.querySelectorAll('[data-testid="image-card"]').length
      );
      
      // Should still be limited (not rendering all items)
      expect(scrolledNodes).toBeLessThan(100);
      
      // Measure scroll performance
      const scrollPerformance = await page.evaluate(async () => {
        const measurements = [];
        let lastTime = performance.now();
        
        return new Promise(resolve => {
          let frames = 0;
          const measureFrame = () => {
            const currentTime = performance.now();
            const delta = currentTime - lastTime;
            measurements.push(delta);
            lastTime = currentTime;
            frames++;
            
            if (frames < 60) { // Measure 60 frames
              requestAnimationFrame(measureFrame);
            } else {
              const avgFrameTime = measurements.reduce((a, b) => a + b) / measurements.length;
              const fps = 1000 / avgFrameTime;
              resolve(fps);
            }
          };
          
          // Trigger smooth scroll
          window.scrollTo({ top: 10000, behavior: 'smooth' });
          requestAnimationFrame(measureFrame);
        });
      });
      
      // Should maintain smooth scrolling (30+ FPS)
      expect(scrollPerformance).toBeGreaterThan(PERFORMANCE_THRESHOLDS.virtualScrollFPS);
    });

    test('should not leak memory with repeated scrolling', async () => {
      await page.goto('/projects');
      await page.click('[data-testid="project-card"]:first-child');
      await page.waitForSelector('[data-testid="image-grid"]');
      
      // Get initial memory usage
      const initialMemory = await page.evaluate(() => {
        if (performance.memory) {
          return performance.memory.usedJSHeapSize;
        }
        return 0;
      });
      
      // Perform aggressive scrolling
      for (let i = 0; i < 10; i++) {
        await page.evaluate(() => window.scrollTo(0, 10000));
        await page.waitForTimeout(100);
        await page.evaluate(() => window.scrollTo(0, 0));
        await page.waitForTimeout(100);
      }
      
      // Force garbage collection if available
      await page.evaluate(() => {
        if (window.gc) window.gc();
      });
      
      await page.waitForTimeout(1000); // Wait for cleanup
      
      // Check final memory usage
      const finalMemory = await page.evaluate(() => {
        if (performance.memory) {
          return performance.memory.usedJSHeapSize;
        }
        return 0;
      });
      
      // Memory increase should be minimal (< 50MB)
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024;
      expect(memoryIncrease).toBeLessThan(50);
    });
  });

  test.describe('Request Deduplication', () => {
    test('should prevent duplicate API calls', async () => {
      await page.goto('/projects');
      
      // Clear metrics
      await page.evaluate(() => {
        window.__PERF_METRICS.apiCalls = [];
      });
      
      // Trigger multiple simultaneous requests
      await Promise.all([
        page.click('[data-testid="refresh-button"]'),
        page.click('[data-testid="refresh-button"]'),
        page.click('[data-testid="refresh-button"]')
      ]);
      
      await page.waitForTimeout(1000); // Wait for requests
      
      const metrics = await page.evaluate(() => window.__PERF_METRICS);
      const projectCalls = metrics.apiCalls.filter(call => 
        call.url.includes('/api/projects')
      );
      
      // Should only make one actual request
      expect(projectCalls.length).toBe(1);
    });

    test('should handle 429 rate limit errors gracefully', async () => {
      // Mock 429 response
      await page.route('**/api/projects', (route) => {
        route.fulfill({
          status: 429,
          headers: {
            'Retry-After': '60'
          },
          body: JSON.stringify({ error: 'Rate limit exceeded' })
        });
      });
      
      await page.goto('/projects');
      
      // Should show error message
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Rate limit');
      
      // Check that no duplicate requests were made
      const metrics = await page.evaluate(() => window.__PERF_METRICS);
      const failedCalls = metrics.apiCalls.filter(call => 
        call.url.includes('/api/projects') && call.status === 429
      );
      
      expect(failedCalls.length).toBe(1);
    });
  });

  test.describe('Performance Monitoring', () => {
    test('should track and display performance metrics', async () => {
      // Navigate to performance dashboard (if available)
      await page.goto('/admin/performance');
      
      // Check that metrics are displayed
      await expect(page.locator('[data-testid="api-metrics"]')).toBeVisible();
      await expect(page.locator('[data-testid="memory-metrics"]')).toBeVisible();
      await expect(page.locator('[data-testid="database-metrics"]')).toBeVisible();
      
      // Verify metrics are updating
      const initialApiCount = await page.locator('[data-testid="total-api-calls"]').textContent();
      
      // Make some API calls
      await page.goto('/projects');
      await page.goto('/admin/performance');
      
      const updatedApiCount = await page.locator('[data-testid="total-api-calls"]').textContent();
      expect(parseInt(updatedApiCount)).toBeGreaterThan(parseInt(initialApiCount));
    });
  });

  test.describe('Memory Pressure Handling', () => {
    test('should handle memory pressure gracefully', async () => {
      // This test simulates memory pressure
      await page.goto('/projects');
      
      // Allocate large amounts of memory
      await page.evaluate(() => {
        window.__MEMORY_TEST = [];
        try {
          // Allocate ~100MB of memory
          for (let i = 0; i < 100; i++) {
            window.__MEMORY_TEST.push(new Array(1024 * 1024).fill('x'));
          }
        } catch (e) {
          // Memory allocation failed - that's OK for this test
        }
      });
      
      // App should still be responsive
      await page.click('[data-testid="project-card"]:first-child');
      await expect(page).toHaveURL(/\/projects\/\d+/);
      
      // Clean up
      await page.evaluate(() => {
        window.__MEMORY_TEST = null;
        if (window.gc) window.gc();
      });
    });
  });

  test.afterEach(async () => {
    // Collect final metrics
    const finalMetrics = await page.evaluate(() => window.__PERF_METRICS);
    
    // Log performance summary
    console.log('Performance Summary:', {
      totalApiCalls: finalMetrics.apiCalls.length,
      avgApiDuration: finalMetrics.apiCalls.reduce((sum, call) => sum + call.duration, 0) / finalMetrics.apiCalls.length,
      memoryUsage: finalMetrics.memoryUsage.length > 0 ? 
        finalMetrics.memoryUsage[finalMetrics.memoryUsage.length - 1].usedJSHeapSize / 1024 / 1024 + 'MB' : 
        'N/A'
    });
  });
});