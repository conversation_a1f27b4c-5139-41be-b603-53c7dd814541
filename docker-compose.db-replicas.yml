# PostgreSQL Read Replicas Configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.db-replicas.yml up
version: '3.8'

services:
  # Primary database (master) - extends the main db service
  db:
    environment:
      - POSTGRES_REPLICATION_MODE=master
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=replicator_password
      - POSTGRES_MAX_WAL_SENDERS=4
      - POSTGRES_WAL_LEVEL=replica
      - POSTGRES_SYNCHRONOUS_COMMIT=on
      - POSTGRES_SYNCHRONOUS_STANDBY_NAMES='replica1,replica2'
    command: |
      postgres 
      -c max_wal_senders=4
      -c wal_level=replica
      -c wal_log_hints=on
      -c max_replication_slots=4
      -c hot_standby=on
      -c shared_preload_libraries='pg_stat_statements'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/postgres/init-replication.sh:/docker-entrypoint-initdb.d/init-replication.sh:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres && psql -U postgres -c 'SELECT 1'"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Read replica 1
  db-replica1:
    image: postgres:14-alpine
    container_name: spheroseg_db_replica1
    environment:
      - POSTGRES_REPLICATION_MODE=slave
      - POSTGRES_MASTER_SERVICE=db
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=replicator_password
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=spheroseg
    volumes:
      - postgres_replica1_data:/var/lib/postgresql/data
      - ./scripts/postgres/setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh:ro
    depends_on:
      db:
        condition: service_healthy
    networks:
      - spheroseg-network
    ports:
      - "5433:5432"
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Read replica 2
  db-replica2:
    image: postgres:14-alpine
    container_name: spheroseg_db_replica2
    environment:
      - POSTGRES_REPLICATION_MODE=slave
      - POSTGRES_MASTER_SERVICE=db
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=replicator_password
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=spheroseg
    volumes:
      - postgres_replica2_data:/var/lib/postgresql/data
      - ./scripts/postgres/setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh:ro
    depends_on:
      db:
        condition: service_healthy
    networks:
      - spheroseg-network
    ports:
      - "5434:5432"
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PgBouncer for connection pooling and read/write splitting
  pgbouncer:
    image: edoburu/pgbouncer:1.19.0
    container_name: spheroseg_pgbouncer
    environment:
      - DATABASES_HOST=db
      - DATABASES_PORT=5432
      - DATABASES_USER=postgres
      - DATABASES_PASSWORD=postgres
      - DATABASES_DBNAME=spheroseg
      - POOL_MODE=transaction
      - MAX_CLIENT_CONN=1000
      - DEFAULT_POOL_SIZE=25
      - MIN_POOL_SIZE=5
      - RESERVE_POOL_SIZE=5
      - RESERVE_POOL_TIMEOUT=3
      - SERVER_IDLE_TIMEOUT=600
      - SERVER_LIFETIME=3600
      - SERVER_CONNECT_TIMEOUT=15
      - QUERY_TIMEOUT=0
      - QUERY_WAIT_TIMEOUT=120
      - CLIENT_IDLE_TIMEOUT=0
      - CLIENT_LOGIN_TIMEOUT=60
      - AUTODB_IDLE_TIMEOUT=3600
      - DNS_MAX_TTL=15
      - DNS_NXDOMAIN_TTL=15
    volumes:
      - ./pgbouncer:/etc/pgbouncer:ro
    depends_on:
      - db
      - db-replica1
      - db-replica2
    networks:
      - spheroseg-network
    ports:
      - "6432:6432"  # PgBouncer port
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -h localhost -p 6432"]
      interval: 10s
      timeout: 5s
      retries: 3

  # HAProxy for database load balancing
  db-load-balancer:
    image: haproxy:2.8-alpine
    container_name: spheroseg_db_lb
    volumes:
      - ./haproxy/haproxy-db.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    depends_on:
      - db
      - db-replica1
      - db-replica2
    networks:
      - spheroseg-network
    ports:
      - "5435:5432"  # Read-only port
      - "5436:5433"  # Read-write port
      - "8405:8404"  # Stats page
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    healthcheck:
      test: ["CMD", "haproxy", "-c", "-f", "/usr/local/etc/haproxy/haproxy.cfg"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Postgres Exporter for Prometheus monitoring
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: spheroseg_postgres_exporter
    environment:
      - DATA_SOURCE_NAME=**************************************/spheroseg?sslmode=disable
      - PG_EXPORTER_DISABLE_DEFAULT_METRICS=false
      - PG_EXPORTER_DISABLE_SETTINGS_METRICS=false
      - PG_EXPORTER_EXTEND_QUERY_PATH=/etc/postgres_exporter/queries.yaml
    volumes:
      - ./prometheus/postgres_exporter_queries.yaml:/etc/postgres_exporter/queries.yaml:ro
    depends_on:
      - db
    networks:
      - spheroseg-network
    ports:
      - "9187:9187"
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # Update backend service to use PgBouncer
  backend:
    environment:
      - DATABASE_URL=*********************************************/spheroseg
      - DATABASE_READ_URL=****************************************************/spheroseg
      - DATABASE_WRITE_URL=****************************************************/spheroseg
      - ENABLE_READ_REPLICAS=true
      - DB_POOL_SIZE=20
      - DB_POOL_IDLE_TIMEOUT=10000
      - DB_POOL_CONNECTION_TIMEOUT=2000

volumes:
  postgres_replica1_data:
    driver: local
  postgres_replica2_data:
    driver: local

networks:
  spheroseg-network:
    driver: bridge