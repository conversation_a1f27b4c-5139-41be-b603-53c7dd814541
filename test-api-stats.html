<!DOCTYPE html>
<html>
<head>
    <title>Test API Stats</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
        button { padding: 10px 20px; margin: 10px 0; cursor: pointer; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Test User Statistics API</h1>
    
    <div>
        <label>API URL: </label>
        <input type="text" id="apiUrl" value="https://spherosegapp.utia.cas.cz" style="width: 300px;">
    </div>
    
    <div>
        <label>Email: </label>
        <input type="text" id="email" value="<EMAIL>" style="width: 300px;">
    </div>
    
    <div>
        <label>Password: </label>
        <input type="password" id="password" value="" style="width: 300px;">
    </div>
    
    <button onclick="testLogin()">1. Login</button>
    <button onclick="testStats()">2. Test /api/user-stats/me/statistics</button>
    <button onclick="testDebug()">3. Test /api/debug/stats</button>
    
    <h2>Results:</h2>
    <pre id="output"></pre>
    
    <script>
        let accessToken = '';
        
        function output(message, isError = false) {
            const outputEl = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = isError ? 'error' : 'success';
            outputEl.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }
        
        async function testLogin() {
            const apiUrl = document.getElementById('apiUrl').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!password) {
                output('Please enter password', true);
                return;
            }
            
            try {
                output('Logging in...');
                const response = await fetch(`${apiUrl}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.accessToken;
                    output('Login successful! Token: ' + accessToken.substring(0, 20) + '...');
                    output('User ID: ' + data.user.id);
                } else {
                    output('Login failed: ' + JSON.stringify(data), true);
                }
            } catch (error) {
                output('Login error: ' + error.message, true);
            }
        }
        
        async function testStats() {
            if (!accessToken) {
                output('Please login first', true);
                return;
            }
            
            const apiUrl = document.getElementById('apiUrl').value;
            
            try {
                output('\nFetching /api/user-stats/me/statistics...');
                const response = await fetch(`${apiUrl}/api/user-stats/me/statistics`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                    },
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    output('Statistics received:');
                    output(JSON.stringify(data, null, 2));
                    
                    // Extract key values
                    output('\nKey Values:');
                    output('Total Projects: ' + data.totalProjects);
                    output('Total Images: ' + data.totalImages);
                    output('Completed Segmentations: ' + data.completedSegmentations);
                    output('Storage Used MB: ' + data.storageUsedMB);
                } else {
                    output('Stats failed: ' + JSON.stringify(data), true);
                }
            } catch (error) {
                output('Stats error: ' + error.message, true);
            }
        }
        
        async function testDebug() {
            if (!accessToken) {
                output('Please login first', true);
                return;
            }
            
            const apiUrl = document.getElementById('apiUrl').value;
            
            try {
                output('\nFetching /api/debug/stats...');
                const response = await fetch(`${apiUrl}/api/debug/stats`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                    },
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    output('Debug stats received:');
                    output(JSON.stringify(data, null, 2));
                } else {
                    output('Debug stats failed: ' + JSON.stringify(data), true);
                }
            } catch (error) {
                output('Debug stats error: ' + error.message, true);
            }
        }
    </script>
</body>
</html>