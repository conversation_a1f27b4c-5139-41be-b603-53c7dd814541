#!/bin/bash

# ML Service Scaling Management Script
# This script helps manage the horizontally scaled ML service deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILES="-f docker-compose.yml -f docker-compose.scaling.yml"
DEFAULT_ML_INSTANCES=3
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Functions
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    print_success "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    print_success "Docker Compose is installed"
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    print_success "Docker daemon is running"
    
    # Check for required files
    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        print_error "docker-compose.yml not found"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_ROOT/docker-compose.scaling.yml" ]; then
        print_error "docker-compose.scaling.yml not found"
        exit 1
    fi
    print_success "Required files found"
    
    # Check if ML model exists
    if [ ! -f "$PROJECT_ROOT/spheroseg/packages/ml/checkpoint_epoch_9.pth.tar" ]; then
        print_warning "ML model checkpoint not found. ML service will fail to process tasks."
    fi
}

deploy_scaled() {
    local instances=${1:-$DEFAULT_ML_INSTANCES}
    print_header "Deploying ML Service with $instances instances"
    
    # Copy scaling environment file if .env doesn't exist
    if [ ! -f "$PROJECT_ROOT/spheroseg/packages/backend/.env" ]; then
        print_warning "Backend .env not found, copying from .env.scaling"
        cp "$PROJECT_ROOT/spheroseg/packages/backend/.env.scaling" "$PROJECT_ROOT/spheroseg/packages/backend/.env"
    fi
    
    # Start services
    cd "$PROJECT_ROOT"
    print_success "Starting services..."
    docker-compose $COMPOSE_FILES --profile prod up -d
    
    # Wait for services to be ready
    print_success "Waiting for services to initialize..."
    sleep 10
    
    # Scale ML service
    print_success "Scaling ML service to $instances instances..."
    docker-compose $COMPOSE_FILES up -d --scale ml=$instances
    
    # Verify deployment
    verify_deployment
}

stop_services() {
    print_header "Stopping Services"
    cd "$PROJECT_ROOT"
    docker-compose $COMPOSE_FILES down
    print_success "Services stopped"
}

scale_ml_service() {
    local instances=$1
    if [ -z "$instances" ]; then
        print_error "Please specify number of instances"
        echo "Usage: $0 scale <number>"
        exit 1
    fi
    
    print_header "Scaling ML Service to $instances instances"
    cd "$PROJECT_ROOT"
    docker-compose $COMPOSE_FILES up -d --scale ml=$instances --no-recreate
    print_success "ML service scaled to $instances instances"
    
    # Show status
    sleep 5
    show_status
}

show_status() {
    print_header "Service Status"
    
    # Show running containers
    echo -e "\n${BLUE}Running Containers:${NC}"
    docker-compose $COMPOSE_FILES ps
    
    # Check ML health through load balancer
    echo -e "\n${BLUE}ML Service Health (via Load Balancer):${NC}"
    if curl -s http://localhost:5003/health > /dev/null 2>&1; then
        health_data=$(curl -s http://localhost:5003/health | python3 -m json.tool 2>/dev/null || echo "Invalid JSON response")
        echo "$health_data"
    else
        print_error "ML load balancer not responding"
    fi
    
    # Show HAProxy stats summary
    echo -e "\n${BLUE}HAProxy Statistics:${NC}"
    if curl -s -u admin:spheroseg123 http://localhost:8404/stats > /dev/null 2>&1; then
        print_success "HAProxy stats available at http://localhost:8404/stats"
        echo "Username: admin, Password: spheroseg123"
    else
        print_warning "HAProxy stats not available"
    fi
    
    # Show RabbitMQ queue status
    echo -e "\n${BLUE}RabbitMQ Queue Status:${NC}"
    if docker exec spheroseg_rabbitmq rabbitmqctl list_queues 2>/dev/null; then
        docker exec spheroseg_rabbitmq rabbitmqctl list_queues
    else
        print_warning "Could not get RabbitMQ status"
    fi
}

verify_deployment() {
    print_header "Verifying Deployment"
    
    local all_good=true
    
    # Check if ML load balancer is responding
    if curl -s http://localhost:5003/health > /dev/null 2>&1; then
        print_success "ML load balancer is responding"
    else
        print_error "ML load balancer is not responding"
        all_good=false
    fi
    
    # Check if backend is healthy
    if curl -s http://localhost:5001/health > /dev/null 2>&1; then
        print_success "Backend service is healthy"
    else
        print_error "Backend service is not healthy"
        all_good=false
    fi
    
    # Check if RabbitMQ is running
    if docker exec spheroseg_rabbitmq rabbitmqctl status > /dev/null 2>&1; then
        print_success "RabbitMQ is running"
    else
        print_error "RabbitMQ is not running"
        all_good=false
    fi
    
    # Check ML instances
    ml_count=$(docker-compose $COMPOSE_FILES ps ml | grep -c "Up" || true)
    if [ "$ml_count" -gt 0 ]; then
        print_success "$ml_count ML instance(s) running"
    else
        print_error "No ML instances running"
        all_good=false
    fi
    
    if [ "$all_good" = true ]; then
        echo -e "\n${GREEN}✓ Deployment verified successfully!${NC}"
    else
        echo -e "\n${RED}✗ Deployment verification failed!${NC}"
        exit 1
    fi
}

run_load_test() {
    print_header "Running Load Test"
    
    # Check if test script exists
    if [ ! -f "$PROJECT_ROOT/test-ml-scaling.py" ]; then
        print_error "test-ml-scaling.py not found"
        exit 1
    fi
    
    # Make sure services are running
    if ! curl -s http://localhost:5003/health > /dev/null 2>&1; then
        print_error "Services not running. Please deploy first."
        exit 1
    fi
    
    # Run the test
    cd "$PROJECT_ROOT"
    python3 test-ml-scaling.py
}

monitor_logs() {
    print_header "Monitoring Logs"
    echo "Press Ctrl+C to stop monitoring"
    cd "$PROJECT_ROOT"
    docker-compose $COMPOSE_FILES logs -f ml ml-load-balancer backend
}

restart_ml_instances() {
    print_header "Restarting ML Instances"
    cd "$PROJECT_ROOT"
    
    # Get current scale
    current_scale=$(docker-compose $COMPOSE_FILES ps ml | grep -c "Up" || echo "0")
    
    if [ "$current_scale" -eq 0 ]; then
        print_error "No ML instances running"
        exit 1
    fi
    
    print_success "Restarting $current_scale ML instances..."
    docker-compose $COMPOSE_FILES restart ml
    print_success "ML instances restarted"
}

show_metrics() {
    print_header "Prometheus Metrics"
    
    # Check if Prometheus is running
    if ! curl -s http://localhost:9090 > /dev/null 2>&1; then
        print_warning "Prometheus not running. Starting monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose $COMPOSE_FILES --profile monitoring up -d
        sleep 10
    fi
    
    echo -e "\n${BLUE}Key Metrics:${NC}"
    
    # Query some key metrics
    metrics=(
        "ml_tasks_processed_total"
        "ml_active_tasks"
        "ml_queue_size"
        "up{job='ml-service'}"
    )
    
    for metric in "${metrics[@]}"; do
        echo -e "\n${YELLOW}$metric:${NC}"
        result=$(curl -s "http://localhost:9090/api/v1/query?query=$metric" 2>/dev/null | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data['status'] == 'success' and data['data']['result']:
        for r in data['data']['result']:
            metric = r.get('metric', {})
            value = r.get('value', ['', 'N/A'])[1]
            instance = metric.get('instance', 'aggregate')
            print(f'  {instance}: {value}')
    else:
        print('  No data available')
except:
    print('  Error querying metric')
" 2>/dev/null || echo "  Error querying metric")
        echo "$result"
    done
    
    echo -e "\n${GREEN}Prometheus UI available at: http://localhost:9090${NC}"
}

# Main command handling
case "${1:-help}" in
    check)
        check_prerequisites
        ;;
    deploy)
        check_prerequisites
        deploy_scaled "$2"
        ;;
    stop)
        stop_services
        ;;
    scale)
        scale_ml_service "$2"
        ;;
    status)
        show_status
        ;;
    verify)
        verify_deployment
        ;;
    test)
        run_load_test
        ;;
    logs)
        monitor_logs
        ;;
    restart)
        restart_ml_instances
        ;;
    metrics)
        show_metrics
        ;;
    help|*)
        echo "ML Service Scaling Management Script"
        echo ""
        echo "Usage: $0 <command> [options]"
        echo ""
        echo "Commands:"
        echo "  check              Check prerequisites"
        echo "  deploy [n]         Deploy with n ML instances (default: 3)"
        echo "  stop               Stop all services"
        echo "  scale <n>          Scale ML service to n instances"
        echo "  status             Show current status"
        echo "  verify             Verify deployment health"
        echo "  test               Run load test"
        echo "  logs               Monitor service logs"
        echo "  restart            Restart ML instances"
        echo "  metrics            Show Prometheus metrics"
        echo "  help               Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 deploy          # Deploy with 3 ML instances"
        echo "  $0 deploy 5        # Deploy with 5 ML instances"
        echo "  $0 scale 10        # Scale to 10 ML instances"
        echo "  $0 status          # Check current status"
        ;;
esac