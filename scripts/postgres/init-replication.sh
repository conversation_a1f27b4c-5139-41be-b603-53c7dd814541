#!/bin/bash
# Initialize PostgreSQL master for replication

set -e

# Create replication user if it doesn't exist
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create replication user
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'replicator') THEN
            CREATE USER replicator WITH REPLICATION ENCRYPTED PASSWORD 'replicator_password';
        END IF;
    END
    \$\$;

    -- Grant replication privileges
    GRANT CONNECT ON DATABASE spheroseg TO replicator;
    
    -- Create replication slot for each replica
    SELECT pg_create_physical_replication_slot('replica1_slot') 
    WHERE NOT EXISTS (
        SELECT 1 FROM pg_replication_slots WHERE slot_name = 'replica1_slot'
    );
    
    SELECT pg_create_physical_replication_slot('replica2_slot')
    WHERE NOT EXISTS (
        SELECT 1 FROM pg_replication_slots WHERE slot_name = 'replica2_slot'
    );

    -- Configure synchronous replication
    ALTER SYSTEM SET synchronous_standby_names = 'replica1,replica2';
    
    -- Performance and monitoring settings
    ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
    ALTER SYSTEM SET pg_stat_statements.track = 'all';
    ALTER SYSTEM SET pg_stat_statements.max = 10000;
    
    -- Logging for replication monitoring
    ALTER SYSTEM SET log_replication_commands = on;
    ALTER SYSTEM SET log_connections = on;
    ALTER SYSTEM SET log_disconnections = on;
    
    -- Create monitoring user for Prometheus
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'postgres_exporter') THEN
            CREATE USER postgres_exporter WITH PASSWORD 'exporter_password';
        END IF;
    END
    \$\$;
    
    GRANT CONNECT ON DATABASE spheroseg TO postgres_exporter;
    GRANT pg_monitor TO postgres_exporter;
    
    -- Create extension for monitoring
    CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
EOSQL

# Update pg_hba.conf for replication
echo "host    replication     replicator      0.0.0.0/0               md5" >> "$PGDATA/pg_hba.conf"
echo "host    all             postgres_exporter    0.0.0.0/0          md5" >> "$PGDATA/pg_hba.conf"

# Reload configuration
pg_ctl reload -D "$PGDATA"

echo "PostgreSQL master initialized for replication"