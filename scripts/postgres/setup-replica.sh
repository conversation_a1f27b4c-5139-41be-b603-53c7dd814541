#!/bin/bash
# Setup PostgreSQL replica

set -e

# Wait for master to be ready
until PGPASSWORD=$POSTGRES_PASSWORD psql -h "$POSTGRES_MASTER_SERVICE" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c '\q' 2>/dev/null; do
    echo "Waiting for master database to be ready..."
    sleep 2
done

# Stop PostgreSQL to prepare for replica setup
su-exec postgres pg_ctl -D "$PGDATA" -m fast -w stop 2>/dev/null || true

# Clean data directory
rm -rf "$PGDATA"/*

# Perform base backup from master
export PGPASSWORD=$POSTGRES_REPLICATION_PASSWORD
pg_basebackup \
    -h "$POSTGRES_MASTER_SERVICE" \
    -D "$PGDATA" \
    -U "$POSTGRES_REPLICATION_USER" \
    -Fp \
    -Xs \
    -P \
    -R \
    -S "replica${HOSTNAME: -1}_slot" \
    -C \
    --checkpoint=fast

# Create standby signal file
touch "$PGDATA/standby.signal"

# Configure replica settings
cat >> "$PGDATA/postgresql.auto.conf" <<EOF
# Replica configuration
primary_conninfo = 'host=$POSTGRES_MASTER_SERVICE port=5432 user=$POSTGRES_REPLICATION_USER password=$POSTGRES_REPLICATION_PASSWORD application_name=replica${HOSTNAME: -1}'
primary_slot_name = 'replica${HOSTNAME: -1}_slot'
hot_standby = on
hot_standby_feedback = on
max_standby_archive_delay = 30s
max_standby_streaming_delay = 30s
wal_receiver_status_interval = 10s
wal_receiver_timeout = 60s
wal_retrieve_retry_interval = 5s

# Performance settings for read queries
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
max_connections = 200
EOF

# Set permissions
chown -R postgres:postgres "$PGDATA"
chmod 700 "$PGDATA"

# Start PostgreSQL
su-exec postgres pg_ctl -D "$PGDATA" -w start

echo "PostgreSQL replica setup completed"